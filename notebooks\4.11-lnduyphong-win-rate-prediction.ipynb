import copy
import json
import os
import sys
import warnings

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from tqdm import tqdm

warnings.filterwarnings("ignore")
sys.path.append(os.path.abspath(".."))
%matplotlib inline


from src.game.game_state_base import GameStateBase

def save_solved_level(game, path):
    saved_game = copy.deepcopy(game)
    iter = 0
    saved_game.visualize_state(saved_image=True, index_image=iter)
    iter += 1
    for t in tqdm(path):
        if t[0] == 'eat':
            saved_game.eat_block(saved_game.find_block(t[1]))
        else:
            saved_game.move_block(saved_game.find_block(t[0]), t[1], t[2])
        saved_game.visualize_state(saved_image=True, index_image=iter)
        iter += 1

log_path = "../notebooks/Logs2"
# Đọc toàn bộ file log
records = []
for file in tqdm(os.listdir(log_path)):
    with open(os.path.join(log_path, file), "r") as f:
        data = json.load(f)
        records.append(data)

# <PERSON><PERSON><PERSON><PERSON> sang DataFrame để dễ xử lý
df = pd.DataFrame(records)

# Thống kê cơ bản
total_files = len(df)
solved = (df['status'] == 'FOUND').sum()
unsolved = total_files - solved
mean_time = df.loc[df['status']=="FOUND", "runtime_sec"].mean()
std_time = df.loc[df['status']=="FOUND", "runtime_sec"].std()

print(f"Tổng số file: {total_files}")
print(f"Đã giải: {solved} ({solved/total_files:.2%})")
print(f"Chưa giải: {unsolved} ({unsolved/total_files:.2%})")
print(f"Thời gian trung bình: {mean_time:.3f} giây ± {std_time:.3f}")

# --- Vẽ biểu đồ ---

# 1. Pie chart solved vs unsolved
plt.figure(figsize=(5,5))
plt.pie([solved, unsolved], labels=["Solved", "Unsolved"], autopct='%1.1f%%', colors=["#66c2a5","#fc8d62"])
plt.title("Tỉ lệ solved / unsolved")
plt.show()

# 2. Histogram thời gian giải
plt.figure(figsize=(10,6))
sns.histplot(df.loc[df['status']=="FOUND", "runtime_sec"], bins=100, kde=True, color="#4daf4a")
plt.xlabel("Runtime (giây)")
plt.ylabel("Số level")
plt.title("Phân phối thời gian giải (chỉ những level solved)")
plt.show()

# 2. Histogram số lượng iteration cần để giải
plt.figure(figsize=(10,6))
sns.histplot(df.loc[df['status']=="FOUND", "iteration"], bins=100, kde=True, color="#4daf4a")
plt.xlabel("Iteration")
plt.ylabel("Số level")
plt.title("Phân phối số lượng iteration cần để giải (chỉ những level solved)")
plt.show()

# 3. Scatterplot Steps vs Runtime
plt.figure(figsize=(8,6))
sns.scatterplot(data=df[df['status']=="FOUND"], x="steps", y="runtime_sec", hue="shuffle_count", palette="viridis")
plt.xlabel("Số bước (steps)")
plt.ylabel("Runtime (giây)")
plt.title("Tương quan giữa steps và runtime")
plt.legend(title="Số lần shuffle")
plt.show()

# 4. Boxplot runtime theo số lần shuffle
plt.figure(figsize=(8,6))
sns.boxplot(data=df[df['status']=="FOUND"], x="shuffle_count", y="runtime_sec")
plt.xlabel("Số lần shuffle")
plt.ylabel("Runtime (giây)")
plt.title("Ảnh hưởng của shuffle đến runtime")
plt.show()

df[df['status'] != 'FOUND']

founded_levels = df[df['status'] == 'FOUND']

avg_digest_block_move = []
std_digest_block_move = []

for idx, row in tqdm(founded_levels.iterrows(), total=founded_levels.shape[0]):
    path = row['path']
    temp = []
    count = 0
    for idx, t in enumerate(path):
        if t[0] == 'eat':
            temp.append(count)
            count = 0
        else:
            count += 1

    avg_digest_block_move.append(np.mean(temp))
    std_digest_block_move.append(np.std(temp))

founded_levels['avg_digest_block_move'] = avg_digest_block_move
founded_levels['std_digest_block_move'] = std_digest_block_move

FEATURE_COLUMNS = [
    "steps",
    "runtime_sec",
    "iteration",
    "shuffle_count",
    "avg_digest_block_move",
    "std_digest_block_move",
]

founded_levels[FEATURE_COLUMNS].describe()

founded_levels[FEATURE_COLUMNS].corr().style.background_gradient(cmap='coolwarm')

# sns.pairplot(founded_levels[FEATURE_COLUMNS], diag_kind='kde', markers='o', hue='shuffle_count', palette='viridis')

founded_levels[FEATURE_COLUMNS].info()

founded_levels['level'] = founded_levels['file'].apply(lambda x: int(x.split('/')[-1].split('.')[0].split('_')[1]))

founded_levels

founded_levels = founded_levels.groupby('level').agg(
    avg_digest_block_move_avg=('avg_digest_block_move', 'mean'),
    std_digest_block_move_avg=('std_digest_block_move', 'mean'),
    avg_digest_block_move_std=('avg_digest_block_move', 'std'),
    std_digest_block_move_std=('std_digest_block_move', 'std'),
    
    avg_digest_block_move_min=('avg_digest_block_move', 'min'),
    avg_digest_block_move_max=('avg_digest_block_move', 'max'),

    avg_runtime_sec=('runtime_sec', 'mean'),
    med_runtime_sec=('runtime_sec', 'median'),
    std_runtime_sec=('runtime_sec', 'std'),
    min_runtime_sec=('runtime_sec', 'min'),
    max_runtime_sec=('runtime_sec', 'max'),

    avg_iteration=('iteration', 'mean'),
    med_iteration=('iteration', 'median'),
    std_iteration=('iteration', 'std'),
    min_iteration=('iteration', 'min'),
    max_iteration=('iteration', 'max'),

    avg_steps=('steps', 'mean'),
    med_steps=('steps', 'median'),
    std_steps=('steps', 'std'),
    min_steps=('steps', 'min'),
    max_steps=('steps', 'max'),
).reset_index()

founded_levels = founded_levels[founded_levels['level'] > 30]

from enum import Enum

class Special_Block(Enum):
    NORMAL = 0
    VECTOR = 1
    LAYER = 2
    HEART = 3
    ICE = 4
    BOMB = 5
    LOCK = 6

class Special_Door(Enum):
    NORMAL = 0
    SHUTTER = 1
    HEART = 2
    ICE = 3


def calculate_wall_bounder(game : GameStateBase) -> int:
    """Calculate the number of wall bounder in the game state."""
    occupied = set(game.walls_occupied)
    occupied.update(game.doors_occupied)
    
    num_wall = 0
    for cell in game.walls_occupied:
        flag = False
        # print(cell)
        for dx, dy in [(1, 0), (-1, 0), (0, 1), (0, -1)]:
            for i in range(1, max(game.size_x, game.size_y) + 3):
                next_cell = (cell[0] + dx * i, cell[1] + dy * i)
                if next_cell in occupied:
                    break
                
                # print("vcl", next_cell)
                
                if next_cell[0] < -1 or next_cell[0] > game.size_x or next_cell[1] < -1 or next_cell[1] > game.size_y:
                    flag = True
                    break
            if flag:
                break
        
        if flag:
            num_wall += 1
                
    return num_wall

def calculate_num_space(game : GameStateBase) -> int:
    """Calculate the number of empty spaces in the game state."""
    occupied = set()#set(game.blocks_occupied)
    occupied.update(game.walls_occupied)
    occupied.update(game.doors_occupied)   
    
    all_occupied = copy.deepcopy(occupied)
    all_occupied.update(game.blocks_occupied)
     
    # for block in game.blocks:
    # x, y = game.blocks[0].subBlocks[0]
    
    visited = set(game.blocks[0].subBlocks[0])
    queue = [game.blocks[0].subBlocks[0]]
     
    num_space = 0
    while queue:
        current = queue.pop(0)
        
        if current not in all_occupied:
            num_space += 1
        
        # Check all 4 directions
        for dx, dy in [(1, 0), (-1, 0), (0, 1), (0, -1)]:
            next_cell = (current[0] + dx, current[1] + dy)
            if next_cell not in visited and next_cell not in occupied:
                queue.append(next_cell)
                visited.add(next_cell)

        if num_space > 144:
            break
                
    return num_space

game_data = []

for level in tqdm(founded_levels['level'].values):
    file_path = f"../Level_Updated/Level_{level}.json"
    with open(file_path) as file:
        content = json.load(file)
        block_list = content['blockList']
        num_blocks = len(block_list)
        
        normal_blocks = [block for block in block_list if block['special'] == Special_Block.NORMAL.value]
        vector_blocks = [block for block in block_list if block['special'] == Special_Block.VECTOR.value]
        layer_blocks = [block for block in block_list if block['special'] == Special_Block.LAYER.value]
        heart_blocks = [block for block in block_list if block['special'] == Special_Block.HEART.value]
        ice_blocks = [block for block in block_list if block['special'] == Special_Block.ICE.value]
        bomb_blocks = [block for block in block_list if block['special'] == Special_Block.BOMB.value]
        lock_blocks = [block for block in block_list if block['special'] == Special_Block.LOCK.value]

        door_list = content['doorList']
        num_doors = len(door_list)

        normal_doors = [door for door in door_list if door['special'] == Special_Door.NORMAL.value]
        shutter_doors = [door for door in door_list if door['special'] == Special_Door.SHUTTER.value]
        heart_doors = [door for door in door_list if door['special'] == Special_Door.HEART.value]
        ice_doors = [door for door in door_list if door['special'] == Special_Door.ICE.value]

        unique_colors = len(set(block['color'] for block in block_list))
        time_limit = content.get('time')

        tile_paths = content['specialTileList']

        wall_list = content['wallList']


        game_state = GameStateBase(file_path)
        boundary_walls = calculate_wall_bounder(game_state)
        num_space = calculate_num_space(game_state)
        edible_blocks = game_state.can_eat()

        game_data.append({
            "level": level,
            "time_limit": time_limit,
            "num_blocks": num_blocks,
            "early_edible_blocks": len(edible_blocks),
            "num_tile_path": len(tile_paths),
            "num_unique_colors": unique_colors,
            "obstacles": len(wall_list) - boundary_walls,
            "num_space": num_space,
            "num_normal_blocks": len(normal_blocks),
            "num_vector_blocks": len(vector_blocks),
            "num_layer_blocks": len(layer_blocks),
            "num_heart_blocks": len(heart_blocks),
            "num_ice_blocks": len(ice_blocks),
            "num_bomb_blocks": len(bomb_blocks),
            "num_lock_blocks": len(lock_blocks),
            "num_doors": num_doors,
            "num_normal_doors": len(normal_doors),
            "num_shutter_doors": len(shutter_doors),
            "num_heart_doors": len(heart_doors),
            "num_ice_doors": len(ice_doors)
        })

game_data_df = pd.DataFrame(game_data)

game_data_df

real_game = pd.read_json("slide_jam_level_0_1000.json")
real_game.head()

founded_levels.columns

game_data_df.columns

FEATURE_COLUMNS = [
'avg_digest_block_move_avg', 'std_digest_block_move_avg',
'avg_digest_block_move_std', 'std_digest_block_move_std',
'avg_digest_block_move_min', 'avg_digest_block_move_max',
'avg_runtime_sec', 'med_runtime_sec', 'std_runtime_sec',
'min_runtime_sec', 'max_runtime_sec', 'avg_iteration', 'med_iteration',
'std_iteration', 'min_iteration', 'max_iteration', 'avg_steps',
'med_steps', 'std_steps', 'min_steps', 'max_steps',

'time_limit', 'num_blocks','early_edible_blocks', 'num_tile_path', 'num_unique_colors',
'obstacles', 'num_space', 'num_normal_blocks', 'num_vector_blocks',
'num_layer_blocks', 'num_heart_blocks', 'num_ice_blocks',
'num_bomb_blocks', 'num_lock_blocks', 'num_doors', 'num_normal_doors',
'num_shutter_doors', 'num_heart_doors', 'num_ice_doors'
]

from lightgbm import LGBMRegressor
import matplotlib.pyplot as plt
import numpy as np
import optuna
from optuna.pruners import MedianPruner
from optuna.samplers import TPESampler
import pandas as pd
import seaborn as sns
from sklearn.inspection import permutation_importance
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import KFold, cross_val_score, train_test_split
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler
from sklearn.svm import SVR
from xgboost import XGBRegressor

real_game

def viz_results(X_test, y_test, pred, best_pipe=None):
    r2 = r2_score(y_test, pred)
    mse = mean_squared_error(y_test, pred)
    print("Test R2:", r2)
    print("Test MSE:", mse)

    # Scatter y vs pred
    plt.figure(figsize=(7,6))
    sns.scatterplot(x=y_test, y=pred)
    mn, mx = min(y_test.min(), pred.min()), max(y_test.max(), pred.max())
    plt.plot([mn, mx], [mn, mx], linestyle="--", linewidth=2)
    plt.xlabel("Actual")
    plt.ylabel("Predicted")
    plt.title("SVR (Optuna + KFold)")
    plt.show()

    # Sorted line plot
    order = np.argsort(y_test)
    plt.figure(figsize=(10,5))
    plt.plot(y_test[order], label="Actual", marker="o", linewidth=1)
    plt.plot(pred[order], label="Predicted", marker="x", linewidth=1)
    plt.legend()
    plt.title("Actual vs Predicted (Sorted)")
    plt.show()

        # Permutation importance (trên pipeline đã fit)
    if best_pipe:
        pi = permutation_importance(best_pipe, X_test, y_test, n_repeats=60, n_jobs=-1)
        imp = pd.Series(pi.importances_mean, index=FEATURE_COLUMNS).sort_values(ascending=False)
        plt.figure(figsize=(8,5))
        sns.barplot(x=imp.index, y=imp.values)
        plt.xticks(rotation=45, ha='right')
        plt.ylabel("Mean decrease in R2")
        plt.title("Permutation Importance")
        plt.tight_layout()
        plt.show()

    # Residuals
    res = y_test - pred
    plt.figure(figsize=(7,4))
    sns.histplot(res, bins=30, kde=True)
    plt.title("Residual Distribution (Actual - Predicted)")
    plt.xlabel("Residual")
    plt.show()

    return r2, mse

df = founded_levels.merge(
    real_game[['level', 'win_rate', 'use_booster_count_avg']], on='level', how='inner'
).sort_values('level').dropna()

df = df.merge(
    game_data_df, on='level', how='inner'
).sort_values('level').dropna()

df.to_csv('final_data.csv', index=False)

df = founded_levels.merge(
    real_game[['level', 'win_rate']], on='level', how='inner'
).sort_values('level').dropna()

df = df.merge(
    game_data_df, on='level', how='inner'
).sort_values('level').dropna()

X = df[FEATURE_COLUMNS]
y = df['win_rate']

def run_pipeline(SEED):    
    df = founded_levels.merge(
        real_game[['level', 'win_rate']], on='level', how='inner'
    ).sort_values('level').dropna()

    df = df.merge(
        game_data_df, on='level', how='inner'
    ).sort_values('level').dropna()

    X = df[FEATURE_COLUMNS].values
    y = df['win_rate'].values


    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.1, random_state=SEED, shuffle=True
    )

    cv = KFold(n_splits=5, shuffle=True, random_state=SEED)

    def objective(trial):
        C = trial.suggest_float("C", 1e-2, 1e3, log=True)
        gamma = trial.suggest_float("gamma", 1e-4, 1e0, log=True)
        epsilon = trial.suggest_float("epsilon", 1e-3, 0.5, log=True)

        pipe = Pipeline([
            ("scaler", StandardScaler()),
            ("svr", SVR(C=C, gamma=gamma, epsilon=epsilon))
        ])

        scores = cross_val_score(pipe, X_train, y_train, cv=cv, scoring="neg_mean_squared_error", n_jobs=-1)
        trial.set_user_attr("cv_scores", scores.tolist())
        return scores.mean()

    study = optuna.create_study(
        direction="maximize",
        sampler=TPESampler(seed=SEED),
        pruner=MedianPruner(n_warmup_steps=10),
    )

    study.optimize(objective, n_trials=200)

    print("Best Trial:", study.best_trial.number)
    print("Best Params:", study.best_params)
    print("CV R2 (mean):", study.best_value)

    best_params = study.best_params
    best_pipe = Pipeline([
        ("scaler", StandardScaler()),
        ("svr", SVR(**best_params))
    ])

    best_pipe.fit(X_train, y_train)
    pred = best_pipe.predict(X_test)

    all_pred = best_pipe.predict(X)

    # Tính residual
    df["pred_win_rate"] = all_pred
    df["residual"] = np.abs(df["win_rate"] - df["pred_win_rate"])

    # Lấy top 50
    top_res = df.sort_values("residual", ascending=False).head(100)

    # Vẽ side-by-side bar cho dễ nhìn
    x = np.arange(len(top_res))
    width = 0.35

    plt.figure(figsize=(15,6))
    plt.bar(x - width/2, top_res["win_rate"], width, label="Actual", color="orange")
    plt.bar(x + width/2, top_res["pred_win_rate"], width, label="Predicted", color="blue")

    plt.xticks(x, top_res["level"], rotation=90)
    plt.ylabel("Win Rate")
    plt.title("Top 50 Levels with Highest Residuals")
    plt.legend()
    plt.tight_layout()
    plt.show()

    return viz_results(X_test, y_test, pred, best_pipe)

np.random.seed(42)
random_seeds = np.random.randint(1000, size=10)
results = []

for seed in random_seeds:
    r2, mse = run_pipeline(seed)
    results.append({
        "r2": r2,
        "mse": mse
    })

results_df = pd.DataFrame(results)
results_df.describe().T[['mean', 'std', 'min', 'max']].round(4).style.format("{:.4f}").set_caption("Random Seeds Results Summary")

outlier_levels = df.sort_values("residual", ascending=False).head(100).level.values

outlier_df = df[df["level"].isin(outlier_levels)]

outlier_df

df = founded_levels.merge(
    real_game[['level', 'win_rate']], on='level', how='inner'
).sort_values('level').dropna()

df = df.merge(
    game_data_df, on='level', how='inner'
).sort_values('level').dropna()
df

df = founded_levels.merge(
    real_game[['level', 'win_rate']], on='level', how='inner'
).sort_values('level').dropna()

df = df.merge(
    game_data_df, on='level', how='inner'
).sort_values('level').dropna().drop(columns=['level'])

label = 'win_rate'
train_df, test_df = train_test_split(df, test_size=0.1, shuffle=True)

from autogluon.tabular import TabularPredictor

train_df

predictor = TabularPredictor(
    label=label, problem_type='regression', eval_metric='r2'
).fit(
    train_data=train_df,
    # presets="best_quality",
    time_limit=300,
)

leader_board = predictor.leaderboard(test_df, silent=True)

leader_board

y_test = test_df[label].values
pred = predictor.predict(test_df).values

# viz_results(np.array(df.drop(columns=[label, "level"]).values), np.array([x[0] for x in df[[label]].values]), pred)

viz_results(test_df.drop(columns=[label]).values, y_test, pred)

pred = predictor.predict(df.drop(columns=[label])).values

viz_results(np.array(df.drop(columns=[label]).values), np.array([x[0] for x in df[[label]].values]), pred)

predictor.feature_importance(df)



