from src.game.objects import Block, Door, Wall, Object
from src.game.const import Special_Block, Special_Door, COLORS, DoorDirection
from typing import List, Tuple, Set
import os
import json
from collections import deque
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import copy
from .special_tile import SpecialTile


class GameState:
    def __init__(self, path: str) -> None:
        self.blocks : List[Block] = []
        self.walls : List[Wall] = []
        self.doors : List[Door] = []
        self.ice_blocks: Set[int] = set()
        self.key_blocks: Set[int] = set()
        self.ice_doors: Set[int] = set()
        self.shutter_doors: Set[int] = set()
        self.special_tiles: List[SpecialTile] = []
        
        # Tập hợp các ô đã chiếm
        self.blocks_occupied = set()
        self.walls_occupied = set()
        self.doors_occupied = set()
        
        """Khởi tạo GameState từ dữ liệu JSON"""
        data = json.load(open(path, 'r'))
        if data is None:
            raise ValueError("Invalid game state data")
        self.level = data['level']
        self.difficulty = data['difficulty']
        self.name = self.level
        self.time = data['time']

        # Là typle (x, y)
        self.size_x = data['size']['x']
        self.size_y = data['size']['y']

        tilelist_data = data.get("specialTileList", [])
        id = 0
        for tile in tilelist_data:
            temp_tile = SpecialTile(
                id, tile["specialTile"], tile["gridPos"], tile["color"]
            )
            self.special_tiles.append(temp_tile)
            id += 1

        # Load Block objects
        id = 0
        for block_data in data['blockList']:
            temp_block = Block(block_data, id)
            if temp_block.special == Special_Block.ICE.value:
                self.ice_blocks.add(temp_block.id)
            if temp_block.hasKey:
                self.key_blocks.add(temp_block.id)
            self.blocks.append(temp_block)
            id += 1
        
        # Load Wall objects
        for wall_data in data['wallList']:
            self.walls.append(Wall(wall_data))
        
        # Load Door objects
        id = 0
        for door_data in data['doorList']:
            temp_door = Door(door_data, id)
            self.doors.append(temp_door)
            if temp_door.special == Special_Door.ICE.value:
                self.ice_doors.add(temp_door.id)
            elif temp_door.special == Special_Door.SHUTTER.value:
                self.shutter_doors.add(temp_door.id)
            id += 1
            
        # Tạo tập hợp các ô đã chiếm
        for block in self.blocks:
            self.blocks_occupied.update(block.subBlocks)
        for wall in self.walls:
            self.walls_occupied.update(wall.subBlocks)
        for door in self.doors:
            self.doors_occupied.update(door.subBlocks)

        self.level_path = os.path.join("solver/output", f"level_{self.level}")
    
    def remove_occupied(self, block: Block) -> bool:
        if block.subBlocks:
            self.blocks_occupied.difference_update(block.subBlocks)
            return True
        return False

    def add_occupied(self, block: Block) -> bool:
        if block.subBlocks:
            self.blocks_occupied.update(block.subBlocks)
            return True
        return False
    
    def is_accessible_door(self, block: Block, door: Door) -> bool:
        """Check if a door is accessible for a specific block"""
        if door.color != block.color:
            return False

        door_is_accessible = False

        # Handle Heart blocks
        if block.special == Special_Block.HEART.value:
            if door.special == Special_Door.HEART.value:
                door_is_accessible = True

        # Handle Ice doors
        elif door.special == Special_Door.ICE.value:
            if door.turnCount <= 0:
                door_is_accessible = True

        # Handle Shutter doors
        elif door.special == Special_Door.SHUTTER.value:
            if door.open:
                door_is_accessible = True
        else:
            door_is_accessible = True

        min_x = min(block.subBlocks, key=lambda cell: cell[0])[0]
        min_y = min(block.subBlocks, key=lambda cell: cell[1])[1]
        max_x = max(block.subBlocks, key=lambda cell: cell[0])[0]
        max_y = max(block.subBlocks, key=lambda cell: cell[1])[1]

        if door.direction == DoorDirection.VERTICAL:
            if max_y - min_y + 1 > len(door.subBlocks):
                door_is_accessible = False
        else:
            if max_x - min_x + 1 > len(door.subBlocks):
                door_is_accessible = False

        return door_is_accessible
    
    def get_accessible_doors(self, block: Block) -> List[Door]:
        """Lấy danh sách các cửa có thể truy cập cho block"""
        accessible_doors = []
        for door in self.doors:
            if self.is_accessible_door(block, door):
                accessible_doors.append(door)
        return accessible_doors

    def can_move_block(self, block: Block) -> bool:
        """Kiểm tra xem block có thể di chuyển hay không"""
        if block.special == Special_Block.ICE.value and block.turnCount > 0:
            return False
        if block.special == Special_Block.LOCK.value and len(self.key_blocks) > 0:
            return False
        return True
    
    def can_cook_custom(self, block: Block) -> bool:
        if self.can_move_block(block) is False:
            return False
        occupied = set(self.doors_occupied)
        occupied.update(self.blocks_occupied)
        occupied.update(self.walls_occupied)

        for special_tile in self.special_tiles:
            if (special_tile.special == 2 and special_tile.color != block.color):
                if (special_tile.x, special_tile.y) not in occupied:
                    occupied.add((special_tile.x, special_tile.y))
        
        # Loại bỏ các ô hiện tại của block
        for cell in block.subBlocks:
            if cell in occupied:
                occupied.remove(cell)

        min_x = min(block.subBlocks, key=lambda cell: cell[0])[0]
        min_y = min(block.subBlocks, key=lambda cell: cell[1])[1]
        max_x = max(block.subBlocks, key=lambda cell: cell[0])[0]
        max_y = max(block.subBlocks, key=lambda cell: cell[1])[1]

        eatable = False
        list_doors = self.get_accessible_doors(block)
        for door in list_doors:
            min_door_x = min(door.subBlocks, key=lambda cell: cell[0])[0]
            min_door_y = min(door.subBlocks, key=lambda cell: cell[1])[1]
            max_door_x = max(door.subBlocks, key=lambda cell: cell[0])[0]
            max_door_y = max(door.subBlocks, key=lambda cell: cell[1])[1]

            skip = False
            if door.direction == DoorDirection.VERTICAL:
                # Cua nam doc
                if not (min_door_y <= min_y and max_y <= max_door_y):
                    skip = True
                assert min_door_x == max_door_x
                for subBlock in block.subBlocks:
                    for i in range(min(subBlock[0], min_door_x) + 1, max(subBlock[0], min_door_x)):
                        if (i, subBlock[1]) in occupied:
                            skip = True
            else:
                # Cua nam ngang
                if not (min_door_x <= min_x and max_x <= max_door_x):
                    skip = True
                assert min_door_y == max_door_y
                for subBlock in block.subBlocks:
                    for i in range(min(subBlock[1], min_door_y) + 1, max(subBlock[1], min_door_y)):
                        if (subBlock[0], i) in occupied:
                            skip = True
            
            if not skip:
                eatable = True
            
            if eatable:
                break

        return eatable

    def can_cook(self, block: Block) -> bool:
        if self.can_move_block(block) is False:
            return False
        occupied = set(self.doors_occupied)
        occupied.update(self.blocks_occupied)
        occupied.update(self.walls_occupied)
        
        for door in self.get_accessible_doors(block):
            for s in door.subBlocks:
                occupied.discard(s)
        
        # Loại bỏ các ô hiện tại của block
        for cell in block.subBlocks:
            if cell in occupied:
                occupied.remove(cell)

        # Thử từng hướng di chuyển
        for (dx, dy) in block.directions.values():
            step = 1
            while True:
                # Tính vị trí mới của block
                moved_cells = [(x + dx * step, y + dy * step) for (x, y) in block.subBlocks]
                
                # Kiểm tra va chạm
                if any(cell in occupied for cell in moved_cells):
                    break
                
                # Kiểm tra xem block có ra khỏi board chưa
                if all(
                    cell[0] < 0 or cell[0] >= self.size_x or cell[1] < 0 or cell[1] >= self.size_y
                    for cell in moved_cells
                ):
                    return True
                step += 1
                # Giới hạn số bước để tránh lặp vô hạn
                if step > max(self.size_x, self.size_y) * 3:
                    break
        return False
    
    def is_valid_move(self, block: Block, dx: int, dy: int) -> bool:
        for sub in block.get_subBlocks():
            new_x = sub[0] + dx
            new_y = sub[1] + dy

            if (
                -1 >= new_x
                or new_x >= self.size_x
                or -1 >= new_y
                or new_y >= self.size_y
            ):
                return False

            new_pos = (new_x, new_y)
            for tile in self.special_tiles:
                if (tile.x, tile.y) == new_pos and block.color != tile.color:
                    return False
            if (
                new_pos in self.blocks_occupied
                or new_pos in self.walls_occupied
                or new_pos in self.doors_occupied
            ):
                return False
        return True
    
    def is_win(self) -> bool:
        return len(self.blocks) == 0
    
    def can_exit(self, block: Block) -> bool:
        """Kiểm tra xem block có thể thoát khỏi game state hay không - ban cu cua anh Thach"""
        if block.special == Special_Block.ICE.value and block.turnCount > 0:
            return False
        occupied = set(self.doors_occupied)
        occupied.update(self.blocks_occupied)
        occupied.update(self.walls_occupied)
        
        if block.special == Special_Block.HEART.value:
            for door in self.doors:
                if door.color == block.color and door.special == Special_Door.HEART.value:
                    for cell in door.subBlocks:
                        if cell in occupied:
                            occupied.remove(cell)
        else:
            for door in self.doors:
                if door.color == block.color:
                    for cell in door.subBlocks:
                        if cell in occupied:
                            occupied.remove(cell)
        
        # Loại bỏ các ô hiện tại của block
        for cell in block.subBlocks:
            if cell in occupied:
                occupied.remove(cell)

        # Thử từng hướng di chuyển
        for (dx, dy) in block.directions.values():
            step = 1
            while True:
                # Tính vị trí mới của block
                moved_cells = [(x + dx * step, y + dy * step) for (x, y) in block.subBlocks]
                
                # Kiểm tra va chạm
                if any(cell in occupied for cell in moved_cells):
                    break
                
                # Kiểm tra xem block có ra khỏi board chưa
                if all(
                    cell[0] < 0 or cell[0] >= self.size_x or cell[1] < 0 or cell[1] >= self.size_y
                    for cell in moved_cells
                ):
                    return True
                step += 1
                # Giới hạn số bước để tránh lặp vô hạn
                if step > max(self.size_x, self.size_y) * 3:
                    break
        return False
    
    def get_valid_moves(self, block) -> List[Tuple[int, int]]:
        """Lấy danh sách các hướng di chuyển hợp lệ cho block"""
        if block.special == Special_Block.ICE.value and block.turnCount > 0:
            return []
        if block.special == Special_Block.LOCK.value and len(self.key_blocks) > 0:
            return []
        valid_moves = []
        # Xóa tạm thời occupied
        self.remove_occupied(block)
        for (dx, dy) in block.directions.values():
            for i in range(max(self.size_x, self.size_y)):
                if self.is_valid_move(block, dx * i, dy * i):
                    valid_moves.append((dx * i, dy * i))
                else:
                    break
        self.add_occupied(block)
        return valid_moves
    
    def can_exit_dfs(self, block: Block) -> bool:
        if block.special == Special_Block.ICE.value and block.turnCount > 0:
            return False
        occupied = set(self.doors_occupied)
        occupied.update(self.blocks_occupied)
        occupied.update(self.walls_occupied)
        
        # Loại bỏ các ô hiện tại của block
        for cell in block.subBlocks:
            if cell in occupied:
                occupied.remove(cell)

        for special_tile in self.special_tiles:
            if (special_tile.special == 2 and special_tile.color != block.color):
                if (special_tile.x, special_tile.y) not in occupied:
                    occupied.add((special_tile.x, special_tile.y))

        temp_game = copy.deepcopy(self)

        if temp_game.can_cook_custom(temp_game.find_block(block.id)):
            return True

        # Thử từng hướng di chuyển
        visited = set()
        stack = [block.subBlocks[0]]
        while stack:
            current = stack.pop()
            visited.add(current)
            (ndx, ndy) = (current[0] - block.subBlocks[0][0], current[1] - block.subBlocks[0][1])
            current_cells = [(x + ndx, y + ndy) for (x, y) in block.subBlocks]

            temp_game.move_block(temp_game.find_block(block.id), ndx, ndy)

            for (dx, dy) in block.directions.values():
                moved_cells = [(x + dx, y + dy) for (x, y) in current_cells]
                if moved_cells[0] in visited:
                    continue

                if any(cell in occupied for cell in moved_cells):
                    continue

                temp_game.move_block(temp_game.find_block(block.id), dx, dy)
                if temp_game.can_cook_custom(temp_game.find_block(block.id)):
                    return True
                
                temp_game.move_block(temp_game.find_block(block.id), -dx, -dy) # Undo move

                stack.append(moved_cells[0])
            
            temp_game.move_block(temp_game.find_block(block.id), -ndx, -ndy) # Undo move

        return False
    
    def can_eat_dfs(self) -> List[Block]:
        """Sử dụng DFS để tìm các block có thể ăn được"""
        result = []
        
        for block in self.blocks:

            has_matching_door = any(self.is_accessible_door(block, door) for door in self.doors)

            if has_matching_door and self.can_exit_dfs(block):
                result.append(block)
                
        return result
    


    def get_valid_moves_hung(self, block: Block, max_step: int = 1) -> List[Tuple[int, int]]:
        """Lấy danh sách các hướng di chuyển hợp lệ cho block"""
        if block.special == Special_Block.ICE.value and block.turnCount > 0:
            return []
        if block.special == Special_Block.LOCK.value and len(self.key_blocks) > 0:
            return []
        
        max_step = min(max_step, self.size_x, self.size_y)
        valid_moves = []
        
        self.remove_occupied(block)
        visited = set()
        dq = deque([(0, 0, 0)])
        visited.add((0, 0))
        
        while dq:
            dx, dy, steps = dq.popleft()
            if dx != 0 or dy != 0:
                valid_moves.append((dx, dy))
                
            if steps >= max_step:
                continue
            
            for (dir_dx, dir_dy) in block.directions.values():
                new_dx = dx + dir_dx
                new_dy = dy + dir_dy
                
                if self.is_valid_move(block, new_dx, new_dy):
                    new_pos = (new_dx, new_dy)
                    if new_pos not in visited:
                        visited.add(new_pos)
                        dq.append((new_dx, new_dy, steps + 1))
        
        self.add_occupied(block)
        return valid_moves
    
    def find_block(self, id: int) -> Block:
        """Tìm block theo id"""
        for block in self.blocks:
            if block.id == id:
                return block
        return None
    
    def move_block(self, block: Block, dx: int, dy: int, time: int = 1) -> bool:
        """Di chuyển block nếu hợp lệ"""
        # if not self.is_valid_move(block, dx, dy):
        #     return False
        for i in range(time):
            self.remove_occupied(block)
            block.move(dx, dy)
            self.add_occupied(block)
        return True
    
    def can_eat(self) -> List[Block]:
        """Kiểm tra xem có block nào có thể ăn được không"""
        result : List[Block] = [block for block in self.blocks if self.can_exit(block)]
        return result
    
    def eat_block(self, block: Block) -> bool:
        """Xoá block khỏi game state"""
        if block.special == Special_Block.LAYER.value:
            block.special = Special_Block.NORMAL.value
            block.color = block.secondColor
        else:
            self.remove_occupied(block)
            self.blocks.remove(block)

        # Cập nhật các ice blocks
        to_remove = []
        for b_id in self.ice_blocks:
            for b in self.blocks:
                if b.id == b_id:
                    b.turnCount -= 1
                    if b.turnCount <= 0:
                        to_remove.append(b_id)
        self.ice_blocks.difference_update(to_remove)
            
        # Cập nhật các ice doors
        to_remove = []
        for d_id in self.ice_doors:
            for d in self.doors:
                if d.id == d_id:
                    d.turnCount -= 1
                    if d.turnCount <= 0:
                        to_remove.append(d_id)
        self.ice_doors.difference_update(to_remove)
        
        # Cập nhật các shutter doors
        for d_id in self.shutter_doors:
            for d in self.doors:
                if d.id == d_id:
                    d.open = not d.open

        # Cập nhật các key blocks
        to_remove = []
        for b_id in self.key_blocks:
            if block.id == b_id:
                    to_remove.append(b_id)
        self.key_blocks.difference_update(to_remove)
        return True
    
    def shuffle_blocks(self, trial: int = 20, shuffle_steps: int = 10) -> List[Tuple[Block, int]]:
        pass
    
    def visualize_state(
        self, saved_image=False, save_path: str = "Output", index_image=0, block_id = -1, ax = None
    ) -> None:
        """Visualize the current game state using matplotlib."""
        # ax.clear()
        if not ax:
            fig, ax = plt.subplots(figsize=(8, 6))

        # Draw blocks
        for block in self.blocks:
            edge_color = "black" if block.id != block_id else "red"
            for x, y in block.subBlocks:
                rect = None
                if block.special == Special_Block.VECTOR.value:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        1,
                        1,
                        facecolor=COLORS[block.color],
                        edgecolor=edge_color,
                        hatch="|" if block.moveType == 2 else "-",
                        linewidth=0.5 if block.id != block_id else 2,
                    )
                elif block.special == Special_Block.HEART.value:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        1,
                        1,
                        facecolor=COLORS[block.color],
                        edgecolor=edge_color,
                        hatch="*",
                        linewidth=0.5 if block.id != block_id else 2,
                    )
                elif block.special == Special_Block.ICE.value:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        1,
                        1,
                        facecolor=COLORS[block.color],
                        edgecolor=edge_color,
                        hatch="X",
                        linewidth=0.5 if block.id != block_id else 2,
                    )
                    # Hiển thị số turnCount ở giữa block
                    ax.text(
                        x,
                        y,
                        str(block.turnCount),
                        color="black",
                        fontsize=12,
                        ha="center",
                        va="center",
                        fontweight="bold",
                    )
                elif block.special == Special_Block.LOCK.value:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        1,
                        1,
                        facecolor=COLORS[block.color],
                        edgecolor=edge_color,
                        linewidth=0.5 if block.id != block_id else 2,
                    )
                    # Hiển thị icon khóa
                    ax.text(x, y, "lock", fontsize=14, ha="center", va="center")
                elif block.special == Special_Block.LAYER.value:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        0.8,
                        0.8,
                        facecolor=COLORS[block.secondColor],
                        edgecolor=COLORS[block.color],
                        linewidth=7,
                    )
                elif block.special == Special_Block.BOMB.value:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        1,
                        1,
                        facecolor=COLORS[block.color],
                        edgecolor=edge_color,
                        linewidth=0.5 if block.id != block_id else 2,
                    )
                    ax.text(
                        x,
                        y,
                        str(block.turnCount),
                        color="black",
                        fontsize=12,
                        ha="center",
                        va="center",
                        fontweight="bold",
                    )
                elif block.special == Special_Block.NORMAL.value:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        1,
                        1,
                        facecolor=COLORS[block.color],
                        edgecolor=edge_color,
                        linewidth=0.5 if block.id != block_id else 2,
                    )
                    if block.hasKey:
                        # Hiển thị icon chìa khóa
                        ax.text(x, y, "key", fontsize=14, ha="center", va="center")

                ax.text(
                    x - 0.48,
                    y + 0.48,
                    str(block.id),
                    color="black",
                    fontsize=8,
                    ha="left",
                    va="top",
                    fontweight="bold",
                )

                ax.add_patch(rect)

            # Draw walls
            for wall in self.walls:
                for x, y in wall.subBlocks:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        1,
                        1,
                        facecolor=COLORS[wall.color],
                        edgecolor="black",
                        linewidth=0.5
                    )
                    ax.add_patch(rect)

        # Draw doors
        for door in self.doors:
            for x, y in door.subBlocks:
                symbol = "|" if door.direction == DoorDirection.VERTICAL else "-"
                if door.special == Special_Door.HEART.value:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        1,
                        1,
                        facecolor=COLORS[door.color],
                        edgecolor="black",
                        hatch="*" + symbol,
                        linewidth=0.5 if door.id != block_id else 2,
                    )
                elif door.special == Special_Door.ICE.value:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        1,
                        1,
                        facecolor=COLORS[door.color],
                        edgecolor="black",
                        hatch="X" + symbol,
                        linewidth=0.5 if door.id != block_id else 2,
                    )
                    # Hiển thị số turnCount ở giữa block
                    ax.text(
                        x,
                        y,
                        str(door.turnCount),
                        color="black",
                        fontsize=12,
                        ha="center",
                        va="center",
                        fontweight="bold",
                    )
                elif door.special == Special_Door.SHUTTER.value:
                    if door.open:
                        rect = Rectangle(
                            (x - 0.5, y - 0.5),
                            1,
                            1,
                            facecolor=COLORS[door.color],
                            edgecolor="black",
                            hatch="//" + symbol,
                        )
                    else:
                        rect = Rectangle(
                            (x - 0.5, y - 0.5),
                            1,
                            1,
                            facecolor=COLORS[door.color],
                            edgecolor="black",
                            hatch="X" + symbol,
                            alpha=0.5,
                        )
                else:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        1,
                        1,
                        facecolor=COLORS[door.color],
                        edgecolor="black",
                        hatch="//" + symbol,
                    )
                ax.add_patch(rect)

        # Visualize special tiles
        for tile in self.special_tiles:
            rect = Rectangle(
                (tile.x - 0.5, tile.y - 0.5),
                1,
                1,
                color=COLORS[tile.color],
                edgecolor="black",
                linewidth=0.5,
                alpha=0.3
            )
            ax.add_patch(rect)

            # Configure plot
        ax.set_xlim(-2, self.size_x + 1)
        ax.set_ylim(-2, self.size_y + 1)
        ax.set_aspect('equal')
        ax.grid(True, alpha = 0.3)
        plt.title(f"{self.name} (Level {self.level})")
        if saved_image:
            os.makedirs(self.level_path, exist_ok=True)
            plt.savefig(f"{self.level_path}/{index_image}.jpg")
            plt.close()
        else:
            # plt.show()
            # st.pyplot(plt)
            plt.draw()

    def visualize_occupied(self) -> None:
        """Visualize the occupied cells in the game state."""
        fig, ax = plt.subplots(figsize=(8, 6))
        
        # Draw occupied blocks
        for x, y in self.blocks_occupied:
            rect = Rectangle(
                (x - 0.5, y - 0.5), 1, 1,
                facecolor='lightgray', edgecolor='black'
            )
            ax.add_patch(rect)
        
        # Draw occupied walls
        for x, y in self.walls_occupied:
            rect = Rectangle(
                (x - 0.5, y - 0.5), 1, 1,
                facecolor='darkgray', edgecolor='black'
            )
            ax.add_patch(rect)
        
        # Draw occupied doors
        for x, y in self.doors_occupied:
            rect = Rectangle(
                
                (x - 0.5, y - 0.5), 1, 1,
                facecolor='lightblue', edgecolor='black', hatch='//'
            )
            ax.add_patch(rect)
        
        # Configure plot
        ax.set_xlim(-2, self.size_x + 1)
        ax.set_ylim(-2, self.size_y + 1)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        plt.title("Occupied Cells")
        plt.show()
    
    def __str__(self) -> str:
        """String representation of the game state."""
        return f"GameState(level={self.level}, difficulty={self.difficulty}, name={self.name}, time={self.time}, size=({self.size_x}, {self.size_y}), \
                blocks={len(self.blocks)}, walls={len(self.walls)}, doors={len(self.doors)})"
        
    def to_hashable(self) -> Tuple:
        """Creates a hashable representation of the game state for use in sets/dicts."""
        
        # Include all relevant block properties
        blocks_tuple = tuple(
            (
                block.id,
                block.color, 
                block.secondColor,  # Important for LAYER blocks
                block.special,
                block.turnCount,    # Important for ICE and BOMB blocks
                block.hasKey,       # Important for key blocks
                block.moveType,     # Important for VECTOR blocks
                tuple(sorted(block.subBlocks))
            )
            for block in sorted(self.blocks, key=lambda b: b.id)
        )
        
        # Include wall properties (walls generally don't change, but for completeness)
        walls_tuple = tuple(
            (wall.color, tuple(sorted(wall.subBlocks))) 
            for wall in self.walls
        )
        
        # Include all relevant door properties
        doors_tuple = tuple(
            (
                door.id,
                door.color,
                door.special,
                door.turnCount,
                door.open,
                tuple(sorted(door.subBlocks))
            )
            for door in sorted(self.doors, key=lambda d: d.id)
        )
        
        # Include the special block/door sets that track game state
        ice_blocks_tuple = tuple(sorted(self.ice_blocks))
        key_blocks_tuple = tuple(sorted(self.key_blocks))
        ice_doors_tuple = tuple(sorted(self.ice_doors))
        shutter_doors_tuple = tuple(sorted(self.shutter_doors))
        
        return (
            blocks_tuple,
            walls_tuple,
            doors_tuple,
            ice_blocks_tuple,
            key_blocks_tuple,
            ice_doors_tuple,
            shutter_doors_tuple
        )

    def copy(self) -> 'GameState':
        """Creates a deep copy of this game state."""
        import copy as copy_module
        
        # Create new instance without calling __init__
        new_state = GameState.__new__(GameState)
        
        # Copy basic attributes
        new_state.level = self.level
        new_state.difficulty = self.difficulty
        new_state.name = self.name
        new_state.time = self.time
        new_state.size_x = self.size_x
        new_state.size_y = self.size_y
        new_state.level_path = self.level_path
        new_state.special_tiles = self.special_tiles
                
        # Deep copy blocks, walls, and doors
        new_state.blocks = [block.copy() for block in self.blocks]
        new_state.walls = [wall.copy() for wall in self.walls]
        new_state.doors = [door.copy() for door in self.doors]
        
        # Copy the special block/door tracking sets
        new_state.ice_blocks = self.ice_blocks.copy()
        new_state.key_blocks = self.key_blocks.copy()
        new_state.ice_doors = self.ice_doors.copy()
        new_state.shutter_doors = self.shutter_doors.copy()
        
        # Rebuild occupied sets from the copied objects
        new_state.blocks_occupied = set()
        new_state.walls_occupied = set()
        new_state.doors_occupied = set()
        
        for block in new_state.blocks:
            new_state.blocks_occupied.update(block.subBlocks)
        for wall in new_state.walls:
            new_state.walls_occupied.update(wall.subBlocks)
        for door in new_state.doors:
            new_state.doors_occupied.update(door.subBlocks)
        
        return new_state
    
    def exit_distance(self, block: Block) -> int:
        """
        Compute the min Manhattan distance from any sub-block cell to the nearest boundary exit.
        """
        distances = []
        for (x, y) in block.subBlocks:
            d_left = x
            d_right = self.size_x - 1 - x
            d_bottom = y
            d_top = self.size_y - 1 - y
            distances.append(min(d_left, d_right, d_bottom, d_top))
        return min(distances)

    def count_blockers_in_direction(self, block: Block, direction: Tuple[int, int]) -> int:
        """
        Count other blocks blocking the straight-line path from block towards boundary in given direction.
        """
        dx, dy = direction
        blockers = set()
        for (x0, y0) in block.subBlocks:
            x, y = x0, y0
            while 0 <= x + dx < self.size_x and 0 <= y + dy < self.size_y:
                x += dx; y += dy
                if (x, y) in self.blocks_occupied:
                    # record the block id occupying this cell
                    for b in self.blocks:
                        if (x, y) in b.subBlocks and b.id != block.id:
                            blockers.add(b.id)
                            break
        return len(blockers)

    def block_exit_potential(self, block: Block, max_step: int = 1, blocker_weight: int = 5) -> float:
        """
        Heuristic score: lower is better. Considers distance to exit, number of blockers, and move flexibility.
        """
        # If block cannot move, it's hopeless
        if not self.can_move_block(block):
            return float('inf')
        # Already exits
        if self.can_exit(block):
            return -float('inf')

        # Directions towards boundaries
        directions = [(1, 0), (-1, 0), (0, 1), (0, -1)]
        best_score = float('inf')
        # Precompute valid moves for flexibility bonus
        valid_moves = len(self.get_valid_moves(block, max_step=max_step))

        for direction in directions:
            dist = self.exit_distance(block)
            blockers = self.count_blockers_in_direction(block, direction)
            score = dist + blocker_weight * blockers - valid_moves
            best_score = min(best_score, score)

        return best_score
    
    def heuristic_naive(self) -> float:
        """Heuristic function to estimate the cost to reach the goal state."""
        total_distance = 0.0
        
        for block in self.blocks:
            min_distance = float('inf')
            
            # Chỉ tính khoảng cách tới cửa cùng màu
            for door in self.doors:
                if door.color == block.color:
                    for block_cell in block.subBlocks:
                        for door_cell in door.subBlocks:
                            distance = abs(block_cell[0] - door_cell[0]) + abs(block_cell[1] - door_cell[1])
                            min_distance = min(min_distance, distance)
            
            # Nếu không có cửa cùng màu, penalty lớn
            if min_distance == float('inf'):
                min_distance = max(self.size_x, self.size_y) * 10
                
            total_distance += min_distance
            
        return total_distance
    
    def heuristic_improved(self) -> float:
        total_distance = 0.0
        unfinished_blocks = 0

        for block in self.blocks:
            if block.special == Special_Block.HEART.value:
                matching_doors = [
                    door for door in self.doors
                    if door.color == block.color and door.special == Special_Door.HEART.value
                ]
            else:
                matching_doors = [door for door in self.doors if door.color == block.color]

            if not matching_doors:
                total_distance += max(self.size_x, self.size_y) * 10
                continue

            bx, by = self.center_of_cells(block.subBlocks)
            min_distance = float("inf")
            for door in matching_doors:
                dx, dy = self.center_of_cells(door.subBlocks)
                dist = abs(bx - dx) + abs(by - dy)
                if block.special == Special_Block.HEART.value:
                    dist *= 0.8
                min_distance = min(min_distance, dist)

            total_distance += min_distance
            unfinished_blocks += 1

    def heuristic_custom_Kien_num_block(self) -> float:
        """Custom heuristic function to estimate the cost to reach the goal state."""
        total_distance = 0.0
        
        for block in self.blocks:
            occupied = set(self.doors_occupied)
            occupied.update(self.blocks_occupied)
            occupied.update(self.walls_occupied)

            for cell in block.subBlocks:
                if cell in occupied:
                    occupied.remove(cell)

            min_distance = float('inf')
            
            min_x = min(block.subBlocks, key=lambda cell: cell[0])[0]
            min_y = min(block.subBlocks, key=lambda cell: cell[1])[1]
            max_x = max(block.subBlocks, key=lambda cell: cell[0])[0]
            max_y = max(block.subBlocks, key=lambda cell: cell[1])[1]

            # Chỉ tính khoảng cách tới cửa cùng màu
            for door in self.doors:
                min_door_x = min(door.subBlocks, key=lambda cell: cell[0])[0]
                min_door_y = min(door.subBlocks, key=lambda cell: cell[1])[1]
                max_door_x = max(door.subBlocks, key=lambda cell: cell[0])[0]
                max_door_y = max(door.subBlocks, key=lambda cell: cell[1])[1]

                # Chac chắn rằng cửa cùng màu có thể chứa block
                if door.color == block.color and (max_x - min_x <= max_door_x - min_door_x or max_y - min_y <= max_door_y - min_door_y):
                    distance = 0
                    for block_cell in block.subBlocks:
                        for door_cell in door.subBlocks:
                            # distance = abs(block_cell[0] - door_cell[0]) + abs(block_cell[1] - door_cell[1])
                            for i in range(min(min_door_x, min_x), max(max_door_x, max_x) + 1):
                                for j in range(min(min_door_y, min_y), max(max_door_y, max_y) + 1):
                                    if (i, j) in occupied:
                                        distance += 1
                    if distance != 0:
                        min_distance = min(min_distance, distance)
            
            # Nếu không có cửa cùng màu, penalty lớn
            if min_distance == float('inf'):
                min_distance = max(self.size_x, self.size_y) * 10
                
            total_distance += min_distance
            
        return total_distance
    
    def advanced_heuristic(self) -> float:
        """Heuristic nhanh hơn"""
        if len(self.blocks) == 0:
            return 0
        
        total_score = 0
        
        # Đơn giản hóa: chỉ tính Manhattan distance
        for block in self.blocks:
            min_dist = float('inf')
            
            min_x = min(block.subBlocks, key=lambda cell: cell[0])[0]
            min_y = min(block.subBlocks, key=lambda cell: cell[1])[1]
            max_x = max(block.subBlocks, key=lambda cell: cell[0])[0]
            max_y = max(block.subBlocks, key=lambda cell: cell[1])[1]

            doors = []

            for door in self.doors:
                min_door_x = min(door.subBlocks, key=lambda cell: cell[0])[0]
                min_door_y = min(door.subBlocks, key=lambda cell: cell[1])[1]
                max_door_x = max(door.subBlocks, key=lambda cell: cell[0])[0]
                max_door_y = max(door.subBlocks, key=lambda cell: cell[1])[1]

                # Chac chắn rằng cửa cùng màu có thể chứa block
                if door.color == block.color and (max_x - min_x <= max_door_x - min_door_x or max_y - min_y <= max_door_y - min_door_y):
                    if block.special == Special_Block.HEART.value:
                        if door.special == Special_Door.HEART.value:
                            doors.append(door)
                    else:
                        doors.append(door)
                
            if doors:
                # Chỉ tính với door đầu tiên để tăng tốc
                door = doors[0]
                min_dist = min(
                    abs(bc[0] - dc[0]) + abs(bc[1] - dc[1])
                    for bc in block.subBlocks for dc in door.subBlocks
                )
            
            total_score += min_dist if min_dist != float('inf') else 1000
        
        # Penalty đơn giản
        total_score += len(self.blocks) * 100
        total_score -= len(self.can_eat_dfs()) * 200
        
        return total_score