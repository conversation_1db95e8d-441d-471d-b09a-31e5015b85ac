import gym
import numpy as np
from stable_baselines3.common.torch_layers import BaseFeaturesExtractor
import torch
import torch.nn as nn

from ..game.const import Special_Block, Special_Door
from ..game.game_state_base import GameStateBase


class SmallMapCNN(BaseFeaturesExtractor):
    def __init__(self, observation_space, features_dim=256):
        super().__init__(observation_space, features_dim)
        n_input_channels = observation_space.shape[0]  # số channel (C)
        self.cnn = nn.Sequential(
            nn.Conv2d(n_input_channels, 32, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.Conv2d(32, 64, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.Flatten()
        )
        with torch.no_grad():
            sample_input = torch.zeros(1, *observation_space.shape)
            n_flatten = self.cnn(sample_input).shape[1]
        self.linear = nn.Sequential(nn.Linear(n_flatten, features_dim), nn.ReLU())

    def forward(self, obs):
        return self.linear(self.cnn(obs))


# ==== PuzzleEnv với multi-channel encode ====
class PuzzleEnv(gym.Env):
    def __init__(self, level_path, debug=False):
        super().__init__()
        self.level_path = level_path
        self.state = GameStateBase(level_path)
        self.action_space = gym.spaces.MultiDiscrete([len(self.state.blocks), 4])
        self.debug = debug

        # Kích thước cố định
        self.W, self.H = self.state.size_x, self.state.size_y
        self.C = 11  # số channel encode
        self.observation_space = gym.spaces.Box(
            low=0, high=1, shape=(self.C, self.W, self.H), dtype=np.float32
        )


    def reset(self):
        self.state = GameStateBase(self.level_path)
        if self.debug:
            self.state.visualize_state()

        edible_blocks = self.state.can_eat()
        for b in edible_blocks:
            self.state.eat_block(b)
        return self._encode_state()

    def _encode_state(self):
        obs = np.zeros((self.C, self.W, self.H), dtype=np.float32)

        # Blocks
        for block in self.state.blocks:
            if block.special == Special_Block.NORMAL.value:
                ch = 0
            elif block.special == Special_Block.ICE.value:
                ch = 1
            elif block.special == Special_Block.LOCK.value:
                ch = 2
            elif block.special == Special_Block.LAYER.value:
                ch = 3
            elif block.special == Special_Block.HEART.value:
                ch = 4
            else:
                continue
            for x, y in block.subBlocks:
                x, y = x + 1, y + 1
                if 0 <= x < self.W and 0 <= y < self.H:
                    obs[ch, x, y] = 1

        # Walls
        for wall in self.state.walls:
            for x, y in wall.subBlocks:
                x, y = x + 1, y + 1
                if 0 <= x < self.W and 0 <= y < self.H:
                    obs[5, x, y] = 1

        # Doors
        for door in self.state.doors:
            if door.special == Special_Door.NORMAL.value:
                ch = 6
            elif door.special == Special_Door.ICE.value:
                ch = 7
            elif door.special == Special_Door.SHUTTER.value:
                ch = 8
            elif door.special == Special_Door.HEART.value:
                ch = 9
            else:
                continue
            for x, y in door.subBlocks:
                x, y = x + 1, y + 1
                if 0 <= x < self.W and 0 <= y < self.H:
                    obs[ch, x, y] = 1

        # Special tiles (gộp chung)
        for tile in self.state.special_tiles:
            x, y = tile.x + 1, tile.y + 1
            obs[10, tile.x, tile.y] = 1

        return obs

    def step(self, action):
        reward = 0
        block_index = int(action[0])
        direction_index = int(action[1])
        moves = [(0, -1), (0, 1), (-1, 0), (1, 0)]

        dx, dy = moves[direction_index]

        valid_blocks = [block.id for block in self.state.blocks]


        if block_index not in [block.id for block in self.state.blocks]:
            print(f"Invalid block index: {block_index}, Remaining blocks: {valid_blocks}")
            return self._encode_state(), reward, False, {}

        if self.state.is_valid_move(self.state.find_block(block_index), dx, dy):
            self.state.move_block(self.state.find_block(block_index), dx, dy)
            print(f"Move block index: {block_index}, Direction {direction_index}, Remaining blocks: {valid_blocks}")
        else:
            return self._encode_state(), reward, False, {}

        edible_blocks = self.state.can_eat()
        for b in edible_blocks:
            if self.state.eat_block(b):
                reward += 5
                print(f"Eating block index: {b.id}, Remaining blocks: {valid_blocks}")
                if self.debug:
                    self.state.visualize_state()

        done = False
        if self.state.is_win():
            reward += 50000.0
            done = True

        return self._encode_state(), reward, done, {}
