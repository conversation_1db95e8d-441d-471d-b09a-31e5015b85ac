from queue import PriorityQueue

from ..game.game_state_base import GameStateBase as GameState
from .heuristic import Heuristic


class BaseBlockHeuristic(Heuristic):
    def __init__(self):
        super().__init__()

    @staticmethod
    def calculate_score(game : GameState, block_id : int) -> float:
        """Calculate the score for moving a block to a door."""

        block = game.find_block(block_id)
        if block is None:
            return max(game.size_x, game.size_y) * 1000
            
        occupied = set(game.blocks_occupied)
        for cell in block.subBlocks:
            if cell in occupied:
                occupied.remove(cell)

        occupied_temp = set(game.doors_occupied)
        occupied_temp.update(game.walls_occupied)

        for special_tile in game.special_tiles:
            if (special_tile.special == 2 and special_tile.color != block.color):
                if (special_tile.x, special_tile.y) not in occupied:
                    occupied_temp.add((special_tile.x, special_tile.y))

        for door in game.get_accessible_doors(block):
            for cell in door.subBlocks:
                if cell in occupied_temp:
                    occupied_temp.remove(cell)

        occupied_ice_block = set()
        for bl in game.blocks:
            if not game.can_move_block(bl):
                occupied_ice_block.update(bl.subBlocks)

        pq = PriorityQueue()

        # score = max(self.size_x, self.size_y) * 1000
        visited = set()
        pq.put((0, (0, 0)))
        while not pq.empty():
            dis, current = pq.get()
            visited.add(current)
            (ndx, ndy) = current
            #(current[0] - block.subBlocks[0][0], current[1] - block.subBlocks[0][1])
            # current_cells = [(x + dx, y + dy) for (x, y) in block.subBlocks]

            for (dx, dy) in block.directions.values():
                delta = 0
                
                if (ndx + dx, ndy + dy) in visited:
                    continue
                
                moved_cells = [(x + ndx + dx, y + ndy + dy) for (x, y) in block.subBlocks]

                if any(cell in occupied_temp for cell in moved_cells):
                    continue
                    
                if any(cell in occupied_ice_block for cell in moved_cells):
                    continue

                for cell in moved_cells:
                    if cell in occupied:
                      delta += 1  

                # Kiểm tra xem block có ra khỏi board chưa
                if all(
                    cell[0] < 0 or cell[0] >= game.size_x or cell[1] < 0 or cell[1] >= game.size_y
                    for cell in moved_cells
                ):
                    # score = min(score, dis + delta)
                    return dis + delta
                
                pq.put((dis + delta, (ndx + dx, ndy + dy)))    
                visited.add((ndx + dx, ndy + dy))
            
        return max(game.size_x, game.size_y) * 1000
        

    def heuristic_base_block(self, game : GameState, block_id : int) -> float:
        """Custom heuristic function to estimate the cost to reach the goal state."""
        block = game.find_block(block_id)

        if block is None:
            return max(game.size_x, game.size_y) * 1000
        
        if not game.can_move_block(block):
            return max(game.size_x, game.size_y) * 1000

        min_distance = max(game.size_x, game.size_y) * 1000

        min_distance = min(min_distance, self.calculate_score(game=game, block_id=block_id))
    
        return min_distance

    def run(self):
        pass