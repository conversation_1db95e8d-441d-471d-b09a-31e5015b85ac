from .heuristic import Heuristic
from ..game.game_state_medium_path import Game<PERSON><PERSON>
from ..game.objects import Block
from collections import deque
from typing import Set, Tuple, Optional
from collections import defaultdict
import numpy as np
from numba import njit


@njit
def numba_check_bounds(x: int, y: int, size_x: int, size_y: int) -> bool:
    """Numba optimized bounds checking"""
    return 0 <= x < size_x and 0 <= y < size_y

@njit
def numba_manhattan_distance(x1: int, y1: int, x2: int, y2: int) -> int:
    """Numba optimized Manhattan distance calculation"""
    return abs(x1 - x2) + abs(y1 - y2)

@njit
def numba_check_cell_in_pathway(cell_x: int, cell_y: int, pathway_array: np.ndarray) -> bool:
    """Numba optimized pathway checking"""
    for i in range(pathway_array.shape[0]):
        if pathway_array[i, 0] == cell_x and pathway_array[i, 1] == cell_y:
            return True
    return False

@njit
def numba_calculate_blocking_cost(block_cells: np.ndarray, pathway_array: np.ndarray) -> float:
    """Calculate blocking cost using numba for speed"""
    if pathway_array.shape[0] == 0:
        return 0.0
    
    blocking_count = 0
    min_distance = 999999
    
    for i in range(block_cells.shape[0]):
        cell_x, cell_y = block_cells[i, 0], block_cells[i, 1]
        
        # Check if cell is in pathway
        if numba_check_cell_in_pathway(cell_x, cell_y, pathway_array):
            blocking_count += 1
        
        # Find minimum distance to pathway
        for j in range(pathway_array.shape[0]):
            dist = numba_manhattan_distance(cell_x, cell_y, pathway_array[j, 0], pathway_array[j, 1])
            if dist < min_distance:
                min_distance = dist
    
    if blocking_count == 0:
        return float(min_distance)
    
    # Weight blocking heavily but not too much to avoid overflow
    return min(blocking_count * 50.0 + min_distance, 10000.0)

class CleanCostHeuristic(Heuristic):
    def __init__(self, game: Optional[GameState] = None):
        super().__init__()
        self.pathways = defaultdict(dict)
        
        if game is None:
            return

        for block in game.blocks:
            # Làm sạch map
            # print("Cleaning map for block:", block.id)
            game_clean = game.copy()
            for eat_block in game_clean.blocks[:]:
                if eat_block.id != block.id and game_clean.can_move_block(eat_block):
                    while game_clean.find_block(eat_block.id):
                        game_clean.eat_block(eat_block)
            # game_clean.visualize_state()
            # game_clean.visualize_occupied()

            # Tính pathway ở vị trí hiện tại
            anchor = tuple(block.subBlocks[0])
            pathway = self.get_pathway(game_clean, block)
            self.pathways[block.id][anchor] = pathway
            
            # Tính pathway ở các vị trí khác
            visited = set()
            valid_moves = game_clean.get_valid_moves(block, (game.size_x + game.size_y + 4))
            for move in valid_moves:
                block_to_move = game_clean.find_block(block.id)
                if block_to_move is not None and (move[0], move[1]) not in visited:
                    game_clean.move_block(block_to_move, move[0], move[1])
                    visited.add((move[0], move[1]))
                    pathway = self.get_pathway(game_clean, block_to_move)
                    self.pathways[block.id][tuple(block_to_move.subBlocks[0])] = pathway
                    game_clean.move_block(block_to_move, -move[0], -move[1])

    @staticmethod
    def get_pathway_direction(game: GameState, block: Block):
        game = game.copy()
        found_block = game.find_block(block.id)
        if not found_block:
            return []
        if not game.can_move_block(found_block):
            return []

        occupied = set(game.doors_occupied) | set(game.walls_occupied)
        
        special_tiles_occupied = {
            (tile.x, tile.y) for tile in game.special_tiles 
            if tile.special == 2 and tile.color != found_block.color
        }
        occupied.update(special_tiles_occupied)

        if game.can_cook(found_block):
            return []
        
        # game.visualize_state()

        queue = deque([((0, 0), [])])
        visited = set()
        # visited.add(tuple(found_block.subBlocks))
        
        while queue:
            current, path = queue.popleft()
            (ndx, ndy) = current
            
            for direction, (dx, dy) in found_block.directions.items():
                if (ndx + dx, ndy + dy) in visited:
                    continue
                
                new_blocks = tuple((x + ndx + dx, y + ndy + dy) for (x, y) in found_block.subBlocks)
                
                if any(cell in occupied for cell in new_blocks):
                    continue

                if not all(numba_check_bounds(x, y, game.size_x, game.size_y) for x, y in new_blocks):
                    continue
                
                new_path = path + [(dx, dy)]
                
                game.move_block(found_block, ndx + dx, ndy + dy)    
                
                if game.can_cook(found_block):
                    return new_path
                
                game.move_block(found_block, -ndx -dx, -ndy -dy)  
                
                visited.add((ndx + dx, ndy + dy))
                queue.append(((ndx + dx, ndy + dy), new_path))
        return []
    
    @staticmethod
    def get_pathway(state: GameState, block: Block) -> Set[Tuple[int, int]]:
        state = state.copy()
        target_block = state.find_block(block.id)
        if not target_block:
            return set()

        for b in state.blocks[:]:
            if b.id == block.id:
                continue
            while state.find_block(b.id):
                state.eat_block(b)
                
        # state.visualize_state()

        pathway = CleanCostHeuristic.get_pathway_direction(state, target_block)
        result_occupied = set()
        
        accessible_doors = state.get_accessible_doors(target_block)
        
        if not accessible_doors:
            print(f"Ếu có matching door đâu block id: {block.id}")
            return set()
        
        # Đi theo pathway
        for dx_step, dy_step in pathway:
            state.move_block(block=target_block, dx=dx_step, dy=dy_step, time=1)
            result_occupied.update(target_block.subBlocks)
                
        # state.visualize_state()
        for matching_door in accessible_doors:
            for direction_x, direction_y in block.directions.values():
                for step in range(state.size_x + state.size_y + 2):
                    # Tối ưu với any() và generator expression
                    if any((sub[0] + direction_x * step, sub[1] + direction_y * step) in matching_door.subBlocks 
                            for sub in target_block.subBlocks):
                        # Apply moves at once
                        for _ in range(step):
                            state.move_block(target_block, direction_x, direction_y)
                            # state.visualize_state()
                            result_occupied.update(target_block.subBlocks)
                        result_occupied.difference_update(block.subBlocks, matching_door.subBlocks)
                        return result_occupied
        
        state.visualize_state()
        print(f"No matching door found for the block id {block.id}.")
        return set()

        # state.blocks_occupied.update(result_occupied)
        # state.visualize_occupied()

    @staticmethod
    def get_blocking_block(state: GameState, pathway: Set[Tuple[int, int]]) -> Set[Block]:
        """Tối ưu với set comprehension và generator expression"""
        return {
            block for block in state.blocks 
            if any(cell in pathway for cell in block.subBlocks)
        }

    @staticmethod
    def clean_cost_each_block(
        game: GameState, 
        blocker: Block, 
        pathway: set,
    ) -> float:
        if not any(cell in pathway for cell in blocker.subBlocks):
            return 0.0

        if game.find_block(blocker.id) is None:
            return 0.0

        if not game.can_move_block(blocker):
            return len(game.blocks) * 1000

        # Convert to numpy arrays for numba processing
        block_cells = np.array(list(blocker.subBlocks), dtype=np.int32)
        pathway_array = np.array(list(pathway), dtype=np.int32) if pathway else np.empty((0, 2), dtype=np.int32)
        
        # Use numba for fast calculation
        return numba_calculate_blocking_cost(block_cells, pathway_array)

    def calculate(self, state: GameState, block_id: int) -> float:
        block_target = state.find_block(block_id)
        
        if block_target is None:
            return 0.0
        
        if not state.can_move_block(block_target):
            # print(f"WARMING: Không thể di chuyển BLOCK ID: {block_target.id}")
            return float('inf')
        
        if not state.get_accessible_doors(block_target):
            # print(f"WARMING: Không có accessible doors cho BLOCK ID: {block_target.id}")
            return float('inf')

        block_anchor = tuple(block_target.subBlocks[0])
        if block_id not in self.pathways:
            print(f"WARNING: Không có pathway cho BLOCK ID {block_id}")
            return float('inf')

        if block_anchor not in self.pathways[block_id]:
            print(f"WARNING: Không tìm thấy anchor {block_anchor} cho BLOCK ID {block_id}")
            return float('inf')

        pathway = self.pathways[block_id][block_anchor]

        if len(pathway) < 1:
            print(f"WARNING: Không có pathway cho BLOCK ID {block_id} với anchor {block_anchor}")
            return float('inf')

        blocking_blocks = CleanCostHeuristic.get_blocking_block(state, pathway)
        
        if not blocking_blocks:
            print(f"WARNING: Không có blocking blocks cho BLOCK ID {block_id} nhưng có PATHWAY: {pathway}")
            return 0.0
        
        # Tối ưu với sum() và generator expression
        return sum(
            CleanCostHeuristic.clean_cost_each_block(state, block, pathway)
            for block in blocking_blocks
        )

    def clear_cache(self):
        """Clear the pathway cache to free memory"""
        self.pathways.clear()
    
    def restart(self, state: GameState):
        self.clear_cache()
        self.__init__(state)
        
    def run(self):
        pass