from fastapi import HTTPException

from backend.services.file_service import file_service


# File service endpoints (delegate to file_service)
async def get_video(task_id: str):
    try:
        return await file_service.get_video(task_id)
    except Exception as e:
        raise HTTPException(status_code=404, detail=f"Video not found: {str(e)}")


async def get_zip(task_id: str):
    try:
        return await file_service.get_zip(task_id)
    except Exception as e:
        raise HTTPException(status_code=404, detail=f"Zip not found: {str(e)}")


async def get_map_image(task_id: str):
    try:
        return await file_service.get_map_image(task_id)
    except Exception as e:
        raise HTTPException(status_code=404, detail=f"Map image not found: {str(e)}")


async def get_map_json(task_id: str):
    try:
        return await file_service.get_map_json(task_id)
    except Exception as e:
        raise HTTPException(status_code=404, detail=f"Map JSON not found: {str(e)}")


async def cleanup_task(task_id: str):
    try:
        return await file_service.cleanup_task(task_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Cleanup failed: {str(e)}")