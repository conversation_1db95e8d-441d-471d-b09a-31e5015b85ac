import streamlit as st


def UserManual():
    st.info("👆 Hãy upload một file JSON level để bắt đầu!")

    # Hiển thị hướng dẫn
    with st.expander("📖 Hướng dẫn sử dụng"):
        st.markdown("""
        ### G<PERSON><PERSON>i Puzzle - 5 Chế độ:
        
        **1. <PERSON><PERSON><PERSON>ra <PERSON>han<PERSON> (Random):**
        - Chỉ check xem level có giải được không
        - Hiển thị số moves nếu giải được
        - <PERSON><PERSON><PERSON> chóng (< 30s), không tạo video
        
        **2. G<PERSON><PERSON>i tối ưu (Heuristic):**
        - Tìm path ngắn nhất có thể
        - Tạo video demo chi tiết + file zip
        
        **3. Kiểm tra + Tối ưu:**
        - Check nhanh trước, sau đó tối ưu
        - An toàn nhất, so sánh hiệu quả
        
        **4. Giải Shortest Path:**  
        - Thuật toán Medium Path Solver
        - Path với độ dài trung bình, tối ưu hợp lý
        - Video demo chi tiết + file zip
        
        **5. <PERSON><PERSON><PERSON><PERSON> theo thứ tự Block:**  
        - <PERSON><PERSON><PERSON><PERSON> puzzle theo thứ tự blocks được chỉ định
        - Nhập danh sách ID blocks (ví dụ: 1,2,3,4)
        - Hữu ích cho strategic solving
        - Video demo chi tiết + file zip
        
        ### Sinh Map Mới:
        - Tạo map variant từ map gốc
        - Đổi màu blocks/doors ngẫu nhiên  
        - Thêm blocks mới và shuffle vị trí
        - Đảm bảo map sinh ra có thể giải được
        - Download JSON và ảnh map mới
        
        ### Hướng dẫn:
        1. **Upload file JSON**: Chọn file level
        2. **Chọn chức năng**: Giải puzzle hoặc sinh map
        3. **Chọn thuật toán**: (nếu giải puzzle)
        4. **Nhập thứ tự blocks**: (nếu chọn mode "Giải theo thứ tự Block")
        5. **Bắt đầu**: Nhấn nút và đợi kết quả
        6. **Download**: Files kết quả
        """)
