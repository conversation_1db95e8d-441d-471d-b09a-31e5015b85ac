import copy
import itertools
import logging
import math
from queue import PriorityQueue
from typing import List, Optional, Tuple

import numpy as np

from ..game.game_state_base import GameStateBase as GameState
from ..game.visualization.visualize_game_state import visualize_state
from ..heuristic.block_based_heuristic import BaseBlockHeuristic
from .config import ExperimentConfig, SolverMetrics


class PuzzleSolver:
    def __init__(
        self,
        config: ExperimentConfig,
        logger: Optional[logging.Logger] = None,
        heuristic: Optional[BaseBlockHeuristic] = None,
    ):
        self.config = config
        self.logger = logger or self._setup_logger()
        self.heuristic = heuristic
        self._reset()

    def _setup_logger(self) -> logging.Logger:
        logger = logging.getLogger(f"{__name__}.CheckSolvableService")
        if not logger.handlers:
            handler = logging.StreamHandler()
            handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - %(message)s"))
            logger.addHandler(handler)
        logger.setLevel(getattr(logging, self.config.log_level.upper()))
        return logger

    def _reset(self):
        self.counter = itertools.count()
        self.metrics = SolverMetrics()
        self.best_blocks = float("inf")
        self.unsolvable = False
        self.path = []

    def check_solvable(self, state: GameState) -> Tuple[Optional[List], dict]:
        """Randomize multiple trials to check solvability."""
        self.logger.debug(f"Starting solver: {len(state.blocks)} blocks")
        trials = 0
        self.min_block_for_stagnant = math.floor(math.sqrt(len(state.blocks)))

        while trials < self.config.max_trials:
            self._reset()
            trial_state = copy.deepcopy(state)
            while self.metrics.shuffle_count < self.config.max_shuffle:
                if self.config.debug_viz:
                    # trial_state.visualize_state()
                    visualize_state(trial_state)
                previous_num_blocks = len(trial_state.blocks)

                if trials <= self.config.max_trials // 2:
                    trial_state, shuffle_path = self.shuffle_all_blocks(trial_state)
                else:
                    trial_state, shuffle_path = self.shuffle_blocks(trial_state)

                self.metrics.shuffle_count += 1
                self.metrics.shuffle_moves += len(shuffle_path)
                self.path.extend(shuffle_path)

                if trial_state.is_win():
                    self.logger.debug(
                        f"✅ Solved in trial {trials + 1}, shuffles: {self.metrics.shuffle_count}"
                    )
                    return self.path, self.metrics.to_dict()

                if self.unsolvable:
                    return None, self.metrics.to_dict()

                if len(trial_state.blocks) == previous_num_blocks:
                    break

            trials += 1

        self.logger.debug("❌ Not solvable within trials/shuffles limit")
        return None, self.metrics.to_dict()

    def lookahead_expand_unique_block_base(
        self, state: GameState, depth, path, visited, id, block_id=-1
    ):
        """Trả về list (score, path, state) của các leaf sau lookahead DFS"""
        if depth == 0 or state.is_win():
            return [(self.heuristic.heuristic_base_block(state, id), path, state)]

        results = []

        can_eat_blocks = state.can_eat()
        if can_eat_blocks:
            for block in can_eat_blocks:
                new_state = copy.deepcopy(state)
                new_state.eat_block(new_state.find_block(block.id))
                new_path = path + [("eat", block.id)]
                results.extend(
                    self.lookahead_expand_unique_block_base(
                        new_state, depth - 1, new_path, visited, id
                    )
                )
        else:
            permuted_range = np.random.permutation(len(state.blocks))
            for index in permuted_range:
                if block_id == state.blocks[index].id:
                    continue

                for dx, dy in state.get_valid_moves(state.blocks[index], max_step=4):
                    new_state = copy.deepcopy(state)
                    if new_state.move_block(new_state.blocks[index], dx, dy):
                        sig = new_state.to_hashable()
                        if (id, sig) not in visited:
                            new_path = path + [(new_state.blocks[index].id, dx, dy)]
                            results.extend(
                                self.lookahead_expand_unique_block_base(
                                    new_state,
                                    depth - 1,
                                    new_path,
                                    visited,
                                    id,
                                    block_id=new_state.blocks[index].id,
                                )
                            )

        return results

    def lookahead_expand_unique_block_base_order(
        self, state: GameState, depth, path, visited, id, block_id=-1
    ):
        """Trả về list (score, path, state) của các leaf sau lookahead DFS"""
        if depth == 0 or state.is_win():
            return [(self.heuristic.heuristic_base_block(state, id), path, state)]

        results = []

        can_eat_blocks = state.can_eat()
        if can_eat_blocks:
            for block in can_eat_blocks:
                if block.id != id:
                    continue

                new_state = copy.deepcopy(state)
                new_state.eat_block(new_state.find_block(block.id))
                new_path = path + [("eat", block.id)]
                # results.extend(self.lookahead_expand_unique_block_base_order(new_state, depth-1, new_path, visited, id))
                results.append((0, new_path, new_state))
                return results

        permuted_range = np.random.permutation(len(state.blocks))
        for index in permuted_range:
            if block_id == state.blocks[index].id:
                continue

            for dx, dy in state.get_valid_moves(state.blocks[index], max_step=8):
                new_state = copy.deepcopy(state)
                if new_state.move_block(new_state.blocks[index], dx, dy):
                    sig = new_state.to_hashable()
                    if depth == 2 or (id, sig) not in visited:
                        new_path = path + [(new_state.blocks[index].id, dx, dy)]
                        results.extend(
                            self.lookahead_expand_unique_block_base_order(
                                new_state,
                                depth - 1,
                                new_path,
                                visited,
                                id,
                                block_id=new_state.blocks[index].id,
                            )
                        )

        return results

    def solve_custom_base_on_unique_block(
        self,
        init_state: GameState,
        lookahead_depth=2,
        path=[],
        block_id=-1,
        iteration_init=0,
        counter=itertools.count(),
        visited=set(),
        check_every=1,
        patience=10,
        max_iter=100000,
    ):
        pq = PriorityQueue()
        iteration = iteration_init

        best_remaining_blocks = len(init_state.blocks)
        num_iter = 0

        if block_id == -1:
            current_state = copy.deepcopy(init_state)
            while True:
                list_block = current_state.blocks
                for block in list_block:
                    if current_state.find_block(block.id) is None:
                        continue

                    if not current_state.can_move_block(current_state.find_block(block.id)):
                        continue

                    # if self.heuristic.heuristic_base_block(current_state, block.id) >= 5000:
                    #     continue

                    path, current_state, visited, iteration = (
                        self.solve_custom_base_on_unique_block(
                            current_state,
                            path=path,
                            block_id=block.id,
                            iteration_init=iteration,
                            visited=visited,
                            check_every=check_every,
                            patience=patience,
                            max_iter=max_iter,
                        )
                    )

                    can_eat_blocks = current_state.can_eat()
                    if can_eat_blocks:
                        for block in can_eat_blocks:
                            current_state.eat_block(block)
                            path += [("eat", block.id)]

                    if current_state.is_win():
                        return path, current_state, visited, iteration

                if current_state.is_win():
                    visited.clear()
                    return path, current_state, visited, iteration

                if iteration >= max_iter:
                    # current_state.visualize_state()
                    return None, current_state, visited, iteration

        else:
            pq.put((len(init_state.blocks), 0, block_id, next(counter), init_state, path))

        visited.add((block_id, init_state.to_hashable()))

        last_num_block = 0
        last_score = 0
        while not pq.empty():
            num_block, score, block_id, _, current_state, path = pq.get()
            iteration += 1

            # Check win
            if current_state.is_win():
                return path, current_state, visited, iteration

            # Cập nhật best_remaining_blocks
            if num_block < best_remaining_blocks:
                best_remaining_blocks = num_block

            # Nếu số block đang chưa bằng tối ưu thì bỏ qua
            if num_block > best_remaining_blocks:
                continue

            if last_num_block == num_block and last_score == score:
                num_iter += 1
            else:
                num_iter = 0
                last_num_block = num_block
                last_score = score

            if num_iter > patience:
                return path, current_state, visited, iteration

            if iteration >= max_iter:
                return [], current_state, visited, iteration

            # Lookahead
            results = self.lookahead_expand_unique_block_base(
                current_state, lookahead_depth, path, visited, block_id
            )

            if self.config.debug_viz:
                if iteration % check_every == 0:
                    self.logger.debug(
                        f"Iter {iteration}, Blocks: {num_block}, "
                        f"Heuristic: {score}, Depth: {lookahead_depth} Block ID: {block_id} Len : {len(results)}"
                    )
                    if self.config.debug_img:
                        # current_state.visualize_state()
                        visualize_state(current_state)

            for score_leaf, new_path, new_state in results:
                sig = new_state.to_hashable()
                if (block_id, sig) not in visited:
                    visited.add((block_id, sig))
                    if new_state.find_block(block_id) is None:
                        return self.solve_custom_base_on_unique_block(
                            new_state,
                            lookahead_depth=2,
                            block_id=-1,
                            iteration_init=iteration,
                            path=new_path,
                            check_every=check_every,
                            patience=patience,
                            max_iter=max_iter,
                        )

                    pq.put(
                        (
                            len(new_state.blocks),
                            score_leaf,
                            block_id,
                            next(counter),
                            new_state,
                            new_path,
                        )
                    )

        return path, current_state, visited, iteration

    def solve(self, initial_state: GameState):
        """Main solving function that checks solvability."""
        # self.logger.debug("Starting to solve the puzzle")
        path, current_state, visited, iteration = self.solve_custom_base_on_unique_block(
            initial_state,
            lookahead_depth=self.config.lookahead_depth,
            check_every=self.config.check_every,
            patience=self.config.patience,
            max_iter=self.config.max_iter,
        )

        del visited  # Clear visited to free memory
        del current_state  # Clear current_state to free memorys

        return path

    def solve_custom_order(
        self,
        init_state: GameState,
        lookahead_depth=2,
        path=[],
        block_id=-1,
        iteration_init=0,
        counter=itertools.count(),
        visited=set(),
        check_every=1,
        patience=10,
        max_iter=100000,
        order=[],
    ):
        pq = PriorityQueue()
        iteration = iteration_init

        best_remaining_blocks = len(init_state.blocks)
        num_iter = 0

        if block_id == -1:
            current_state = copy.deepcopy(init_state)
            # while True:
            # list_block = current_state.blocks
            for id in order:
                block = current_state.find_block(id)
                if block is None:
                    continue

                if not current_state.can_move_block(current_state.find_block(block.id)):
                    continue

                # if self.heuristic.heuristic_base_block(current_state, block.id) >= 5000:
                #     continue

                path, current_state, visited, iteration = self.solve_custom_order(
                    current_state,
                    path=path,
                    lookahead_depth=lookahead_depth,
                    block_id=block.id,
                    iteration_init=iteration,
                    visited=visited,
                    check_every=check_every,
                    patience=patience,
                    max_iter=max_iter,
                    order=order,
                )

                can_eat_blocks = current_state.can_eat()
                if can_eat_blocks:
                    for bl in can_eat_blocks:
                        if bl.id != id:
                            continue
                        current_state.eat_block(bl)
                        path += [("eat", bl.id)]
                        visited.clear()  # Clear visited set to avoid re-visiting states after eating blocks

                if current_state.is_win():
                    visited.clear()
                    return path, current_state, visited, iteration

            if current_state.is_win():
                return path, current_state, visited, iteration

            if iteration >= max_iter:
                # current_state.visualize_state()
                return None, current_state, visited, iteration

        else:
            pq.put((len(init_state.blocks), 0, block_id, next(counter), init_state, path))

        last_num_block = 0
        last_score = 0
        while not pq.empty():
            num_block, score, block_id, _, current_state, path = pq.get()
            visited.add((block_id, current_state.to_hashable()))
            iteration += 1

            # Check win
            if current_state.is_win():
                return path, current_state, visited, iteration

            # Cập nhật best_remaining_blocks
            if num_block < best_remaining_blocks:
                best_remaining_blocks = num_block

            # Nếu số block đang chưa bằng tối ưu thì bỏ qua
            if num_block > best_remaining_blocks:
                continue

            if last_num_block == num_block and last_score == score:
                num_iter += 1
            else:
                num_iter = 0
                last_num_block = num_block
                last_score = score

            if num_iter > patience:
                return path, current_state, visited, iteration

            if iteration >= max_iter:
                return [], current_state, visited, iteration

            # Lookahead
            results = self.lookahead_expand_unique_block_base_order(
                current_state, lookahead_depth, path, visited, block_id
            )
            results = sorted(results, key=lambda x: (len(x[2].blocks), x[0]))  # Sort by score

            if self.config.debug_viz:
                if iteration % check_every == 0:
                    self.logger.debug(
                        f"Iter {iteration}, Blocks: {num_block}, "
                        f"Heuristic: {score}, Depth: {lookahead_depth} Block ID: {block_id} Len : {len(results)} Path: {len(path)}"
                    )
                    if self.config.debug_img:
                        # current_state.visualize_state()
                        visualize_state(current_state)

            for score_leaf, new_path, new_state in results:
                sig = new_state.to_hashable()
                if (block_id, sig) not in visited:
                    visited.add((block_id, sig))
                    if new_state.find_block(block_id) is None:
                        return self.solve_custom_order(
                            new_state,
                            lookahead_depth=lookahead_depth,
                            block_id=-1,
                            iteration_init=iteration,
                            path=new_path,
                            check_every=check_every,
                            patience=patience,
                            max_iter=max_iter,
                            order=order,
                        )

                    pq.put(
                        (
                            len(new_state.blocks),
                            score_leaf,
                            block_id,
                            next(counter),
                            new_state,
                            new_path,
                        )
                    )

        return path, current_state, visited, iteration

    def solve_next_block(
        self, init_state: GameState, lookahead_depth=2, block_id=-1, check_every=1, max_iter=100000
    ):
        pq = PriorityQueue()
        iteration = 0
        counter = itertools.count()
        visited = set()
        path = []

        best_remaining_blocks = len(init_state.blocks)

        pq.put((len(init_state.blocks), 0, block_id, next(counter), init_state, path))

        while not pq.empty():
            num_block, score, block_id, _, current_state, path = pq.get()
            visited.add(current_state.to_hashable())
            iteration += 1

            if current_state.find_block(block_id) is None:
                return path, current_state, iteration

            # Check win
            if current_state.is_win():
                return path, current_state, iteration

            # Cập nhật best_remaining_blocks
            if num_block < best_remaining_blocks:
                best_remaining_blocks = num_block

            # Nếu số block đang chưa bằng tối ưu thì bỏ qua
            if num_block > best_remaining_blocks:
                continue

            if iteration >= max_iter:
                return [], current_state, iteration

            # Lookahead
            results = self.lookahead_expand_unique_block_base_order(
                current_state, lookahead_depth, path, visited, block_id
            )
            results = sorted(results, key=lambda x: len(x[2].blocks))  # Sort by score

            if self.config.debug_viz:
                if iteration % check_every == 0:
                    self.logger.debug(
                        f"Iter {iteration}, Blocks: {num_block}, "
                        f"Heuristic: {score}, Depth: {lookahead_depth} Block ID: {block_id} Len : {len(results)} Path: {len(path)}"
                    )
                    if self.config.debug_img:
                        # current_state.visualize_state()
                        visualize_state(current_state)

            for score_leaf, new_path, new_state in results:
                sig = new_state.to_hashable()
                if sig not in visited:
                    visited.add(sig)
                    if new_state.find_block(block_id) is None:
                        return new_path, new_state, iteration

                    if new_state.can_exit(new_state.find_block(block_id)):
                        new_state.eat_block(new_state.find_block(block_id))
                        new_path = new_path + [("eat", block_id)]
                        return new_path, new_state, iteration

                    pq.put(
                        (
                            len(new_state.blocks),
                            score_leaf,
                            block_id,
                            next(counter),
                            new_state,
                            new_path,
                        )
                    )

        return None, current_state, iteration

    def solve_order(self, initial_state: GameState, order: List[int] = []):
        """Main solving function that checks solvability."""
        # self.logger.debug("Starting to solve the puzzle")
        path, current_state, visited, iteration = self.solve_custom_order(
            initial_state,
            lookahead_depth=self.config.lookahead_depth,
            check_every=self.config.check_every,
            patience=self.config.max_iter,
            max_iter=self.config.max_iter,
            order=order,
        )
        del visited  # Clear visited to free memory
        del current_state  # Clear current_state to free memorys

        return path

    def solve_unique_block(self, initial_state: GameState, block_id: int):
        """Main solving function that checks solvability."""
        path, current_state, iteration = self.solve_next_block(
            initial_state,
            lookahead_depth=self.config.lookahead_depth,
            check_every=self.config.check_every,
            max_iter=self.config.max_iter,
            block_id=block_id,
        )

        del current_state  # Clear current_state to free memorys

        return path

    ### Sol for instead of dfs ###
    def lookahead_expand_unique_block_base_for_custom(self, nstate: GameState, path, visited, id):
        """Trả về list (score, path, state) của các leaf sau lookahead DFS"""

        if nstate.is_win():
            return [(0, 0, path)]

        state = copy.deepcopy(nstate)
        # path = copy.deepcopy(npath)

        results = []
        can_eat_blocks = state.can_eat()
        if can_eat_blocks:
            for block in can_eat_blocks:
                state.eat_block(state.find_block(block.id))
                path = path + [("eat", block.id)]

            return [(len(state.blocks), 0, path)]

        new_path = path[:]
        for i in range(len(state.blocks)):
            for dx, dy in state.get_valid_moves(state.blocks[i], max_step=6):
                # new_state = copy.deepcopy(state)
                state.move_block(state.blocks[i], dx, dy)
                new_path = path + [(state.blocks[i].id, dx, dy)]

                can_eat_blocks = state.can_eat()
                if can_eat_blocks:
                    for block in can_eat_blocks:
                        state.eat_block(block)
                        new_path = new_path + [("eat", block.id)]
                    return [(len(state.blocks), 0, new_path)]

                for j in range(len(state.blocks)):
                    if i == j:
                        continue

                    for dx2, dy2 in state.get_valid_moves(state.blocks[j], max_step=6):
                        if state.move_block(state.blocks[j], dx2, dy2):
                            sig = state.to_hashable()
                            if (id, sig) not in visited:
                                # visited.add((id, sig))
                                new_path = new_path + [(state.blocks[j].id, dx2, dy2)]
                                append_path = new_path[:]
                                results.append(
                                    (
                                        len(state.blocks),
                                        self.heuristic.heuristic_base_block(state, id),
                                        append_path,
                                    )
                                )
                                new_path.pop()  # Remove the last move

                            state.move_block(state.blocks[j], -dx2, -dy2)  # Undo move

                new_path.pop()
                state.move_block(state.blocks[i], -dx, -dy)

        return results

    def solve_custom_base_on_unique_block_for_custom(
        self,
        init_state: GameState,
        path=[],
        block_id=-1,
        iteration_init=0,
        counter=itertools.count(),
        visited=set(),
        check_every=1,
        patience=10,
        max_iter=100000,
    ):
        pq = PriorityQueue()
        iteration = iteration_init

        best_remaining_blocks = len(init_state.blocks)
        num_iter = 0

        if block_id == -1:
            current_state = copy.deepcopy(init_state)
            while True:
                list_block = current_state.blocks
                list_block.sort(
                    key=lambda x: self.heuristic.heuristic_base_block(current_state, x.id)
                )
                for block in list_block:
                    if current_state.find_block(block.id) is None:
                        continue

                    if not current_state.can_move_block(current_state.find_block(block.id)):
                        continue

                    # if current_state.heuristic_base_block(block.id) > 5000:
                    #     continue

                    path, current_state, visited, iteration = (
                        self.solve_custom_base_on_unique_block_for_custom(
                            current_state,
                            path=path,
                            block_id=block.id,
                            iteration_init=iteration,
                            visited=visited,
                            check_every=check_every,
                            patience=patience,
                            max_iter=max_iter,
                        )
                    )

                    if path is None:
                        # current_state.visualize_state()
                        return None, current_state, visited, iteration

                    can_eat_blocks = current_state.can_eat()
                    if can_eat_blocks:
                        for bl in can_eat_blocks:
                            current_state.eat_block(bl)
                            path = path + [("eat", bl.id)]
                        visited.clear()  # Clear visited set to avoid re-visiting states after eating blocks

                    if current_state.is_win():
                        return path, current_state, visited, iteration

                if current_state.is_win():
                    return path, current_state, visited, iteration

                if iteration >= max_iter:
                    # current_state.visualize_state()
                    return None, current_state, visited, iteration

        else:
            pq.put((len(init_state.blocks), 0, block_id, next(counter), init_state, path))

        visited.add((block_id, init_state.to_hashable()))

        last_num_block = 0
        last_score = 0
        while not pq.empty():
            num_block, score, block_id, _, current_state, path = pq.get()
            iteration += 1

            # Check win
            if current_state.is_win():
                visited.clear()
                return path, current_state, visited, iteration

            # Cập nhật best_remaining_blocks
            if num_block < best_remaining_blocks:
                best_remaining_blocks = num_block

            # Nếu số block đang chưa bằng tối ưu thì bỏ qua
            if num_block > best_remaining_blocks:
                continue

            if last_num_block == num_block and last_score == score:
                num_iter += 1
            else:
                num_iter = 0
                last_num_block = num_block
                last_score = score

            if num_iter > patience:
                return path, current_state, visited, iteration

            if iteration >= max_iter:
                return [], current_state, visited, iteration

            # Lookahead
            results = self.lookahead_expand_unique_block_base_for_custom(
                current_state, copy.deepcopy(path), visited, block_id
            )
            results.sort(
                key=lambda x: (x[0], x[1])
            )  # Sort by number of blocks and then by heuristic score

            # Logging
            if self.config.debug_viz:
                if iteration % check_every == 0:
                    self.logger.debug(
                        f"Iter {iteration}, Blocks: {num_block}, "
                        f"Heuristic: {score}, Depth: {2} Block ID: {block_id} Len : {len(results)} Path: {len(path)}"
                    )
                    if self.config.debug_img:
                        # current_state.visualize_state()
                        visualize_state(current_state)

            for num_b, score_leaf, new_path in results:
                # print("quay xuoi")
                eat = False
                for i in range(len(path), len(new_path)):
                    if new_path[i][0] == "eat":
                        eat = True
                        current_state.eat_block(current_state.find_block(new_path[i][1]))
                        continue
                    id = new_path[i][0]
                    dx = new_path[i][1]
                    dy = new_path[i][2]
                    current_state.move_block(current_state.find_block(id), dx, dy)

                can_eat = current_state.can_eat()
                if can_eat:
                    for bl in can_eat:
                        current_state.eat_block(bl)
                        new_path = new_path + [("eat", bl.id)]
                        eat = True

                if eat:
                    del visited  # Clear visited set to avoid re-visiting states after eating blocks
                    return self.solve_custom_base_on_unique_block_for_custom(
                        current_state,
                        block_id=-1,
                        iteration_init=iteration,
                        path=new_path,
                        check_every=check_every,
                        patience=patience,
                        max_iter=max_iter,
                    )

                sig = current_state.to_hashable()
                if (block_id, sig) not in visited:
                    visited.add((block_id, sig))

                    pq.put(
                        (
                            len(current_state.blocks),
                            score_leaf,
                            block_id,
                            next(counter),
                            copy.deepcopy(current_state),
                            new_path,
                        )
                    )

                # print("quay nguoc " + str(len(new_path)) + str(len(path)))
                for i in range(len(path), len(new_path)):
                    j = len(new_path) - (i - len(path)) - 1
                    id = new_path[j][0]
                    dx = new_path[j][1]
                    dy = new_path[j][2]
                    current_state.move_block(current_state.find_block(id), -dx, -dy)

        return path, current_state, visited, iteration

    def solve_for_custom(self, initial_state: GameState):
        """Main solving function that checks solvability."""
        # self.logger.debug("Starting to solve the puzzle")
        path, current_state, visited, iteration = self.solve_custom_base_on_unique_block_for_custom(
            initial_state,
            check_every=self.config.check_every,
            patience=self.config.patience,
            max_iter=self.config.max_iter,
        )

        del visited  # Clear visited to free memory
        del current_state  # Clear current_state to free memorys

        return path
