import streamlit as st

from frontend.components.cancel_task_button import <PERSON><PERSON><PERSON>utton
from frontend.handlers.genmap_result_handler import GenMapResultHandler
from frontend.handlers.solve_result_handler import SolveResultHandler
from frontend.handlers.task_manager import task_manager
from frontend.handlers.task_poller import TaskPoller


def Col2(
    game, main_mode, order_input, solve_mode, auto_solve, auto_gen, uploaded_file, mode, num_blocks
):
    # Initialize handlers
    solve_result_handler = SolveResultHandler()
    genmap_result_handler = GenMapResultHandler()
    poller = TaskPoller()

    if main_mode == "solve":
        st.header("🎯 Kết quả giải")
        CancelButton()

        # <PERSON><PERSON><PERSON> tra nếu có task đang chạy, tiế<PERSON> tục poll
        if st.session_state.task_running and st.session_state.current_task_id:
            auto_solve = True
            task_id = st.session_state.current_task_id
            solve_mode = st.session_state.task_mode

        if auto_solve:
            # <PERSON><PERSON><PERSON> chưa có task đang chạy thì tạo task mới
            if not st.session_state.task_running:
                with st.spinner(f"Đang tạo task với mode: {solve_mode}..."):
                    success, result = task_manager.create_solve_task(
                        uploaded_file, solve_mode, order_input
                    )
                    if success:
                        st.rerun()
                    else:
                        st.error(result)
            else:
                # Poll kết quả
                task_id = st.session_state.current_task_id
                poller.poll_solve_task(
                    task_id, solve_mode, order_input, solve_result_handler, game, uploaded_file
                )

    elif main_mode == "gen_map":
        st.header("🗺️ Map mới được sinh")
        CancelButton()

        # Kiểm tra nếu có task đang chạy, tiếp tục poll
        if st.session_state.task_running and st.session_state.current_task_id:
            auto_gen = True
            task_id = st.session_state.current_task_id

        if auto_gen:
            # Nếu chưa có task đang chạy thì tạo task mới
            if not st.session_state.task_running:
                with st.spinner("Đang sinh map mới..."):
                    success, result = task_manager.create_gen_map_task(
                        uploaded_file, mode, num_blocks
                    )
                    if success:
                        st.rerun()
                    else:
                        st.error(result)
            else:
                # Poll kết quả
                task_id = st.session_state.current_task_id
                poller.poll_gen_map_task(task_id, genmap_result_handler, uploaded_file)
