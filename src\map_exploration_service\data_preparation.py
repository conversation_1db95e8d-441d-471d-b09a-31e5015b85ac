import json
from typing import List

import numpy as np
import pandas as pd

from ..check_solvable_service.api import check_solve
from ..game.const import Special_Block, Special_Door
from ..game.game_state_base import GameStateBase
from .utils import calculate_num_space, calculate_wall_bounder


def get_game_data(file_path_list: List[str]) -> pd.DataFrame:
    game_data = []
    for file_path in file_path_list:
        game_data.append(get_single_game_data(file_path))
    return pd.DataFrame(game_data)

def get_single_game_data(file_path: str):
    with open(file_path) as file:
        content = json.load(file)
        block_list = content['blockList']
        num_blocks = len(block_list)
        
        normal_blocks = [block for block in block_list if block['special'] == Special_Block.NORMAL.value]
        vector_blocks = [block for block in block_list if block['special'] == Special_Block.VECTOR.value]
        layer_blocks = [block for block in block_list if block['special'] == Special_Block.LAYER.value]
        heart_blocks = [block for block in block_list if block['special'] == Special_Block.HEART.value]
        ice_blocks = [block for block in block_list if block['special'] == Special_Block.ICE.value]
        bomb_blocks = [block for block in block_list if block['special'] == Special_Block.BOMB.value]
        lock_blocks = [block for block in block_list if block['special'] == Special_Block.LOCK.value]

        door_list = content['doorList']
        num_doors = len(door_list)

        normal_doors = [door for door in door_list if door['special'] == Special_Door.NORMAL.value]
        shutter_doors = [door for door in door_list if door['special'] == Special_Door.SHUTTER.value]
        heart_doors = [door for door in door_list if door['special'] == Special_Door.HEART.value]
        ice_doors = [door for door in door_list if door['special'] == Special_Door.ICE.value]

        unique_colors = len(set(block['color'] for block in block_list))
        time_limit = content.get('time')

        tile_paths = content['specialTileList']

        wall_list = content['wallList']


        game_state = GameStateBase(file_path)
        boundary_walls = calculate_wall_bounder(game_state)
        num_space = calculate_num_space(game_state)
        edible_blocks = game_state.can_eat()

        return {
            "time_limit": time_limit,
            "num_blocks": num_blocks,
            "early_edible_blocks": len(edible_blocks),
            "num_tile_path": len(tile_paths),
            "num_unique_colors": unique_colors,
            "obstacles": len(wall_list) - boundary_walls,
            "num_space": num_space,
            "num_normal_blocks": len(normal_blocks),
            "num_vector_blocks": len(vector_blocks),
            "num_layer_blocks": len(layer_blocks),
            "num_heart_blocks": len(heart_blocks),
            "num_ice_blocks": len(ice_blocks),
            "num_bomb_blocks": len(bomb_blocks),
            "num_lock_blocks": len(lock_blocks),
            "num_doors": num_doors,
            "num_normal_doors": len(normal_doors),
            "num_shutter_doors": len(shutter_doors),
            "num_heart_doors": len(heart_doors),
            "num_ice_doors": len(ice_doors)
        }

def get_solving_data(file_path_list: List[str], num_trials: int = 10, saved_file_dir: str = None) -> pd.DataFrame:
    if saved_file_dir:
        return pd.read_csv(saved_file_dir)
    else:
        solving_data = []
        for file_path in file_path_list:
            solving_data.append(get_single_solving_data(file_path=file_path, num_trials=num_trials))
        return pd.DataFrame(solving_data)

def get_single_solving_data(file_path: str, num_trials: int = 10):
    trial_count = 0
    metric_list = []

    while trial_count < num_trials:
        is_solved, metric = check_solve(file_path)
        if is_solved:
            eating_flag_list = []
            count = 0
            for route in metric.get("path", []):
                if route[0] == 'eat':
                    eating_flag_list.append(count)
                    count = 0
                else:
                    count += 1

            metric['avg_digest_block_move'] = np.mean(eating_flag_list)
            metric['std_digest_block_move'] = np.std(eating_flag_list)

            metric_list.append(metric)
            trial_count += 1

    metric_df = pd.DataFrame(metric_list)
    metric_df = metric_df.agg(
        avg_digest_block_move_avg=('avg_digest_block_move', 'mean'),
        std_digest_block_move_avg=('std_digest_block_move', 'mean'),
        avg_digest_block_move_std=('avg_digest_block_move', 'std'),
        std_digest_block_move_std=('std_digest_block_move', 'std'),
        
        avg_digest_block_move_min=('avg_digest_block_move', 'min'),
        avg_digest_block_move_max=('avg_digest_block_move', 'max'),

        avg_runtime_sec=('runtime_sec', 'mean'),
        med_runtime_sec=('runtime_sec', 'median'),
        std_runtime_sec=('runtime_sec', 'std'),
        min_runtime_sec=('runtime_sec', 'min'),
        max_runtime_sec=('runtime_sec', 'max'),

        avg_iteration=('iteration', 'mean'),
        med_iteration=('iteration', 'median'),
        std_iteration=('iteration', 'std'),
        min_iteration=('iteration', 'min'),
        max_iteration=('iteration', 'max'),

        avg_steps=('steps', 'mean'),
        med_steps=('steps', 'median'),
        std_steps=('steps', 'std'),
        min_steps=('steps', 'min'),
        max_steps=('steps', 'max'),
    ).reset_index()
    metric_data = metric_df.to_dict(orient='records')[0]

    return metric_data