# from src.check_solvable_service.api import get_next_block_exit_path
from src.game.game_state_base import GameStateB<PERSON> as GameState
from src.heuristic.block_based_heuristic import BaseBlockHeuristic
from src.solver_block_based_service.config import ExperimentConfig
from src.solver_block_based_service.solver import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def solve_base_block(state_path: str):
    config = ExperimentConfig()

    heuristic = BaseBlockHeuristic()
    solver = PuzzleSolver(config, heuristic=heuristic)
    initial_state = GameState(state_path)

    path = solver.solve_for_custom(initial_state)
    return path


def get_current_state(initial_state: GameState, current_state_route: list):
    for t in current_state_route:
        if t[0] == "eat":
            initial_state.eat_block(initial_state.find_block(t[1]))
        else:
            initial_state.move_block(initial_state.find_block(t[0]), t[1], t[2])


def solve_base_block_order(state_path: str, solution_order):
    config = ExperimentConfig()

    heuristic = BaseBlockHeuristic()
    solver = PuzzleSolver(config, heuristic=heuristic)
    initial_state = GameState(state_path)

    order = []
    if len(solution_order) > 0:
        order = []
        for t in solution_order:
            if t[0] == "eat":
                order.append(t[1])

    path = solver.solve_order(initial_state, order=order)
    return path


# def solve_next_block(state_path: str, config: ExperimentConfig = None):
#     if not config:
#         config = ExperimentConfig()

#     state = GameState(state_path)
#     heuristic = BaseBlockHeuristic()
#     solver = PuzzleSolver(config, heuristic=heuristic)
#     solution = []

#     while True:
#         if state.is_win():
#             return solution

#         min_next_path = float("inf")
#         next_block_id = -1
#         for i in range(20):
#             block_id, path = get_next_block_exit_path(
#                 state_path=state_path, current_state_route=solution
#             )
#             if len(path) < min_next_path:
#                 min_next_path = len(path)
#                 next_block_id = block_id

#         print(f"Next block ID: {next_block_id}, Path length: {min_next_path}")

#         path = solver.solve_unique_block(copy.deepcopy(state), next_block_id)
#         solution.extend(path)
#         get_current_state(state, path)

def solve_unique_block(state_path: str, path: list, block_id: int, config: ExperimentConfig = None):
    if not config:
        config = ExperimentConfig()

    state = GameState(state_path)
    get_current_state(state, path)
    heuristic = BaseBlockHeuristic()
    solver = PuzzleSolver(config, heuristic=heuristic)
    solution = solver.solve_unique_block(initial_state=state, block_id=block_id)
     
    return solution


def solve_order_block(state_path: str, order: list, config: ExperimentConfig = None):
    if not config:
        config = ExperimentConfig()

    state = GameState(state_path)
    heuristic = BaseBlockHeuristic()
    solver = PuzzleSolver(config, heuristic=heuristic)

    return solver.solve_order(state, order=order)
