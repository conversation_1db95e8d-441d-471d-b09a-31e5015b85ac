import os

from fastapi.responses import FileResponse, JSONResponse

from backend.core.config import config


class FileService:
    """Service for handling file downloads and cleanup"""

    @staticmethod
    async def get_video(task_id: str):
        """Get video file for task"""
        video_path = f"{config.TEMP_VIDEOS_DIR}/{task_id}/solution_video.mp4"
        if os.path.exists(video_path):
            return FileResponse(
                video_path, media_type="video/mp4", filename=f"{task_id}_solution.mp4"
            )
        else:
            return JSONResponse(status_code=404, content={"error": "Video not found"})

    @staticmethod
    async def get_zip(task_id: str):
        """Get zip file for task"""
        zip_path = f"{config.TEMP_VIDEOS_DIR}/{task_id}/solution_complete.zip"

        print(f"🔍 Looking for zip file: {zip_path}")
        if os.path.exists(f"{config.TEMP_VIDEOS_DIR}/{task_id}"):
            print(f"📁 Directory contents: {os.listdir(f'{config.TEMP_VIDEOS_DIR}/{task_id}')}")
        else:
            print("📁 Directory not found")

        if os.path.exists(zip_path):
            file_size = os.path.getsize(zip_path)
            print(f"✅ Found zip file: {zip_path} ({file_size} bytes)")
            return FileResponse(
                zip_path, media_type="application/zip", filename=f"{task_id}_complete.zip"
            )
        else:
            # Try fallback names
            fallback_paths = [
                f"{config.TEMP_VIDEOS_DIR}/{task_id}/solution_images.zip",
                f"{config.TEMP_VIDEOS_DIR}/{task_id}/solution.zip",
            ]

            for fallback_path in fallback_paths:
                if os.path.exists(fallback_path):
                    print(f"✅ Found fallback zip: {fallback_path}")
                    return FileResponse(
                        fallback_path,
                        media_type="application/zip",
                        filename=f"{task_id}_complete.zip",
                    )

            print(f"❌ No zip file found for task {task_id}")
            return JSONResponse(status_code=404, content={"error": "Zip file not found"})

    @staticmethod
    async def get_map_image(task_id: str):
        """Get generated map image"""
        img_path = f"{config.TEMP_VIDEOS_DIR}/{task_id}/generated_map.png"
        if os.path.exists(img_path):
            return FileResponse(
                img_path, media_type="image/png", filename=f"{task_id}_generated_map.png"
            )
        else:
            return JSONResponse(status_code=404, content={"error": "Map image not found"})

    @staticmethod
    async def get_map_json(task_id: str):
        """Get generated map JSON"""
        json_path = f"{config.TEMP_VIDEOS_DIR}/{task_id}/generated_map.json"
        if os.path.exists(json_path):
            return FileResponse(
                json_path, media_type="application/json", filename=f"{task_id}_generated_map.json"
            )
        else:
            return JSONResponse(status_code=404, content={"error": "Map JSON not found"})

    @staticmethod
    async def cleanup_task(task_id: str):
        """Cleanup task files"""
        import shutil

        cleanup_count = 0

        # Cleanup video directory
        video_dir = f"{config.TEMP_VIDEOS_DIR}/{task_id}"
        if os.path.exists(video_dir):
            shutil.rmtree(video_dir)
            cleanup_count += 1

        # Cleanup result files
        result_file = f"{config.TEMP_RESULTS_DIR}/{task_id}_result.json"
        if os.path.exists(result_file):
            os.unlink(result_file)
            cleanup_count += 1

        # Cleanup progress files
        progress_file = f"{config.TEMP_RESULTS_DIR}/{task_id}_progress.json"
        if os.path.exists(progress_file):
            os.unlink(progress_file)
            cleanup_count += 1

        # Cleanup progress logs
        progress_log_file = f"progress_logs/{task_id}.json"
        if os.path.exists(progress_log_file):
            os.unlink(progress_log_file)
            cleanup_count += 1

        return {
            "message": f"Cleaned up {cleanup_count} files for task {task_id}",
            "task_id": task_id,
            "cleaned_files": cleanup_count,
        }


# Global file service instance
file_service = FileService()
