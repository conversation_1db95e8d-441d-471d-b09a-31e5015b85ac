import json
import logging
import tempfile
import traceback
import uuid

from fastapi import File, Form, HTTPException, Request, UploadFile
from fastapi.responses import JSONResponse

from backend.core.task_manager import TaskStatus, task_manager
from backend.solvers.gen_map_handler import generate_map_worker
from src.gen_map_service.api import GenMapMode


async def solve_level_async_v2(
    request: Request,
    file: UploadFile = File(...),
    mode: str = Form(default="random"),
    order: str = Form(default=""),
):
    try:
        logging.info(f"Solving level with mode: {mode}")

        # Lưu file tạm
        content = await file.read()
        with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as tmp_file:
            json.dump(json.loads(content), tmp_file)
            temp_path = tmp_file.name

        # Parse order nếu có
        parsed_order = []
        if order.strip():
            try:
                game_state = json.loads(content)
                num_blocks = len(game_state["blockList"])

                if order.strip().startswith("["):
                    parsed_order = json.loads(order.strip())
                else:
                    parsed_order = [int(x.strip()) for x in order.strip().split(",") if x.strip()]

                if len(parsed_order) != num_blocks:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid order length. Must be {num_blocks} blocks",
                    )

                sorted_order = sorted(parsed_order)
                if sorted_order[0] != 0:
                    raise HTTPException(
                        status_code=400,
                        detail="Block number must start from 0",
                    )

                for i in range(num_blocks):
                    if sorted_order[i] != i:
                        raise HTTPException(
                            status_code=400,
                            detail=f"Block {i} should be at position {i}",
                        )
            except (json.JSONDecodeError, ValueError) as e:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid order format. Use JSON array [1,2,3] or comma-separated: 1,2,3. Error: {str(e)}",
                )

        # Validate mode
        valid_modes = [
            "random",
            "heuristic_only",
            "check_then_solve",
            "medium_path_only",
            "order_block",
        ]
        if mode not in valid_modes:
            raise HTTPException(
                status_code=400, detail=f"Invalid mode. Must be one of: {valid_modes}"
            )

        # Validate order cho mode order_block
        if mode == "order_block":
            if not parsed_order:
                raise HTTPException(
                    status_code=400,
                    detail="Mode 'order_block' requires 'order' parameter with block IDs",
                )

        # Import task manager và solve_level_wrapper
        from backend.core.task_manager import task_manager
        from backend.solvers.solve_handler import solve_level_wrapper

        task_id = str(uuid.uuid4())
        # Submit task với solve_level_wrapper
        task_id = task_manager.submit(
            solve_level_wrapper,
            temp_path,
            task_id,
            mode,
            parsed_order,
            task_id_index=1,
        )

        return {
            "task_id": task_id,
            "user_id": task_id,
            "status": "queued",
            "mode": mode,
            "message": "Task đã được thêm vào queue",
            "queue_info": task_manager.to_dict(),
            "rate_limit_info": "queued",
        }

    except Exception as e:
        logging.error(f"Error solving level: {e}")
        logging.error(traceback.format_exc())
        return JSONResponse(status_code=500, content={"error": str(e)})


async def get_task_status_v2(task_id: str):
    """Get the status of a task"""
    return JSONResponse(status_code=200, content={"status": task_manager.get_status(task_id)})


async def get_task_result_v2(task_id: str):
    """Lấy kết quả của task từ task manager với format mới"""
    try:
        from backend.core.task_manager import task_manager

        status = task_manager.get_status(task_id)

        if status == TaskStatus.COMPLETED.value:
            # Task đã hoàn thành - trả về kết quả solver
            result = task_manager.get_data(task_id)
            result["status"] = "completed"
            return result

        elif status == TaskStatus.PROCESSING.value:
            # Task đang xử lý - trả về thông tin progress
            task_info = task_manager.to_dict().get(task_id, {})
            progress = getattr(task_info.get("process", None), "progress", 0)
            message = getattr(task_info.get("process", None), "message", "Đang xử lý...")

            return {
                "status": "processing",
                "message": message,
                "queue_info": {
                    "status": "processing",
                    "progress": progress,
                    "message": message,
                    "position_in_queue": 0,
                    "estimated_wait_time": 0,
                    "cancelled": False,
                },
            }

        elif status == TaskStatus.ERROR.value:
            # Task bị lỗi
            return {
                "status": "error",
                "message": "Task failed with error",
                "queue_info": {
                    "status": "error",
                    "progress": 0,
                    "message": "Task failed",
                    "position_in_queue": 0,
                    "estimated_wait_time": 0,
                    "cancelled": False,
                },
            }

        elif status == TaskStatus.CANCELLED.value:
            # Task bị hủy
            return {
                "status": "cancelled",
                "message": "Task was cancelled",
                "queue_info": {
                    "status": "cancelled",
                    "progress": 0,
                    "message": "Task cancelled",
                    "position_in_queue": 0,
                    "estimated_wait_time": 0,
                    "cancelled": True,
                },
            }

        else:
            # Task không tồn tại
            raise HTTPException(status_code=404, detail="Task không tồn tại")

    except Exception as e:
        logging.error(f"Error getting task result: {e}")
        return JSONResponse(status_code=500, content={"error": str(e)})


async def cancel_task_v2(task_id: str):
    """Cancel a task"""
    try:
        is_cancel = task_manager.cancel(task_id)
        if is_cancel:
            return JSONResponse(status_code=200, content={"message": "Task cancelled"})
        else:
            return JSONResponse(
                status_code=400,
                content={"error": f"Task not found or already cancelled: {task_id}"},
            )
    except Exception as e:
        logging.error(f"Error cancelling task: {e}")
        logging.error(traceback.format_exc())
        return JSONResponse(status_code=500, content={"error": str(e)})


async def get_all_tasks_v2():
    """Get all tasks"""
    return JSONResponse(status_code=200, content={"tasks": task_manager.to_dict()})


async def get_task_progress_v2(task_id: str):
    """Get the progress of a task"""
    task_status = task_manager.get_status(task_id)
    progress_message = task_manager.get_progress_message(task_id)
    return JSONResponse(
        status_code=200,
        content={
            "status": task_status,
            "progress": progress_message.get("progress", 0),
            "message": progress_message.get("message", "Đang xử lý..."),
            "position_in_queue": 0,
            "estimated_wait_time": 0,
            "cancelled": task_status == TaskStatus.CANCELLED.value,
        },
    )


async def generate_map_async_v2(
    request: Request,
    file: UploadFile = File(...),
    mode: GenMapMode = GenMapMode.PERMUTE,
    num_blocks: int = 30,
):
    """Generate map async"""

    content = await file.read()
    with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as tmp_file:
        json.dump(json.loads(content), tmp_file)
        temp_path = tmp_file.name

    task_id = str(uuid.uuid4())
    task_manager.submit(
        generate_map_worker,
        temp_path,
        task_id,
        mode,
        False,
        num_blocks,
        task_id_index=1,
    )
    return {
        "task_id": task_id,
        "user_id": task_id,
        "status": "queued",
        "mode": "gen_map",
        "message": "Gen map task đã được thêm vào queue",
        "queue_info": task_manager.to_dict(),
        "rate_limit_info": "queued",
    }


async def predict_task(
    file: UploadFile = File(...),
):
    """
    Load the latest registered model "slide_jam_win_rate_prediction" from MLflow
    and perform predictions on the uploaded data.
    """
    from io import StringIO

    import mlflow
    import mlflow.pyfunc
    import pandas as pd

    try:
        logging.info("Starting prediction task")

        # Configure MLflow tracking server and authentication
        import os

        # Set MLflow tracking URI from environment variable or default
        mlflow_tracking_uri = os.getenv("MLFLOW_TRACKING_URI", "http://localhost:5000")
        mlflow.set_tracking_uri(mlflow_tracking_uri)
        logging.info(f"MLflow tracking URI set to: {mlflow_tracking_uri}")

        # Configure MLflow authentication if credentials are provided
        mlflow_username = os.getenv("MLFLOW_TRACKING_USERNAME")
        mlflow_password = os.getenv("MLFLOW_TRACKING_PASSWORD")
        mlflow_token = os.getenv("MLFLOW_TRACKING_TOKEN")

        if mlflow_token:
            os.environ["MLFLOW_TRACKING_TOKEN"] = mlflow_token
            logging.info("MLflow authentication configured using token")
        elif mlflow_username and mlflow_password:
            os.environ["MLFLOW_TRACKING_USERNAME"] = mlflow_username
            os.environ["MLFLOW_TRACKING_PASSWORD"] = mlflow_password
            logging.info("MLflow authentication configured using username/password")
        else:
            logging.info("No MLflow authentication credentials provided - using anonymous access")

        # Load the latest registered model from MLflow
        model_name = "slide_jam_win_rate_prediction"
        model_version = "latest"

        logging.info(f"Loading model: {model_name} version: {model_version}")

        # Construct model URI for the latest version
        model_uri = f"models:/{model_name}/{model_version}"

        # Load the model using MLflow
        try:
            model = mlflow.pyfunc.load_model(model_uri)
            logging.info(f"Successfully loaded model from URI: {model_uri}")
        except Exception as e:
            logging.error(f"Failed to load model from MLflow: {e}")
            raise HTTPException(
                status_code=500, detail=f"Failed to load model '{model_name}' from MLflow: {str(e)}"
            )

        # Load and parse the uploaded data
        try:
            content = await file.read()

            # Try to parse as JSON first
            try:
                data = json.loads(content)
                # Convert to DataFrame if it's a list of dictionaries
                if isinstance(data, list):
                    df = pd.DataFrame(data)
                elif isinstance(data, dict):
                    df = pd.DataFrame([data])
                else:
                    raise ValueError("Data must be a JSON object or array of objects")
            except json.JSONDecodeError:
                # If JSON parsing fails, try to read as CSV
                content_str = content.decode("utf-8")
                df = pd.read_csv(StringIO(content_str))

            logging.info(f"Successfully loaded data with shape: {df.shape}")

        except Exception as e:
            logging.error(f"Failed to parse uploaded data: {e}")
            raise HTTPException(
                status_code=400,
                detail=f"Failed to parse uploaded data. Please provide valid JSON or CSV format: {str(e)}",
            )

        # Perform prediction
        try:
            predictions = model.predict(df)
            logging.info(f"Successfully generated predictions for {len(predictions)} samples")

            # Convert predictions to a serializable format
            if hasattr(predictions, "tolist"):
                predictions_list = predictions.tolist()
            else:
                predictions_list = list(predictions)

            # Prepare response
            result = {
                "status": "success",
                "model_name": model_name,
                "model_version": model_version,
                "model_uri": model_uri,
                "input_shape": df.shape,
                "predictions": predictions_list,
                "num_predictions": len(predictions_list),
            }

            return JSONResponse(status_code=200, content=result)

        except Exception as e:
            logging.error(f"Failed to generate predictions: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to generate predictions: {str(e)}")

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logging.error(f"Unexpected error in predict_task: {e}")
        logging.error(traceback.format_exc())
        return JSONResponse(status_code=500, content={"error": f"Unexpected error: {str(e)}"})
