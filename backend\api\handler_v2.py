import json
import logging
import tempfile
import traceback
import uuid

from fastapi import File, Form, HTTPException, Request, UploadFile
from fastapi.responses import JSONResponse

from backend.core.task_manager import TaskStatus, task_manager
from backend.solvers.gen_map_handler import generate_map_worker
from src.gen_map_service.api import GenMapMode


async def solve_level_async_v2(
    request: Request,
    file: UploadFile = File(...),
    mode: str = Form(default="random"),
    order: str = Form(default=""),
):
    try:
        logging.info(f"Solving level with mode: {mode}")

        # Lưu file tạm
        content = await file.read()
        with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as tmp_file:
            json.dump(json.loads(content), tmp_file)
            temp_path = tmp_file.name

        # Parse order nếu có
        parsed_order = []
        if order.strip():
            try:
                game_state = json.loads(content)
                num_blocks = len(game_state["blockList"])

                if order.strip().startswith("["):
                    parsed_order = json.loads(order.strip())
                else:
                    parsed_order = [int(x.strip()) for x in order.strip().split(",") if x.strip()]

                if len(parsed_order) != num_blocks:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid order length. Must be {num_blocks} blocks",
                    )

                sorted_order = sorted(parsed_order)
                if sorted_order[0] != 0:
                    raise HTTPException(
                        status_code=400,
                        detail="Block number must start from 0",
                    )

                for i in range(num_blocks):
                    if sorted_order[i] != i:
                        raise HTTPException(
                            status_code=400,
                            detail=f"Block {i} should be at position {i}",
                        )
            except (json.JSONDecodeError, ValueError) as e:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid order format. Use JSON array [1,2,3] or comma-separated: 1,2,3. Error: {str(e)}",
                )

        # Validate mode
        valid_modes = [
            "random",
            "heuristic_only",
            "check_then_solve",
            "medium_path_only",
            "order_block",
        ]
        if mode not in valid_modes:
            raise HTTPException(
                status_code=400, detail=f"Invalid mode. Must be one of: {valid_modes}"
            )

        # Validate order cho mode order_block
        if mode == "order_block":
            if not parsed_order:
                raise HTTPException(
                    status_code=400,
                    detail="Mode 'order_block' requires 'order' parameter with block IDs",
                )

        # Import task manager và solve_level_wrapper
        from backend.core.task_manager import task_manager
        from backend.solvers.solve_handler import solve_level_wrapper

        task_id = str(uuid.uuid4())
        # Submit task với solve_level_wrapper
        task_id = task_manager.submit(
            solve_level_wrapper,
            temp_path,
            task_id,
            mode,
            parsed_order,
            task_id_index=1,
        )

        return {
            "task_id": task_id,
            "user_id": task_id,
            "status": "queued",
            "mode": mode,
            "message": "Task đã được thêm vào queue",
            "queue_info": task_manager.to_dict(),
            "rate_limit_info": "queued",
        }

    except Exception as e:
        logging.error(f"Error solving level: {e}")
        logging.error(traceback.format_exc())
        return JSONResponse(status_code=500, content={"error": str(e)})


async def get_task_status_v2(task_id: str):
    """Get the status of a task"""
    return JSONResponse(status_code=200, content={"status": task_manager.get_status(task_id)})


async def get_task_result_v2(task_id: str):
    """Lấy kết quả của task từ task manager với format mới"""
    try:
        from backend.core.task_manager import task_manager

        status = task_manager.get_status(task_id)

        if status == TaskStatus.COMPLETED.value:
            # Task đã hoàn thành - trả về kết quả solver
            result = task_manager.get_data(task_id)
            result["status"] = "completed"
            return result

        elif status == TaskStatus.PROCESSING.value:
            # Task đang xử lý - trả về thông tin progress
            task_info = task_manager.to_dict().get(task_id, {})
            progress = getattr(task_info.get("process", None), "progress", 0)
            message = getattr(task_info.get("process", None), "message", "Đang xử lý...")

            return {
                "status": "processing",
                "message": message,
                "queue_info": {
                    "status": "processing",
                    "progress": progress,
                    "message": message,
                    "position_in_queue": 0,
                    "estimated_wait_time": 0,
                    "cancelled": False,
                },
            }

        elif status == TaskStatus.ERROR.value:
            # Task bị lỗi
            return {
                "status": "error",
                "message": "Task failed with error",
                "queue_info": {
                    "status": "error",
                    "progress": 0,
                    "message": "Task failed",
                    "position_in_queue": 0,
                    "estimated_wait_time": 0,
                    "cancelled": False,
                },
            }

        elif status == TaskStatus.CANCELLED.value:
            # Task bị hủy
            return {
                "status": "cancelled",
                "message": "Task was cancelled",
                "queue_info": {
                    "status": "cancelled",
                    "progress": 0,
                    "message": "Task cancelled",
                    "position_in_queue": 0,
                    "estimated_wait_time": 0,
                    "cancelled": True,
                },
            }

        else:
            # Task không tồn tại
            raise HTTPException(status_code=404, detail="Task không tồn tại")

    except Exception as e:
        logging.error(f"Error getting task result: {e}")
        return JSONResponse(status_code=500, content={"error": str(e)})


async def cancel_task_v2(task_id: str):
    """Cancel a task"""
    try:
        is_cancel = task_manager.cancel(task_id)
        if is_cancel:
            return JSONResponse(status_code=200, content={"message": "Task cancelled"})
        else:
            return JSONResponse(
                status_code=400,
                content={"error": f"Task not found or already cancelled: {task_id}"},
            )
    except Exception as e:
        logging.error(f"Error cancelling task: {e}")
        logging.error(traceback.format_exc())
        return JSONResponse(status_code=500, content={"error": str(e)})


async def get_all_tasks_v2():
    """Get all tasks"""
    return JSONResponse(status_code=200, content={"tasks": task_manager.to_dict()})


async def get_task_progress_v2(task_id: str):
    """Get the progress of a task"""
    task_status = task_manager.get_status(task_id)
    progress_message = task_manager.get_progress_message(task_id)
    return JSONResponse(
        status_code=200,
        content={
            "status": task_status,
            "progress": progress_message.get("progress", 0),
            "message": progress_message.get("message", "Đang xử lý..."),
            "position_in_queue": 0,
            "estimated_wait_time": 0,
            "cancelled": task_status == TaskStatus.CANCELLED.value,
        },
    )


async def generate_map_async_v2(
    request: Request,
    file: UploadFile = File(...),
    mode: GenMapMode = GenMapMode.PERMUTE,
    num_blocks: int = 30,
):
    """Generate map async"""

    content = await file.read()
    with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as tmp_file:
        json.dump(json.loads(content), tmp_file)
        temp_path = tmp_file.name

    task_id = str(uuid.uuid4())
    task_manager.submit(
        generate_map_worker,
        temp_path,
        task_id,
        mode,
        False,
        num_blocks,
        task_id_index=1,
    )
    return {
        "task_id": task_id,
        "user_id": task_id,
        "status": "queued",
        "mode": "gen_map",
        "message": "Gen map task đã được thêm vào queue",
        "queue_info": task_manager.to_dict(),
        "rate_limit_info": "queued",
    }


async def predict_task(
    request: Request,
    file: UploadFile = File(...),
):
    # load model from mlflow

    # load data to predict

    # predict

    # return result
    pass
