from enum import Enum

COLORS = ["red",
        "blue",
        "green",
        "cyan",
        "violet",
        "pink",
        "orange",
        "yellow",
        "lime",
        "navy",
        "gray",]

COLORS_WALL = ["gray",
            "black",]

# Block đặc biệt
# 0: Normal Block
# 1: Vector Block: chỉ có thể di chuyển theo hướng đã chỉ định
# 2: Layer: Block có 2 màu
# 3: Heart Block
# 4: Ice Block: Mỗi Block clear count -= 1
# 5: Bomb Block: <PERSON><PERSON> thời gian nổ, khi thời gian nổ = 0 thì game over -> Không quan tâm lắm
# 6: Lock Block: Block bị khóa, cần clear hết Block có "haskey" để mở khóa
class Special_Block(Enum):
    NORMAL = 0
    VECTOR = 1
    LAYER = 2
    HEART = 3
    ICE = 4
    BOMB = 5
    LOCK = 6

class Special_Door(Enum):
    NORMAL = 0
    SHUTTER = 1
    HEART = 2
    ICE = 3

NORMAL_DIRECTIONS = {
    "up": (0, 1),
    "down": (0, -1),
    "left": (-1, 0),
    "right": (1, 0),
}

HORIZONTAL_DIRECTIONS = {
    "left": (-1, 0),
    "right": (1, 0),
}

VERTICAL_DIRECTIONS = {
    "up": (0, 1),
    "down": (0, -1),
}

def __init__(self):
    """Initialize the Const class."""
    # You can add any initialization logic if needed
    pass

def get_color(index: int) -> str:
    """Get the color by index."""
    if 0 <= index < len(COLORS):
        return COLORS[index]
    else:
        raise IndexError("Color index out of range.")

def get_direction(moveType: int) -> dict[str, tuple[int, int]]:
    """Get the direction vector by name."""
    if moveType == 0:
        return NORMAL_DIRECTIONS
    elif moveType == 1:
        return HORIZONTAL_DIRECTIONS
    elif moveType == 2:
        return VERTICAL_DIRECTIONS
    else:
        raise ValueError("Invalid direction name.")
    
class DoorDirection(Enum):
    HORIZONTAL = 0
    VERTICAL = 1