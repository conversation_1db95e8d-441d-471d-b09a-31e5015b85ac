[project]
name = "slidejam-solver"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "authlib>=1.6.1",
    "catboost>=1.2.8",
    "fastapi[standard]>=0.116.1",
    "ipykernel>=6.30.1",
    "lightgbm>=4.6.0",
    "matplotlib>=3.10.5",
    "mlflow>=2.19.0",
    "moviepy>=2.2.1",
    "numba==0.61.2",
    "numpy<2.3",
    "optuna>=4.5.0",
    "python-multipart>=0.0.20",
    "scipy>=1.16.1",
    "streamlit>=1.48.0",
    "tqdm>=4.67.1",
    "xgboost>=3.0.4",
]

[tool.ruff]
line-length = 100
extend-include = ["*.ipynb"]

[tool.ruff.lint]
extend-select = ["I"]  # Add import sorting

[tool.ruff.lint.isort]
force-sort-within-sections = true

[dependency-groups]
dev = [
    "ruff>=0.12.8",
]
