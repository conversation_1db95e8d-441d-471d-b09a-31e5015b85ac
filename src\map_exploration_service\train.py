from catboost import CatBoostRegressor
from lightgbm import LGBMRegressor
import numpy as np
import optuna
from sklearn.ensemble import ExtraTreesRegressor, RandomForestRegressor
from sklearn.linear_model import Ridge
from sklearn.metrics import r2_score
from sklearn.model_selection import KFold
from sklearn.svm import SVR
from xgboost import XGBRegressor

from .ensemble_model import EnsembleRegressor, StackingRegressor


# =====================
# Model Factory
# =====================
def create_model(model_type, params, random_state=42):
    if model_type == "lgbm":
        return LGBMRegressor(random_state=random_state, verbose=-1, **params)
    elif model_type == "xgb":
        return XGBRegressor(random_state=random_state, verbosity=0, **params)
    elif model_type == "catboost":
        return CatBoostRegressor(random_state=random_state, verbose=0, **params)
    elif model_type == "rf":
        return RandomForestRegressor(random_state=random_state, **params)
    elif model_type == "et":
        return ExtraTreesRegressor(random_state=random_state, **params)
    elif model_type == "ridge":
        return Ridge(random_state=random_state, **params)
    elif model_type == "svr":
        return SVR(**params)
    else:
        raise ValueError(f"Unsupported model_type: {model_type}")


# =====================
# Objective cho Optuna
# =====================
def get_objective(model_type, X_train, y_train, random_state):
    def objective(trial):
        if model_type == "lgbm":
            params = {
                "objective": "regression",
                "metric": "rmse",
                "boosting_type": "gbdt",
                "verbosity": -1,
                "lambda_l1": trial.suggest_float("lambda_l1", 1e-8, 10.0, log=True),
                "lambda_l2": trial.suggest_float("lambda_l2", 1e-8, 10.0, log=True),
                "num_leaves": trial.suggest_int("num_leaves", 8, 256),
                "feature_fraction": trial.suggest_float("feature_fraction", 0.4, 1.0),
                "bagging_fraction": trial.suggest_float("bagging_fraction", 0.4, 1.0),
                "bagging_freq": trial.suggest_int("bagging_freq", 1, 7),
                "min_child_samples": trial.suggest_int("min_child_samples", 5, 100),
                "max_depth": trial.suggest_int("max_depth", -1, 12),
                "learning_rate": trial.suggest_float("learning_rate", 0.005, 0.1),
                "subsample": trial.suggest_float("subsample", 0.5, 1.0),
                "n_estimators": trial.suggest_int("n_estimators", 100, 1000),
            }

        elif model_type == "xgb":
            params = {
                "objective": "reg:squarederror",
                "eval_metric": "rmse",
                "tree_method": "hist",
                "lambda": trial.suggest_float("lambda", 1e-8, 10.0, log=True),
                "alpha": trial.suggest_float("alpha", 1e-8, 10.0, log=True),
                "max_depth": trial.suggest_int("max_depth", 3, 12),
                "learning_rate": trial.suggest_float("learning_rate", 0.005, 0.2),
                "n_estimators": trial.suggest_int("n_estimators", 100, 1000),
                "subsample": trial.suggest_float("subsample", 0.5, 1.0),
                "colsample_bytree": trial.suggest_float("colsample_bytree", 0.5, 1.0),
                "min_child_weight": trial.suggest_int("min_child_weight", 1, 20),
            }

        elif model_type == "catboost":
            params = {
                "loss_function": "RMSE",
                "eval_metric": "RMSE",
                "iterations": trial.suggest_int("iterations", 200, 1000),
                "depth": trial.suggest_int("depth", 4, 10),
                "learning_rate": trial.suggest_float("learning_rate", 0.005, 0.2),
                "l2_leaf_reg": trial.suggest_float("l2_leaf_reg", 1, 10, log=True),
                "bagging_temperature": trial.suggest_float("bagging_temperature", 0.0, 1.0),
                "border_count": trial.suggest_int("border_count", 32, 255),
                "random_strength": trial.suggest_float("random_strength", 0.5, 5.0),
            }

        elif model_type == "rf":
            params = {
                "n_estimators": trial.suggest_int("n_estimators", 100, 800),
                "max_depth": trial.suggest_int("max_depth", 3, 20),
                "min_samples_split": trial.suggest_int("min_samples_split", 2, 20),
                "min_samples_leaf": trial.suggest_int("min_samples_leaf", 1, 10),
                "max_features": trial.suggest_categorical("max_features", ["sqrt", "log2", None]),
            }

        elif model_type == "et":
            params = {
                "n_estimators": trial.suggest_int("n_estimators", 100, 800),
                "max_depth": trial.suggest_int("max_depth", 3, 20),
                "min_samples_split": trial.suggest_int("min_samples_split", 2, 20),
                "min_samples_leaf": trial.suggest_int("min_samples_leaf", 1, 10),
                "max_features": trial.suggest_categorical("max_features", ["sqrt", "log2", None]),
            }

        elif model_type == "ridge":
            params = {"alpha": trial.suggest_float("alpha", 1e-3, 100.0, log=True)}

        elif model_type == "svr":
            params = {
                "C": trial.suggest_float("C", 1e-2, 1e3, log=True),
                "epsilon": trial.suggest_float("epsilon", 1e-3, 0.5, log=True),
                "gamma": trial.suggest_float("gamma", 1e-4, 1e0, log=True),
                "kernel": trial.suggest_categorical("kernel", ["rbf", "linear", "poly"]),
            }

        else:
            raise ValueError(f"Unsupported model_type: {model_type}")

        model = create_model(model_type, params, random_state)

        kf = KFold(n_splits=5, shuffle=True, random_state=random_state)
        r2_scores = []

        for train_idx, val_idx in kf.split(X_train, y_train):
            X_tr, X_val = X_train[train_idx], X_train[val_idx]
            y_tr, y_val = y_train[train_idx], y_train[val_idx]

            try:
                if model_type == "xgb":
                    model.fit(X_tr, y_tr, eval_set=[(X_val, y_val)], verbose=False)
                elif model_type in ["catboost"]:
                    model.fit(X_tr, y_tr, eval_set=[(X_val, y_val)], verbose=False)
                else:
                    model.fit(X_tr, y_tr)
            except Exception:
                return -1e9

            y_pred = model.predict(X_val).ravel()
            r2_scores.append(r2_score(y_val, y_pred))

        return np.mean(r2_scores)

    return objective


# =====================
# Training Wrapper
# =====================
def train_single_model(X_train, y_train, model_type="lgbm", n_trials=30, random_state=42):
    study = optuna.create_study(direction="maximize")
    study.optimize(get_objective(model_type, X_train, y_train, random_state), n_trials=n_trials)

    best_params = study.best_params
    model = create_model(model_type, best_params, random_state)
    model.fit(X_train, y_train)

    return model, study.best_value, best_params


def train_all_models(X_train, y_train, n_trials=30, random_state=42):
    model_types = ["lgbm", "xgb", "catboost", "rf", "et"]
    results = {}

    for m in model_types:
        model, score, params = train_single_model(X_train, y_train, m, n_trials, random_state)
        results[m] = (model, score, params)

    models = [results[m][0] for m in results]
    ensemble = EnsembleRegressor(models=models)
    stacking = StackingRegressor(models=models, n_splits=10)
    stacking.fit(X_train, y_train)

    results["ensemble"] = ensemble
    results["stacking"] = stacking

    return results
