from enum import Enum
import math
from typing import List, Tuple
from .const import DoorDirection

import numpy as np
from scipy.spatial.transform import Rotation as R


class BlockEnum(Enum):
    BLOCK_1x1 = "Prefabs/Block/1x1"
    BLOCK_1x2 = "Prefabs/Block/1x2"
    BLOCK_1x3 = "Prefabs/Block/1x3"
    BLOCK_2x2 = "Prefabs/Block/2x2"
    BLOCK_PLUS = "Prefabs/Block/Plus"
    BLOCK_T = "Prefabs/Block/T"
    BLOCK_L = "Prefabs/Block/L"
    BLOCK_L_REVERSE = "Prefabs/Block/L Reverse"
    BLOCK_SMALL_L = "Prefabs/Block/Small L"
    BLOCK_U = "Prefabs/Block/U"
    BLOCK_Z = "Prefabs/Block/Z"
    BLOCK_Z_REVERSE = "Prefabs/Block/Z Reverse"

    ### WALL ###
    WALL_05x1 = "Prefabs/Wall/Wall1"
    WALL_05x2 = "Prefabs/Wall/Wall2"
    WALL_05x3 = "Prefabs/Wall/Wall3"
    WALL_1x1 = "Prefabs/Wall/Obstacle1x1"
    WALL_1x2 = "Prefabs/Wall/Obstacle1x2"
    WALL_1x3 = "Prefabs/Wall/Obstacle1x3"
    WALL_2x2 = "Prefabs/Wall/Obstacle2x2"
    WALL_2x3 = "Prefabs/Wall/Obstacle2x3"
    WALL_3x3 = "Prefabs/Wall/Obstacle3x3"
    WALL_CORNER = "Prefabs/Wall/WallCorner"

    ### DOOR ###
    DOOR_05x1 = "Prefabs/Door/Door1"
    DOOR_05x2 = "Prefabs/Door/Door2"
    DOOR_05x3 = "Prefabs/Door/Door3"

    @property
    def shape(self) -> List[Tuple[float, float]]:
        return {
            BlockEnum.BLOCK_1x1: [(0, 0)],
            BlockEnum.BLOCK_1x2: [(0, 0.5), (0, -0.5)],
            BlockEnum.BLOCK_1x3: [(0, -1), (0, 0), (0, 1)],
            BlockEnum.BLOCK_2x2: [(-0.5, -0.5), (-0.5, 0.5), (0.5, -0.5), (0.5, 0.5)],
            BlockEnum.BLOCK_PLUS: [(0, 0), (-1, 0), (1, 0), (0, 1), (0, -1)],
            BlockEnum.BLOCK_T: [(0, 0.5), (0, -0.5), (1, 0.5), (-1, 0.5)],
            BlockEnum.BLOCK_L: [(0.5, 0), (0.5, -1), (0.5, 1), (-0.5, 1)],
            BlockEnum.BLOCK_L_REVERSE: [(-0.5, 0), (-0.5, -1), (-0.5, 1), (0.5, 1)],
            BlockEnum.BLOCK_SMALL_L: [(0.5, 0.5), (0.5, -0.5), (-0.5, -0.5)],
            BlockEnum.BLOCK_U: [(0, -0.5), (1, -0.5), (1, 0.5), (-1, 0.5), (-1, -0.5)],
            BlockEnum.BLOCK_Z: [(0, 0.5), (0, -0.5), (1, -0.5), (-1, 0.5)],
            BlockEnum.BLOCK_Z_REVERSE: [(0, 0.5), (0, -0.5), (-1, 0.5), (1, 0.5)],
            ### WALL ###
            BlockEnum.WALL_05x1: [(0, 0.25)],
            BlockEnum.WALL_05x2: [(0, 0.25), (1, 0.25)],
            BlockEnum.WALL_05x3: [(0, 0.25), (1, 0.25), (-1, 0.25)],
            BlockEnum.WALL_1x1: [(0, 0)],
            BlockEnum.WALL_1x2: [(0.5, 0), (-0.5, 0)],
            BlockEnum.WALL_1x3: [(-1, 0), (0, 0), (1, 0)],
            BlockEnum.WALL_2x2: [(-0.5, -0.5), (-0.5, 0.5), (0.5, -0.5), (0.5, 0.5)],
            BlockEnum.WALL_2x3: [(0, 0.5), (0, -0.5), (1, 0.5), (-1, 0.5), (1, -0.5), (-1, -0.5)],
            BlockEnum.WALL_3x3: [
                (-1, -1),
                (-1, 0),
                (-1, 1),
                (0, -1),
                (0, 0),
                (0, 1),
                (1, -1),
                (1, 0),
                (1, 1),
            ],
            BlockEnum.WALL_CORNER: [(0, 0)],
            ### DOOR ###
            BlockEnum.DOOR_05x1: [(0, 0.25)],
            BlockEnum.DOOR_05x2: [(0, 0.25), (1, 0.25)],
            BlockEnum.DOOR_05x3: [(0, 0.25), (1, 0.25), (-1, 0.25)],
        }[self]


def round_to_grid(value):
    """Làm tròn giá trị về lưới 0, 0.5, 1/sqrt(2), 1"""
    sqrt2_inv = 1 / math.sqrt(2)  # ≈ 0.707

    # Danh sách các giá trị hợp lệ
    valid_values = [-1, -sqrt2_inv, -0.5, 0, 0.5, sqrt2_inv]

    # Tìm giá trị gần nhất
    closest = min(valid_values, key=lambda x: abs(x - value))
    return closest


def round_rotation(rotation):
    """Làm tròn các giá trị rotation về lưới"""
    rounded_rotation = {}
    for axis in ["x", "y", "z", "w"]:
        if axis in rotation:
            rounded_rotation[axis] = round_to_grid(rotation[axis])
    return rounded_rotation


def angle_in_oxz_plane(quat):
    # Tạo đối tượng quaternion
    r = round_rotation(quat)
    r = R.from_quat([r["x"], r["y"], r["z"], r["w"]])

    # Vector gốc trên trục X
    v = np.array([1, 0, 0])

    # Xoay vector
    v_rotated = r.apply(v)

    # Chiếu vector xoay lên mặt phẳng OXZ (bỏ trục Y)
    v1 = np.array([1, 0])  # Gốc (OX)
    v2 = np.array([v_rotated[0], v_rotated[2]])  # Vector sau khi chiếu

    # Chuẩn hóa để tránh lỗi chia 0
    v2_norm = np.linalg.norm(v2)
    if v2_norm == 0:
        return 0.0  # Không có xoay trong OXZ

    v2 = v2 / v2_norm

    # Tính góc giữa 2 vector
    dot = np.clip(np.dot(v1, v2), -1.0, 1.0)
    angle_rad = np.arccos(dot)
    angle_deg = np.degrees(angle_rad)

    # Xác định chiều xoay (dựa vào cross product trong mặt phẳng)
    cross = v1[0] * v2[1] - v1[1] * v2[0]
    if cross < 0:
        angle_deg = -angle_deg  # Xoay theo chiều kim đồng hồ

    return angle_deg


def get_block_enum(block_type: str) -> BlockEnum:
    return BlockEnum(block_type)


def euler_from_quaternion(x, y, z, w):
    """Convert quaternion to Euler angles in degrees"""
    return angle_in_oxz_plane({"x": x, "y": y, "z": z, "w": w})


def rotate_point_2d(x, y, angle_deg):
    """Rotate point (x, y) around origin (0, 0) by angle_deg"""
    x, y = round(x, 1), round(y, 1)
    rad = math.radians(angle_deg)
    cos_a = math.cos(rad)
    sin_a = math.sin(rad)
    return (x * cos_a - y * sin_a, x * sin_a + y * cos_a)


def get_block_cells_2d(
    block_enum: BlockEnum, position: dict, rotation: dict
) -> List[Tuple[int, int]]:
    # Use X and Z from position (Unity 3D to 2D XZ)
    cx, cy = position["x"], position["z"]

    # Get rotation around Y (heading) in degrees
    angle_deg = euler_from_quaternion(rotation["x"], rotation["y"], rotation["z"], rotation["w"])

    # Get cell offsets and apply rotation
    rotated_cells = []
    for dx, dy in block_enum.shape:
        rx, ry = rotate_point_2d(dx, dy, angle_deg)
        rotated_cells.append((int(round(cx + rx, 0)), int(round(cy + ry, 0))))

    return rotated_cells

def get_block_direction(x: int, y: int, z: int, w: int) -> DoorDirection:
    angle_deg = euler_from_quaternion(x, y, z, w) % 180
    if angle_deg == 0:
        return DoorDirection.HORIZONTAL
    elif angle_deg == 90:
        return DoorDirection.VERTICAL
    else:
        raise ValueError("Invalid direction.")