from typing import List, Tuple, Optional
from game.objects import Block
from collections import deque
from game.game_state_medium_path import GameState
import copy

class Optimizer:
    @staticmethod
    def get_first_solution(game_state, max_depth=10, target_block: Optional[Block] = None) -> Optional[Tuple[int, List[Tuple[int, int, int]]]]:
        """
        Tìm một đường đi đầu tiên cho bất kỳ block nào có thể vào door.
        Sử dụng BFS để tìm đường ngắn nhất.
        
        Returns:
            Tuple[block_id, move_sequence] hoặc None nếu không tìm được
        """
        # Sử dụng BFS thay vì DFS để tìm đường ngắn nhất
        queue = deque([(game_state, [])])
        visited_states = set()
        
        while queue:
            current_state : GameState
            current_state, move_sequence = queue.popleft()
            
            # Kiểm tra độ sâu
            if len(move_sequence) > max_depth:
                continue

            # Tạo hash của state hiện tại để tránh lặp
            state_hash = current_state.to_hashable()
            if state_hash in visited_states:
                continue
            visited_states.add(state_hash)
            
            # Kiểm tra tất cả blocks xem có block nào có thể thoát không
            for block in current_state.blocks:
                if current_state.can_exit(block):
                    return (block.id, move_sequence)
            
            # Thử di chuyển tất cả các block
            for block in current_state.blocks:
                if not current_state.can_move_block(block):
                    continue
                    
                # Lấy tất cả các di chuyển hợp lệ cho block này
                valid_moves = current_state.get_valid_moves(block, max_step=10)

                for dx, dy in valid_moves:
                    new_state = copy.deepcopy(current_state)
                    new_block = new_state.find_block(block.id)
                    
                    if new_block and new_state.move_block(new_block, dx, dy):
                        new_move_sequence = move_sequence + [(block.id, dx, dy)]
                        
                        # Nếu block vừa di chuyển có thể ăn được, thử ăn nó
                        if new_state.can_exit(new_block):
                            # Tạo state sau khi ăn block
                            eat_state = copy.deepcopy(new_state)
                            eat_block = eat_state.find_block(block.id)
                            if eat_block:
                                eat_state.eat_block(eat_block)
                                eat_move_sequence = new_move_sequence + [(-block.id, 0, 0)]
                                queue.append((eat_state, eat_move_sequence))
                        
                        # Thêm state mới vào queue
                        queue.append((new_state, new_move_sequence))
        return None

    @staticmethod
    def get_first_solution_optimized(game_state : GameState, max_depth=5) -> Optional[Tuple[int, List[Tuple[int, int, int]]]]:
        """
        Phiên bản tối ưu hơn với các cải tiến:
        - Giảm max_depth xuống 5 (thường đủ)
        - Ưu tiên kiểm tra blocks gần door trước
        - Early termination khi tìm thấy solution
        """
        # Tìm vị trí door trung tâm để tính khoảng cách
        def get_door_center():
            if not game_state.doors:
                return (game_state.size_x // 2, game_state.size_y // 2)
            
            all_door_positions = []
            for door in game_state.doors:
                all_door_positions.extend(door.subBlocks)
            
            if all_door_positions:
                avg_x = sum(pos[0] for pos in all_door_positions) / len(all_door_positions)
                avg_y = sum(pos[1] for pos in all_door_positions) / len(all_door_positions)
                return (avg_x, avg_y)
            return (game_state.size_x // 2, game_state.size_y // 2)
        
        door_center = get_door_center()
        
        # Sắp xếp blocks theo khoảng cách đến door center (gần nhất trước)
        def distance_to_door(block):
            if not block.subBlocks:
                return float('inf')
            # Lấy vị trí trung tâm của block
            block_center_x = sum(pos[0] for pos in block.subBlocks) / len(block.subBlocks)
            block_center_y = sum(pos[1] for pos in block.subBlocks) / len(block.subBlocks)
            return abs(block_center_x - door_center[0]) + abs(block_center_y - door_center[1])
        
        sorted_blocks = sorted(game_state.blocks, key=distance_to_door)
        
        # Kiểm tra nhanh: có block nào đã có thể thoát ngay không?
        for block in sorted_blocks:
            if game_state.can_exit(block):
                return (block.id, [])
        
        queue = deque([(game_state, [])])
        visited_states = set()
        
        while queue:
            current_state, move_sequence = queue.popleft()
            
            if len(move_sequence) > max_depth:
                continue
                
            state_hash = current_state.to_hashable()
            if state_hash in visited_states:
                continue
            visited_states.add(state_hash)
            
            # Thử di chuyển blocks theo thứ tự ưu tiên (gần door trước)
            current_sorted_blocks = sorted(current_state.blocks, key=distance_to_door)
            
            for block in current_sorted_blocks:
                if not current_state.can_move_block(block):
                    continue
                    
                valid_moves = current_state.get_valid_moves(block, max_step=3)
                
                # Sắp xếp moves theo hướng về phía door center
                def move_priority(move):
                    dx, dy = move
                    if not block.subBlocks:
                        return float('inf')
                    # Tính vị trí mới của block center
                    new_positions = [(x + dx, y + dy) for x, y in block.subBlocks]
                    new_center_x = sum(pos[0] for pos in new_positions) / len(new_positions)
                    new_center_y = sum(pos[1] for pos in new_positions) / len(new_positions)
                    return abs(new_center_x - door_center[0]) + abs(new_center_y - door_center[1])
                
                sorted_moves = sorted(valid_moves, key=move_priority)
                
                for dx, dy in sorted_moves:
                    new_state = copy.deepcopy(current_state)
                    new_block = new_state.find_block(block.id)
                    
                    if new_block and new_state.move_block(new_block, dx, dy):
                        new_move_sequence = move_sequence + [(block.id, dx, dy)]
                        
                        # Kiểm tra ngay xem có block nào có thể thoát không
                        for check_block in new_state.blocks:
                            if new_state.can_exit(check_block):
                                return (check_block.id, new_move_sequence)
                        
                        # Nếu block vừa di chuyển có thể ăn được
                        if new_state.can_exit(new_block):
                            eat_state = copy.deepcopy(new_state)
                            eat_block = eat_state.find_block(block.id)
                            if eat_block:
                                eat_state.eat_block(eat_block)
                                eat_move_sequence = new_move_sequence + [(-block.id, 0, 0)]
                                
                                # Kiểm tra ngay sau khi ăn
                                for check_block in eat_state.blocks:
                                    if eat_state.can_exit(check_block):
                                        return (check_block.id, eat_move_sequence)
                                
                                queue.append((eat_state, eat_move_sequence))
                        
                        queue.append((new_state, new_move_sequence))
        
        return None

    @staticmethod
    def get_adaptive_solution(game_state : GameState, start_depth=2, max_depth=10, increment=1) -> Optional[Tuple[int, List[Tuple[int, int, int]]]]:
        """
        Tìm solution với độ sâu tự động tăng dần.
        Bắt đầu với độ sâu nhỏ và tăng dần cho đến khi tìm thấy solution.
        
        Args:
            game_state: Trạng thái game hiện tại
            start_depth: Độ sâu bắt đầu (default: 2)
            max_depth: Độ sâu tối đa (default: 10)
            increment: Bước tăng độ sâu (default: 1)
        
        Returns:
            Tuple[block_id, move_sequence] hoặc None nếu không tìm được
        """
        print(f"Starting adaptive search from depth {start_depth} to {max_depth}")
        
        for current_depth in range(start_depth, max_depth + 1, increment):
            print(f"Trying depth {current_depth}...")
            
            result = Optimizer.get_first_solution_optimized(game_state, max_depth=current_depth)
            if result:
                block_id, move_sequence = result
                print(f"Solution found at depth {current_depth} with {len(move_sequence)} moves")
                return result
        
        print(f"No solution found up to depth {max_depth}")
        return None

    @staticmethod
    def get_smart_adaptive_solution(game_state : GameState, start_depth=2, max_depth=10) -> Optional[Tuple[int, List[Tuple[int, int, int]]]]:
        """
        Phiên bản thông minh hơn với chiến lược adaptive search:
        - Ước tính độ phức tạp ban đầu
        - Điều chỉnh bước tăng depth dựa trên kích thước bài toán
        - Early termination nếu quá nhiều states được explore
        """
        
        # Ước tính độ phức tạp của bài toán
        num_blocks = len(game_state.blocks)
        board_size = game_state.size_x * game_state.size_y
        complexity_score = num_blocks * (board_size / 100)  # Điểm phức tạp
        
        # Điều chỉnh chiến lược dựa trên độ phức tạp
        if complexity_score < 5:  # Bài toán đơn giản
            depths_to_try = [1, 2, 3, 4, 6, 8]
        elif complexity_score < 15:  # Bài toán trung bình
            depths_to_try = [2, 3, 4, 5, 7, 9]
        else:  # Bài toán phức tạp
            depths_to_try = [2, 3, 5, 7, 10]
        
        # Lọc depths trong khoảng cho phép
        depths_to_try = [d for d in depths_to_try if start_depth <= d <= max_depth]
        
        print(f"Smart adaptive search for complexity {complexity_score:.2f}")
        print(f"Will try depths: {depths_to_try}")
        
        for current_depth in depths_to_try:
            print(f"Trying depth {current_depth}...")
            
            result = Optimizer.get_first_solution_optimized(game_state, max_depth=current_depth)
            if result:
                block_id, move_sequence = result
                print(f"Solution found at depth {current_depth} with {len(move_sequence)} moves")
                return result
                
            # Nếu depth hiện tại quá lâu, có thể skip một số depth cao hơn
            # (có thể thêm logic timing ở đây nếu cần)
        
        print("No solution found with smart adaptive search")
        return None

    @staticmethod
    def get_fastest_solution(game_state : GameState, use_adaptive=True, start_depth=2, max_depth=8) -> Optional[List[Tuple[int, int, int]]]:
        """
        Hàm tối ưu nhất - tự động điều chỉnh depth và chỉ trả về move sequence
        
        Args:
            game_state: Trạng thái game
            use_adaptive: Có sử dụng adaptive search không (default: True)
            start_depth: Độ sâu bắt đầu (default: 2)
            max_depth: Độ sâu tối đa (default: 8)
        """
        if use_adaptive:
            result = Optimizer.get_smart_adaptive_solution(game_state, start_depth, max_depth)
        else:
            result = Optimizer.get_first_solution_optimized(game_state, max_depth=max_depth)
        
        if result:
            block_id, move_sequence = result
            return move_sequence
        return None

    @staticmethod
    def get_immediate_solution(game_state : GameState) -> Optional[List[Tuple[int, int, int]]]:
        """
        Tìm solution nhanh nhất có thể - chỉ thử depth 1 và 2
        Dành cho các trường hợp cần phản hồi tức thì
        """
        # Kiểm tra xem có block nào đã có thể exit ngay không
        for block in game_state.blocks:
            if game_state.can_exit(block):
                return []  # Không cần di chuyển gì
        
        # Thử depth 1
        result = Optimizer.get_first_solution_optimized(game_state, max_depth=1)
        if result:
            return result[1]
            
        # Thử depth 2
        result = Optimizer.get_first_solution_optimized(game_state, max_depth=2)
        if result:
            return result[1]
        
        return None  # Không tìm thấy solution nhanh