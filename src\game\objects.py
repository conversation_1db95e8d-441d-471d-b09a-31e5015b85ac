import json
from typing import List, Tuple

from matplotlib.patches import Rectangle
import matplotlib.pyplot as plt

from src.game.block_enum import BlockEnum, get_block_cells_2d, get_block_direction
from src.game.const import COLORS, Special_Block, get_direction


class Object:
    def __init__(self):
        pass


class Block(Object):
    def __init__(self, block_data: dict, id: int) -> None:
        self.id = id

        # Màu và màu thứ 2 của block
        self.color = block_data["color"]
        self.secondColor = block_data["secondColor"]

        self.special = block_data["special"]
        self.pos_x = block_data["position"]["x"]
        self.pos_y = block_data["position"]["y"]
        self.pos_z = block_data["position"]["z"]
        self.prefabPath = block_data["prefabPath"]

        # Các hướng đi của block
        # 0: Normal Block: có thể đi theo mọi hướng
        # 1: Horizontal Block: chỉ có thể đi ngang
        # 2: Vertical Block: chỉ có thể đi dọc
        self.moveType = block_data["moveType"]
        if self.special == Special_Block.VECTOR.value:
            self.directions = get_direction(self.moveType)
        else:
            self.directions = get_direction(0)

        # Other properties
        self.turnCount = block_data["turnCount"]
        self.timeCount = block_data["timeCount"]
        self.hasKey = block_data["hasKey"]
        self.rotation = block_data["rotation"]
        self.subBlocks: List[Tuple[int, int]] = get_block_cells_2d(
            BlockEnum(block_data["prefabPath"]), block_data["position"], block_data["rotation"]
        )

    def get_center(self) -> tuple[float, float]:
        """Trả về tọa độ trung tâm của block"""
        if not self.subBlocks:
            return (self.pos_x, self.pos_y)
        x_coords = [x for x, y in self.subBlocks]
        y_coords = [y for x, y in self.subBlocks]
        return (sum(x_coords) / len(x_coords), sum(y_coords) / len(y_coords))

    def get_subBlocks(self):
        """Trả về danh sách các ô con của block"""
        return self.subBlocks

    def move(self, dx, dy):
        """Di chuyển block theo hướng dx, dy"""
        self.subBlocks = [(x + dx, y + dy) for x, y in self.get_subBlocks()]

    def copy(self) -> "Block":
        """Creates a deep copy of this block.

        Returns:
            A new Block instance with the same properties as this one.
        """
        # Create a new Block with same data (implementation depends on your needs)
        # This is a placeholder - you'll need to implement based on your requirements
        new_block = Block.__new__(Block)
        new_block.__dict__ = self.__dict__.copy()
        new_block.subBlocks = self.subBlocks.copy()
        return new_block

    def remove_sub_block(self, sub_block) -> bool:
        """Xoá một subBlock khỏi danh sách subBlocks"""
        if sub_block in self.subBlocks:
            self.subBlocks.remove(sub_block)
            return True
        return False

    def visualize_block(self) -> None:
        # print("Visualizing block:", self.id)
        # print("Subblocks:", self.subBlocks)
        fig, ax = plt.subplots(figsize=(6, 4))
        for x, y in self.subBlocks:
            rect = Rectangle(
                (x - 0.5, y - 0.5), 1, 1, facecolor=COLORS[self.color], edgecolor="black"
            )
            ax.add_patch(rect)
        ax.set_aspect("equal")
        ax.set_xlim(-1, 10)
        ax.set_ylim(-1, 10)
        ax.grid(True, alpha=0.3)
        plt.title(f"Block ID: {self.id}")
        # plt.show()
        plt.draw()

    def get_boundaries(self) -> tuple[float, float, float, float]:
        """Trả về các ranh giới của block"""
        if not self.subBlocks:
            return (self.pos_x, self.pos_y, self.pos_x, self.pos_y)
        x_coords = [x for x, y in self.subBlocks]
        y_coords = [y for x, y in self.subBlocks]
        return (min(x_coords), min(y_coords), max(x_coords), max(y_coords))


class Wall(Object):
    def __init__(self, wall_data: dict) -> None:
        # Other properties
        self.prefabPath = wall_data["prefabPath"]
        self.rotation = wall_data["rotation"]
        self.directions = get_direction(0)
        self.subBlocks = get_block_cells_2d(
            BlockEnum(wall_data["prefabPath"]), wall_data["position"], wall_data["rotation"]
        )
        self.color = -1
        self.position = wall_data["position"]

    def get_subBlocks(self):
        """Trả về danh sách các ô con của block"""
        return self.subBlocks

    def move(self, dx, dy):
        """Di chuyển block theo hướng dx, dy"""
        self.subBlocks = [(x + dx, y + dy) for x, y in self.get_subBlocks()]

    def copy(self) -> "Wall":
        """Creates a deep copy of this wall.

        Returns:
            A new Wall instance with the same properties as this one.
        """
        new_wall = Wall.__new__(Wall)
        new_wall.__dict__ = self.__dict__.copy()
        new_wall.subBlocks = self.subBlocks.copy()
        return new_wall


class Door(Object):
    def __init__(self, door_data: dict, id: int) -> None:
        # Màu của Gate ra ứng với màu của Block
        self.id = id
        self.color = door_data["color"]
        self.pos_x = door_data["position"]["x"]
        self.pos_y = door_data["position"]["y"]
        self.pos_z = door_data["position"]["z"]
        self.prefabPath = door_data["prefabPath"]
        self.rotation = door_data["rotation"]
        self.direction = get_block_direction(
            x=door_data["rotation"]["x"],
            y=door_data["rotation"]["y"],
            z=door_data["rotation"]["z"],
            w=door_data["rotation"]["w"],
        )

        # Other properties
        self.subBlocks = get_block_cells_2d(
            BlockEnum(door_data["prefabPath"]), door_data["position"], door_data["rotation"]
        )
        self.special = door_data.get("special", 0)
        self.turnCount = door_data.get("turnCount", 0)
        self.open = door_data.get("isShutterOpen", False)

    def get_center(self) -> tuple[float, float]:
        """Trả về tọa độ trung tâm của cửa"""
        if not self.subBlocks:
            return (self.pos_x, self.pos_y)
        x_coords = [x for x, y in self.subBlocks]
        y_coords = [y for x, y in self.subBlocks]
        return (sum(x_coords) / len(x_coords), sum(y_coords) / len(y_coords))

    def get_boundaries(self) -> tuple[float, float, float, float]:
        """Trả về các ranh giới của cửa"""
        if not self.subBlocks:
            return (self.pos_x, self.pos_y, self.pos_x, self.pos_y)
        x_coords = [x for x, y in self.subBlocks]
        y_coords = [y for x, y in self.subBlocks]
        return (min(x_coords), min(y_coords), max(x_coords), max(y_coords))

    def copy(self) -> "Door":
        """Creates a deep copy of this door.

        Returns:
            A new Door instance with the same properties as this one.
        """
        new_door = Door.__new__(Door)
        new_door.__dict__ = self.__dict__.copy()
        new_door.subBlocks = self.subBlocks.copy()
        return new_door


if __name__ == "__main__":
    j = """
    {
      "prefabPath": "Prefabs/Block/1x3",
      "position": {
        "x": 1.0,
        "y": 0.8500000238418579,
        "z": 2.0
      },
      "rotation": {
        "x": 0.7071068286895752,
        "y": 0.0,
        "z": 0.0,
        "w": 0.7071068286895752
      },
      "scale": {
        "x": 0.5,
        "y": 0.5,
        "z": 0.5
      },
      "color": 0,
      "special": 0,
      "secondColor": 0,
      "moveType": 0,
      "turnCount": 0,
      "timeCount": 0,
      "hasKey": false
    }"""
    block = Block(json.loads(j), 1)
    block.visualize_block()
