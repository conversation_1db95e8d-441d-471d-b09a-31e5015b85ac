import time

import streamlit as st

from frontend.handlers import task_manager
from frontend.handlers.task_manager import task_manager
from src.gen_map_service.api import GenMapMode


def Sidebar(
    uploaded_file, main_mode, order_input, solve_mode, auto_solve, auto_gen, mode, num_blocks
):
    with st.sidebar:
        st.header("📁 Upload Level")
        uploaded_file = st.file_uploader(
            "Chọn file JSON level", type=["json"], help="Upload file JSON chứa dữ liệu level"
        )

        if uploaded_file:
            # Mode selection - THÊM GEN MAP
            st.header("Chọn chức năng")

            def cancel_task():
                task_manager.cancel_task(st.session_state.current_task_id)

            main_mode = st.radio(
                "Chức năng chính:",
                ["solve", "gen_map"],
                format_func=lambda x: {"solve": "Giải Puzzle", "gen_map": "Sinh Map Mới"}[x],
                # disabled=st.session_state.task_running,  # DISABLE KHI TASK ĐANG CHẠY
                on_change=cancel_task,
            )

            if main_mode == "solve":
                # Algorithm selection cho solving
                st.header("<PERSON><PERSON>n thuật toán giải")
                solve_mode = st.radio(
                    "Ph<PERSON><PERSON>ng thức giải:",
                    [
                        "random",
                        "heuristic_only",
                        "check_then_solve",
                        "medium_path_only",
                        "order_block",  # THÊM ORDER_BLOCK
                    ],
                    format_func=lambda x: {
                        "random": "Kiểm tra nhanh (Random)",
                        "heuristic_only": "Giải tối ưu (Heuristic)",
                        "check_then_solve": "Kiểm tra + Tối ưu",
                        "medium_path_only": "Giải Shortest Path",
                        "order_block": "Giải theo thứ tự Block",  # THÊM DESCRIPTION
                    }[x],
                    # disabled=st.session_state.task_running,  # DISABLE KHI TASK ĐANG CHẠY
                    on_change=cancel_task,
                )

                # Mô tả mode giải
                mode_descriptions = {
                    "random": """
                    **Kiểm tra nhanh (Random):**
                    - Chỉ check xem level có giải được không
                    - Hiển thị số moves nếu giải được
                    - Nhanh chóng (< 30s), không tạo video
                    - Dùng để test nhiều level
                    """,
                    "heuristic_only": """
                    **Giải tối ưu (Heuristic):**
                    - Tìm path ngắn nhất có thể
                    - Tạo video demo chi tiết
                    - Download file zip chứa ảnh các bước
                    - Chậm hơn nhưng tối ưu
                    """,
                    "check_then_solve": """
                    **Kiểm tra + Tối ưu:**
                    - Bước 1: Check nhanh bằng random
                    - Bước 2: Nếu giải được → tối ưu bằng heuristic
                    - An toàn nhất, có video + so sánh hiệu quả
                    - Thời gian trung bình
                    """,
                    "medium_path_only": """
                    **Giải Shortest Path:**
                    - Thuật toán Medium Path Solver
                    - Tìm path với độ dài trung bình
                    - Tạo video demo chi tiết
                    - Download file zip chứa ảnh các bước
                    - Thời gian xử lý trung bình
                    """,
                    "order_block": """
                    **Giải theo thứ tự Block:**
                    - Giải puzzle theo thứ tự blocks được chỉ định
                    - Nhập danh sách ID blocks cần giải theo thứ tự
                    - Tạo video demo chi tiết
                    - Download file zip chứa ảnh các bước
                    - Thời gian xử lý tùy thuộc vào thứ tự
                    """,  # THÊM MÔ TẢ ORDER_BLOCK
                }

                st.info(mode_descriptions[solve_mode])

                # THÊM INPUT CHO ORDER
                order_input = ""
                if solve_mode == "order_block":
                    st.header("🔢 Thứ tự Blocks")
                    order_input = st.text_input(
                        "Nhập thứ tự blocks (cách nhau bởi dấu phẩy):",
                        placeholder="Ví dụ: 1,2,3,4 hoặc 5,1,3,2",
                        help="Nhập ID của các blocks theo thứ tự bạn muốn giải. Ví dụ: 1,2,3,4",
                        disabled=st.session_state.task_running,  # DISABLE KHI TASK ĐANG CHẠY
                    )

                    if order_input.strip():
                        try:
                            # Validate order input
                            order_list = [
                                int(x.strip()) for x in order_input.strip().split(",") if x.strip()
                            ]
                            if order_list:
                                st.success(f"✅ Thứ tự hợp lệ: {order_list}")
                            else:
                                st.warning("⚠️ Vui lòng nhập ít nhất một block ID")
                        except ValueError:
                            st.error(
                                "❌ Format không hợp lệ. Vui lòng chỉ nhập các số nguyên cách nhau bởi dấu phẩy."
                            )

                # Options cho video
                if solve_mode in [
                    "heuristic_only",
                    "check_then_solve",
                    "medium_path_only",
                    "order_block",  # THÊM ORDER_BLOCK
                ]:
                    st.header("⚙️ Tùy chọn Video")
                    st.checkbox("Hiển thị số thứ tự bước", value=True, disabled=True)
                    st.checkbox("Tạo file zip", value=True, disabled=True)

            elif main_mode == "gen_map":
                # Mô tả gen map
                mode = st.radio(
                    "Chọn mode sinh map:",
                    [GenMapMode.RANDOM.value, GenMapMode.PERMUTE.value],
                    on_change=cancel_task,
                )
                num_blocks = st.number_input(
                    "Số blocks mới tối đa:", min_value=1, max_value=100, value=15
                )
                st.header("🗺️ Sinh Map Mới")
                st.info("""
                **Sinh Map Mới:**
                - Tạo map mới dựa trên map gốc
                - Đổi màu blocks và doors ngẫu nhiên
                - Thêm blocks mới (1-3 blocks)
                - Shuffle vị trí các blocks
                - Đảm bảo map sinh ra có thể giải được
                - Download map mới dưới dạng JSON và ảnh
                """)

            # Nút action
            if main_mode == "solve":
                # THÊM VALIDATION CHO ORDER_BLOCK VÀ TASK RUNNING
                button_disabled = st.session_state.task_running

                if solve_mode == "order_block" and not st.session_state.task_running:
                    if not order_input.strip():
                        button_disabled = True
                        st.warning("⚠️ Vui lòng nhập thứ tự blocks trước khi bắt đầu giải")
                    else:
                        try:
                            order_list = [
                                int(x.strip()) for x in order_input.strip().split(",") if x.strip()
                            ]
                            if not order_list:
                                button_disabled = True
                        except ValueError:
                            button_disabled = True

                # HIỂN THỊ BUTTON TEXT KHÁC NHAU
                while True:
                    st.info("🔄 Task đang chạy...")
                    if st.session_state.task_running:
                        button_text = "🔄 Task đang chạy..."
                    else:
                        button_text = "🚀 Bắt đầu giải"
                    time.sleep(2)

                if st.session_state.task_running:
                    button_text = "🔄 Task đang chạy..."
                else:
                    button_text = "🚀 Bắt đầu giải"

                auto_solve = st.button(button_text, type="primary", disabled=button_disabled)
            else:
                # THÊM LOGIC TƯƠNG TỰ CHO GEN MAP
                button_disabled = st.session_state.task_running

                if st.session_state.task_running:
                    button_text = "🔄 Task đang chạy..."
                else:
                    button_text = "🗺️ Sinh Map Mới"

                auto_gen = st.button(button_text, type="primary", disabled=button_disabled)

        return (
            uploaded_file,
            main_mode,
            order_input,
            solve_mode,
            auto_solve,
            auto_gen,
            mode,
            num_blocks,
        )
