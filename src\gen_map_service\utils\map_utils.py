import copy
from typing import List

from src.game.game_state_base import GameStateBase
from src.game.objects import Door, Object, Wall


def get_map_boundaries(state: GameStateBase):
    min_x, min_y = float('inf'), float('inf')
    max_x, max_y = -float('inf'), -float('inf')

    for wall in state.walls:
        for x, y in wall.subBlocks:
            min_x = min(x, min_x)
            min_y = min(y, min_y)
            max_x = max(x, max_x)
            max_y = max(y, max_y)

    return min_x, max_x, min_y, max_y

def is_adjacent_door(wall: Wall, doors: List[Door]):
    wall_positions = set(wall.subBlocks)
    
    directions = [(-1, 0), (1, 0), (0, -1), (0, 1)]

    for door in doors:
        for door_x, door_y in door.subBlocks:
            for dx, dy in directions:
                neighbor_pos = (door_x + dx, door_y + dy)
                if neighbor_pos in wall_positions:
                    return True
    return False

def swap_color(object1: Object, object2: Object):
    temp_color = copy.deepcopy(object1.color)
    object1.color = copy.deepcopy(object2.color)
    object2.color = temp_color

def swap_speciality(object1: Object, object2: Object):
    temp_speciality = copy.deepcopy(object1.special)
    object1.special = copy.deepcopy(object2.special)
    object2.special = temp_speciality
