import copy
from typing import List, Tuple

from src.game.game_state_base import Game<PERSON>tateB<PERSON>
from src.game.objects import Block

from .utils import get_valid_moves, move_object


def distance_to_nearest_door(state: GameStateBase, block: Block, new_pos: List[Tuple[int, int]]) -> float:
    min_dist = float('inf')
    accessible_doors = state.get_accessible_doors(block)
    for door in accessible_doors:
        for bx in new_pos:
            for dx in door.subBlocks:
                dist = abs(bx[0] - dx[0]) + abs(bx[1] - dx[1])
                if dist < min_dist:
                    min_dist = dist
    return min_dist if min_dist != float('inf') else 0.0


def block_contribution(state: GameStateBase, block: Block) -> float:
    return distance_to_nearest_door(state, block, block.subBlocks)


def total_heuristic(state: GameStateBase) -> float:
    return sum(block_contribution(state, b) for b in state.blocks)


def best_offset_for_block(state: GameStateBase, block: Block, max_step: int = 50) -> <PERSON><PERSON>[Tuple[int, int], float]:
    state.remove_occupied(block)

    start = (0, 0)
    visited = set([start])
    stack = [start]

    best_offset = (0, 0)
    base_sub = block.subBlocks  # giữ lại để tính nhanh new_sub_blocks
    best_contrib = distance_to_nearest_door(state, block, base_sub)

    steps = 0

    while stack and steps < max_step:
        dx, dy = stack.pop()
        steps += 1

        # Mở rộng 4 hướng từ offset hiện tại
        for dir_dx, dir_dy in block.directions.values():
            ndx, ndy = dx + dir_dx, dy + dir_dy
            if (ndx, ndy) in visited:
                continue
            visited.add((ndx, ndy))

            # Tạo block tạm và validate
            tmp = copy.deepcopy(block)
            tmp.move(ndx, ndy)
            if not state.is_valid_move(tmp, 0, 0):
                continue

            # Tính contribution tại vị trí mới (so với cửa)
            new_sub = [(x + ndx, y + ndy) for (x, y) in base_sub]
            contrib = distance_to_nearest_door(state, block, new_sub)

            if contrib > best_contrib:
                best_contrib = contrib
                best_offset = (ndx, ndy)

            stack.append((ndx, ndy))

    # Khôi phục: đặt block về chỗ cũ (chưa move thật) và add occupied lại
    state.add_occupied(block)
    return best_offset, best_contrib


def coordinate_ascent_shuffle(state: GameStateBase, passes: int = 5, max_step: int = 200, early_stop: bool = True):
    """
    Lặp nhiều vòng; mỗi vòng duyệt qua tất cả block và di chuyển nếu làm tăng tổng heuristic.
    """
    cur_total = total_heuristic(state)
    improved_any = True

    for p in range(passes):
        if early_stop and not improved_any:
            break
        improved_any = False

        # Có thể shuffle thứ tự block để tránh kẹt pattern
        # random.shuffle(state.blocks)

        for block in state.blocks:
            # Contribution hiện tại của block
            cur_contrib = block_contribution(state, block)

            # Tìm offset tốt nhất khi các block khác giữ nguyên
            best_off, best_contrib = best_offset_for_block(state, block, max_step=max_step)

            # Nếu không cải thiện riêng block thì bỏ qua
            if best_contrib <= cur_contrib:
                continue

            # Move thật và cập nhật trạng thái + tổng heuristic
            state.remove_occupied(block)
            move_object(state, block, best_off[0], best_off[1])
            state.add_occupied(block)

            cur_total = cur_total - cur_contrib + best_contrib
            improved_any = True

    return cur_total


def strategic_shuffle(state: GameStateBase, passes: int = 5, max_step: int = 200):
    """
    Wrapper thay cho bản cũ: tối đa hóa tổng khoảng cách của tất cả block tới cửa bằng coordinate ascent.
    """
    return coordinate_ascent_shuffle(state, passes=passes, max_step=max_step, early_stop=True)
