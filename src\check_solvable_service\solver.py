import copy
import itertools
import logging
import math
import random
from typing import List, Optional, Tuple

import numpy as np

from ..game.game_state_base import GameStateBase
from ..game.visualization.visualize_game_state import visualize_state
from ..heuristic.heuristic import Heuristic
from .config import ExperimentConfig, SolverMetrics
from .utils import can_exit_precheck


class PuzzleSolver:
    def __init__(self, config: ExperimentConfig, logger: Optional[logging.Logger] = None):
        self.config = config
        self.logger = logger or self._setup_logger()
        self._reset()

    def _setup_logger(self) -> logging.Logger:
        logger = logging.getLogger(f"{__name__}.CheckSolvableService")
        if not logger.handlers:
            handler = logging.StreamHandler()
            handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - %(message)s"))
            logger.addHandler(handler)
        logger.setLevel(getattr(logging, self.config.log_level.upper()))
        return logger

    def _reset(self):
        self.counter = itertools.count()
        self.metrics = SolverMetrics()
        self.best_blocks = float("inf")
        self.unsolvable = False
        self.path = []

    def shuffle_all_blocks(self, state: GameStateBase) -> Tuple[GameStateBase, List]:
        shuffle_path = []
        initial_blocks = len(state.blocks)

        self.logger.debug(
            f"All Randomizing: {initial_blocks} blocks, {self.config.randomizing_steps} steps"
        )

        stagnant_counter = 0
        last_block_count = len(state.blocks)

        for idx in range(self.config.randomizing_steps):
            if not state.blocks:
                break

            edible_blocks = state.can_eat()
            for block in edible_blocks:
                if self.config.debug_viz:
                    visualize_state(state, block_id=block.id, regime="show")
                if state.eat_block(state.find_block(block.id)):
                    self.logger.debug(f"Remaining Blocks: {len(state.blocks)}, ate block: {block.id}, iteration: {idx}")
                    shuffle_path.append(('eat', block.id))

            current_block_count = len(state.blocks)
            if current_block_count <= self.min_block_for_stagnant:
                if current_block_count == last_block_count:
                    stagnant_counter += 1
                else:
                    stagnant_counter = 0
                    last_block_count = current_block_count

                if stagnant_counter >= self.config.stagnant_limit:
                    self.unsolvable = True
                    self.logger.debug(
                        f"Stagnant detected with {current_block_count} blocks. Stopping shuffle."
                    )
                    break

            permed_blocks = np.random.permutation(state.blocks)
            for block in permed_blocks:
                moves = state.get_valid_moves(block, max_step=10)
                if moves:
                    dx, dy = random.choice(moves)
                    if state.move_block(block, dx, dy):
                        shuffle_path.append((block.id, dx, dy))

            self.metrics.iteration = idx
        self.logger.debug(f"Randomization: {initial_blocks} -> {len(state.blocks)} blocks, "
                        f"{len(shuffle_path)} moves")

        return state, shuffle_path

    def shuffle_all_blocks_cutoff(self, state: GameStateBase) -> Tuple[GameStateBase, List]:
        shuffle_path = []
        initial_blocks = len(state.blocks)
        visited_set = set()

        self.logger.debug(f"All Randomizing: {initial_blocks} blocks, {self.config.randomizing_steps} steps")

        stagnant_counter = 0
        last_block_count = len(state.blocks)

        for idx in range(self.config.randomizing_steps):
            if not state.blocks:
                break

            edible_blocks = state.can_eat()
            for block in edible_blocks:
                if self.config.debug_viz:
                    visualize_state(state, block_id=block.id, regime="show")
                if state.eat_block(block):
                    self.logger.debug(f"Remaining Blocks: {len(state.blocks)}, ate block: {block.id}, iteration: {idx}")
                    shuffle_path.append(('eat', block.id))

            current_block_count = len(state.blocks)
            if current_block_count <= self.min_block_for_stagnant:
                if current_block_count == last_block_count:
                    stagnant_counter += 1
                else:
                    stagnant_counter = 0
                    last_block_count = current_block_count

                if stagnant_counter >= self.config.stagnant_limit:
                    self.unsolvable = True
                    self.logger.debug(f"Stagnant detected with {current_block_count} blocks. Stopping shuffle.")
                    break

            permed_blocks = np.random.permutation(state.blocks)
            for block in permed_blocks:
                moves = state.get_valid_moves(block, max_step=10)
                if moves:
                    dx, dy = random.choice(moves)
                    new_state = copy.deepcopy(state)
                    new_state.move_block(new_state.find_block(block.id), dx, dy)
                    new_state_signature = new_state.to_hashable()

                    if new_state_signature not in visited_set:
                        shuffle_path.append((block.id, dx, dy))
                        state = new_state
                        visited_set.add(new_state_signature)

            self.metrics.iteration = idx
        self.logger.debug(f"Randomization: {initial_blocks} -> {len(state.blocks)} blocks, "
                        f"{len(shuffle_path)} moves")

        return state, shuffle_path

    def shuffle_single_block(self, state: GameStateBase) -> Tuple[GameStateBase, List]:
        shuffle_path = []
        initial_blocks = len(state.blocks)

        self.logger.debug(
            f"Single Randomizing: {initial_blocks} blocks, {self.config.randomizing_steps} steps"
        )

        stagnant_counter = 0
        last_block_count = len(state.blocks)

        for _ in range(self.config.randomizing_steps * 2):
            edible_blocks = state.can_eat()
            for block in edible_blocks:
                if self.config.debug_viz:
                    visualize_state(state, block_id=block.id, regime="show")
                if state.eat_block(state.find_block(block.id)):
                    self.logger.debug(f"Remaining Blocks: {len(state.blocks)}")
                    shuffle_path.append(("eat", block.id))

            if not state.blocks:
                break

            current_block_count = len(state.blocks)
            if current_block_count <= self.min_block_for_stagnant:
                if current_block_count == last_block_count:
                    stagnant_counter += 1
                else:
                    stagnant_counter = 0
                    last_block_count = current_block_count

                if stagnant_counter >= self.config.stagnant_limit:
                    self.logger.debug(
                        f"Stagnant detected with {current_block_count} blocks. Stopping shuffle."
                    )
                    break

            block = random.choice(state.blocks)
            moves = state.get_valid_moves(block, max_step=10)
            if moves:
                dx, dy = random.choice(moves)
                if state.move_block(block, dx, dy):
                    shuffle_path.append((block.id, dx, dy))

        self.logger.debug(
            f"Randomization: {initial_blocks} -> {len(state.blocks)} blocks, "
            f"{len(shuffle_path)} moves"
        )

        return state, shuffle_path

    def get_next_block_exit(self, state: GameStateBase):
        shuffle_path = []

        while True:
            edible_blocks = state.can_eat()
            for block in edible_blocks:
                if state.eat_block(state.find_block(block.id)):
                    shuffle_path.append(("eat", block.id))
                    return block.id, shuffle_path

            permed_blocks = np.random.permutation(state.blocks)
            for block in permed_blocks:
                moves = state.get_valid_moves(block, max_step=15)
                if moves:
                    dx, dy = random.choice(moves)
                    if state.move_block(block, dx, dy):
                        shuffle_path.append((block.id, dx, dy))

    def check_solvable(
        self,
        state: GameStateBase, 
        heuristic: Heuristic = None
    ) -> Tuple[Optional[List], dict]:
        self.logger.debug(f"Starting solver: {len(state.blocks)} blocks")
        trials = 0
        self.min_block_for_stagnant = math.floor(math.sqrt(len(state.blocks)))

        for single_block in state.blocks:
            if not can_exit_precheck(state, single_block):
                if self.config.debug_viz:
                    visualize_state(state, block_id=single_block.id, regime="show")
                self.metrics.error_block_id = single_block.id
                self.metrics.path = self.path
                return False, self.metrics.to_dict()

        while trials < self.config.max_trials:
            self._reset()
            trial_state = copy.deepcopy(state)
            while self.metrics.shuffle_count < self.config.max_shuffle:
                if self.config.debug_viz:
                    visualize_state(trial_state, regime="show")
                previous_num_blocks = len(trial_state.blocks)

                trial_state, shuffle_path = self.shuffle_all_blocks(trial_state)

                self.path.extend(shuffle_path)
                self.metrics.shuffle_count += 1
                self.metrics.shuffle_moves += len(shuffle_path)
                self.metrics.path = self.path

                if trial_state.is_win():
                    self.logger.debug(
                        f"Solved in trial {trials + 1}, shuffles: {self.metrics.shuffle_count}"
                    )
                    return True, self.metrics.to_dict()

                if self.unsolvable:
                    return False, self.metrics.to_dict()

                if len(trial_state.blocks) == previous_num_blocks:
                    break

            trials += 1

        self.logger.debug("Not solvable within trials/shuffles limit")
        return False, self.metrics.to_dict()
