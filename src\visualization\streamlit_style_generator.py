import copy
import os

import matplotlib

matplotlib.use("Agg")  # Backend non-GUI
import json
import time
import zipfile

import matplotlib.pyplot as plt
from moviepy import ImageSequenceClip
from PIL import Image  # ← THÊM PIL để resize ảnh

from src.game.game_state_base import GameStateBase as GameState
from src.game.visualization.visualize_game_state import visualize_state

FPS = 2


class StreamlitStyleGenerator:
    def __init__(self, game: GameState, task_id: str):
        self.game = game
        self.task_id = task_id
        self.output_dir = f"temp_videos/{task_id}"
        os.makedirs(self.output_dir, exist_ok=True)

        # *** CỐ ĐỊNH KÍCH THƯỚC PIXEL CHO TẤT CẢ ẢNH ***
        self.fig_width = 10
        self.fig_height = 10
        self.dpi = 150
        self.target_width = 1000  # ← THÊM: kích thước pixel cố định
        self.target_height = 1000

    def save_figure_with_fixed_size(self, fig, file_path):
        """Lưu figure với kích thước pixel cố định"""
        # Lưu tạm với bbox_inches='tight'
        temp_path = file_path.replace(".png", "_temp.png")
        fig.savefig(
            temp_path, dpi=self.dpi, bbox_inches="tight", facecolor="white", edgecolor="none"
        )
        plt.close(fig)

        # Dùng PIL để resize về kích thước chuẩn
        with Image.open(temp_path) as img:
            # Convert về RGB nếu cần (tránh RGBA issues)
            if img.mode in ("RGBA", "LA"):
                rgb_img = Image.new("RGB", img.size, (255, 255, 255))
                rgb_img.paste(img, mask=img.split()[-1] if img.mode == "RGBA" else None)
                img = rgb_img

            # Resize về kích thước chuẩn
            resized_img = img.resize(
                (self.target_width, self.target_height), Image.Resampling.LANCZOS
            )
            resized_img.save(file_path, "PNG", quality=95)

        # Xóa file tạm
        os.unlink(temp_path)

        """Tạo ảnh từng bước và video - với kích thước ảnh cố định"""

    def create_step_images_and_video(self, path):
        saved_game = copy.deepcopy(self.game)
        images = []

        try:
            # Create step images directory
            step_images_dir = f"{self.output_dir}/step_images"
            os.makedirs(step_images_dir, exist_ok=True)

            # *** BƯỚC ĐẦU - CỐ ĐỊNH KÍCH THƯỚC ***
            fig, ax = plt.subplots(figsize=(self.fig_width, self.fig_height))
            visualize_state(saved_game, ax=ax)
            ax.set_title("Bước 0 - Trạng thái ban đầu", fontsize=16, fontweight="bold")

            img_path = f"{step_images_dir}/step_0.png"
            self.save_figure_with_fixed_size(fig, img_path)  # ← SỬA: dùng hàm mới
            images.append(img_path)

            # *** CÁC BƯỚC TIẾP THEO - CỐ ĐỊNH KÍCH THƯỚC ***
            for i, step in enumerate(path):
                id = -1
                if step[0] == "eat":
                    block = saved_game.find_block(step[1])
                    if block:
                        saved_game.eat_block(block)
                    move_desc = f"Ăn block {step[1]}"
                    id = -1
                else:
                    # Format: (block_id, dx, dy)
                    block_id, dx, dy = step
                    block = saved_game.find_block(block_id)
                    if block:
                        # Sử dụng move_block với dx, dy
                        saved_game.move_block(block, dx, dy)
                    move_desc = f"Di chuyển block {block_id} ({dx:+}, {dy:+})"
                    id = block_id

                # CỐ ĐỊNH KÍCH THƯỚC CHO TẤT CẢ CÁC BƯỚC
                fig, ax = plt.subplots(figsize=(self.fig_width, self.fig_height))
                visualize_state(saved_game, ax=ax, block_id=id)
                ax.set_title(f"Bước {i + 1} - {move_desc}", fontsize=16, fontweight="bold")

                img_path = f"{step_images_dir}/step_{i + 1}.png"
                self.save_figure_with_fixed_size(fig, img_path)  # ← SỬA: dùng hàm mới
                images.append(img_path)

            print(
                f"Created {len(images)} step images with fixed size {self.target_width}x{self.target_height}"
            )

            # *** VERIFY IMAGE SIZES TRƯỚC KHI TẠO VIDEO ***
            for i, img_path in enumerate(images):
                with Image.open(img_path) as img:
                    if img.size != (self.target_width, self.target_height):
                        return {"success": False, "error": f"Image size mismatch at step {i}"}

            # *** TẠO VIDEO TỪ ÀNH - VỚI ERROR HANDLING BETTER ***
            video_path = f"{self.output_dir}/solution_video.mp4"

            try:
                print(f"🎬 Creating video from {len(images)} images...")
                clip = ImageSequenceClip(images, fps=FPS)

                # Thử các cách viết khác nhau tùy theo version MoviePy
                try:
                    # MoviePy mới
                    clip.write_videofile(video_path, codec="libx264", verbose=False, logger=None)
                except TypeError:
                    try:
                        # MoviePy cũ
                        clip.write_videofile(video_path, codec="libx264")
                    except TypeError:
                        # Fallback - chỉ dùng tham số cơ bản
                        clip.write_videofile(video_path)

                print(f"✅ Created video: {video_path}")

            except Exception as video_error:
                print(f"❌ Video creation failed: {video_error}")
                # Vẫn tiếp tục tạo ZIP mà không có video
                video_path = None

            # *** TẠO ZIP FILE ***
            zip_path = f"{self.output_dir}/solution_complete.zip"

            print(f"📦 Creating zip file: {zip_path}")

            with zipfile.ZipFile(zip_path, "w", zipfile.ZIP_DEFLATED) as zip_file:
                # Add video to zip (nếu có)
                if video_path and os.path.exists(video_path):
                    zip_file.write(video_path, "solution_video.mp4")

                # Add all step images to zip
                for img_path in images:
                    if os.path.exists(img_path):
                        filename = os.path.basename(img_path)
                        zip_file.write(img_path, f"step_images/{filename}")

                # Add solution info JSON
                solution_info = {
                    "task_id": self.task_id,
                    "total_steps": len(path),
                    "path": path,
                    "video_file": "solution_video.mp4" if video_path else None,
                    "step_images_count": len(images),
                    "image_size": f"{self.target_width}x{self.target_height}",
                    "created_at": time.time(),
                }

                # Write solution info to temp file then add to zip
                info_path = f"{self.output_dir}/solution_info.json"
                with open(info_path, "w", encoding="utf-8") as f:
                    json.dump(solution_info, f, indent=2, ensure_ascii=False)

                zip_file.write(info_path, "solution_info.json")

            return {
                "images": images,
                "video_path": video_path,
                "zip_path": zip_path,
                "success": True,
                "step_images_dir": step_images_dir,
                "image_size": f"{self.target_width}x{self.target_height}",
            }

        except Exception as e:
            print(f"❌ Lỗi tạo visualization: {str(e)}")
            import traceback

            traceback.print_exc()
            return {
                "success": False,
                "error": str(e)
            }
    
    def create_random_images(self, path, is_solvable=True, error_block_id=None):
        """
        Tạo các ảnh trạng thái trước khi ăn blocks cho thuật toán random
        Trả về danh sách các đường dẫn ảnh để hiển thị trực tiếp
        """
        saved_game = copy.deepcopy(self.game)
        images = []
        eat_states = []
        
        try:
            # Create directory cho random images
            random_images_dir = f"{self.output_dir}/random_states"
            os.makedirs(random_images_dir, exist_ok=True)
            
            # Trạng thái ban đầu
            fig, ax = plt.subplots(figsize=(self.fig_width, self.fig_height))
            visualize_state(saved_game, ax=ax)
            ax.set_title("Trạng thái ban đầu", fontsize=16, fontweight='bold')
            
            img_path = f"{random_images_dir}/initial_state.png"
            self.save_figure_with_fixed_size(fig, img_path)
            images.append(img_path)
            
            if is_solvable and path:
                # Xử lý path để tìm các trạng thái trước khi ăn
                eat_count = 0
                current_game_state = copy.deepcopy(saved_game)
                
                for i, step in enumerate(path):
                    if step[0] == 'eat':
                        # Lưu trạng thái trước khi ăn
                        block_id = step[1]
                        block = current_game_state.find_block(block_id)
                        
                        if block:
                            eat_count += 1
                            
                            # Tạo ảnh trạng thái trước khi ăn
                            fig, ax = plt.subplots(figsize=(self.fig_width, self.fig_height))
                            visualize_state(current_game_state, ax=ax, block_id=block_id)
                            ax.set_title(f"Trước khi ăn Block {block_id}", 
                                       fontsize=16, fontweight='bold', color='red')
                            
                            img_path = f"{random_images_dir}/before_eat_{block_id}_{eat_count}.png"
                            self.save_figure_with_fixed_size(fig, img_path)
                            images.append(img_path)
                            
                            eat_states.append({
                                "block_id": block_id,
                                "eat_order": eat_count,
                                "image_path": img_path,
                                "step_index": i
                            })
                            
                            # Thực hiện ăn block
                            current_game_state.eat_block(block)
                    else:
                        # Di chuyển block
                        block_id, dx, dy = step
                        block = current_game_state.find_block(block_id)
                        if block:
                            current_game_state.move_block(block, dx, dy)
                
                # Trạng thái cuối thành công
                fig, ax = plt.subplots(figsize=(self.fig_width, self.fig_height))
                visualize_state(current_game_state, ax=ax)
                ax.set_title(f"Trạng thái cuối - Đã ăn {eat_count} blocks", 
                           fontsize=16, fontweight='bold', color='green')
                
                img_path = f"{random_images_dir}/final_state_success.png"
                self.save_figure_with_fixed_size(fig, img_path)
                images.append(img_path)
                
            else:
                # SỬA: Trường hợp không giải được - CHẠY HẾT PATH ĐỂ LẤY TRẠNG THÁI CUỐI
                if path:
                    # Chạy hết path để đến trạng thái cuối cùng
                    current_game_state = copy.deepcopy(saved_game)
                    
                    for step in path:
                        if step[0] == 'eat':
                            block_id = step[1]
                            block = current_game_state.find_block(block_id)
                            if block:
                                current_game_state.eat_block(block)
                        else:
                            block_id, dx, dy = step
                            block = current_game_state.find_block(block_id)
                            if block:
                                current_game_state.move_block(block, dx, dy)
                    
                    # Tạo ảnh trạng thái cuối cùng của path (không giải được)
                    fig, ax = plt.subplots(figsize=(self.fig_width, self.fig_height))
                    visualize_state(current_game_state, ax=ax)
                    ax.set_title("Trạng thái cuối - Không thể giải tiếp", 
                               fontsize=16, fontweight='bold', color='red')
                    
                    img_path = f"{random_images_dir}/final_state_failed.png"
                    self.save_figure_with_fixed_size(fig, img_path)
                    images.append(img_path)
                else:
                    # Không có path nào - chỉ hiển thị trạng thái ban đầu
                    fig, ax = plt.subplots(figsize=(self.fig_width, self.fig_height))
                    visualize_state(saved_game, ax=ax, block_id=error_block_id)
                    ax.set_title("Không tìm thấy path nào", 
                               fontsize=16, fontweight='bold', color='red')
                    img_path = f"{random_images_dir}/no_path_found.png"
                    self.save_figure_with_fixed_size(fig, img_path)
                    images.append(img_path)
            return {
                "success": True,
                "images": images,
                "eat_states": eat_states,
                "is_solvable": is_solvable,
                "total_blocks_eaten": len(eat_states) if is_solvable else 0,
                "random_images_dir": random_images_dir
            }
            
        except Exception as e:
            print(f"❌ Lỗi tạo random images: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "error": str(e)
            }

    def cleanup(self):
        """Xóa thư mục tạm"""
        try:
            import shutil

            if os.path.exists(self.output_dir):
                shutil.rmtree(self.output_dir)
        except Exception as e:
            print(f"Warning: Không xóa được {self.output_dir}: {str(e)}")
