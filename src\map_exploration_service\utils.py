import copy

from ..game.game_state_base import Game<PERSON>tateBase


def calculate_wall_bounder(game : GameStateBase) -> int:
    occupied = set(game.walls_occupied)
    occupied.update(game.doors_occupied)
    
    num_wall = 0
    for cell in game.walls_occupied:
        flag = False
        # print(cell)
        for dx, dy in [(1, 0), (-1, 0), (0, 1), (0, -1)]:
            for i in range(1, max(game.size_x, game.size_y) + 3):
                next_cell = (cell[0] + dx * i, cell[1] + dy * i)
                if next_cell in occupied:
                    break
                
                if next_cell[0] < -1 or next_cell[0] > game.size_x or next_cell[1] < -1 or next_cell[1] > game.size_y:
                    flag = True
                    break
            if flag:
                break
        
        if flag:
            num_wall += 1
                
    return num_wall


def calculate_num_space(game : GameStateBase) -> int:
    occupied = set()
    occupied.update(game.walls_occupied)
    occupied.update(game.doors_occupied)   
    
    all_occupied = copy.deepcopy(occupied)
    all_occupied.update(game.blocks_occupied)
    
    visited = set(game.blocks[0].subBlocks[0])
    queue = [game.blocks[0].subBlocks[0]]
     
    num_space = 0
    while queue:
        current = queue.pop(0)
        
        if current not in all_occupied:
            num_space += 1
        
        for dx, dy in [(1, 0), (-1, 0), (0, 1), (0, -1)]:
            next_cell = (current[0] + dx, current[1] + dy)
            if next_cell not in visited and next_cell not in occupied:
                queue.append(next_cell)
                visited.add(next_cell)

        if num_space > 144:
            break
                
    return num_space