# Sử dụng Python 3.12 với uv pre-installed
FROM ghcr.io/astral-sh/uv:python3.12-bookworm-slim

# Thiết lập thư mục làm việc
WORKDIR /app

# Copy file pyproject.toml và uv.lock (nếu có)
COPY pyproject.toml uv.lock* ./

# Cài đặt dependencies Python với uv
RUN uv sync --locked --no-dev

# Copy toàn bộ source code
COPY . .

# Thiết lập biến môi trường
ENV PYTHONUNBUFFERED=1

EXPOSE $STREAMLIT_PORT

# Copy và cấp quyền thực thi cho script
COPY deploy/create_streamlit_secret.sh ./create_secrets.sh
RUN chmod +x ./create_secrets.sh

# Chạy script để tạo file secrets trước khi chạy ứng dụng
CMD sh -c ./create_secrets.sh && uv run --no-sync streamlit run streamlit_app.py --server.port $STREAMLIT_PORT