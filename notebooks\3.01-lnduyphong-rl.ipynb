{"cells": [{"cell_type": "code", "execution_count": 1, "id": "91a3d7f3", "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "import warnings\n", "import json\n", "from datetime import datetime\n", "from tqdm import tqdm\n", "import copy\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "sys.path.append(os.path.abspath(\"..\"))\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 2, "id": "3bc95e7b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Gym has been unmaintained since 2022 and does not support NumPy 2.0 amongst other critical functionality.\n", "Please upgrade to Gymnasium, the maintained drop-in replacement of <PERSON><PERSON>, or contact the authors of your software and request that they upgrade.\n", "Users of this version of Gym should be able to simply replace 'import gym' with 'import gymnasium as gym' in the vast majority of cases.\n", "See the migration guide at https://gymnasium.farama.org/introduction/migration_guide/ for additional information.\n"]}], "source": ["from stable_baselines3 import PPO\n", "from src.rl.env_wrapper import PuzzleEnv, SmallMapCNN"]}, {"cell_type": "code", "execution_count": 4, "id": "5d5cd287", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using cuda device\n", "Wrapping the env with a `Monitor` wrapper\n", "Wrapping the env in a DummyVecEnv.\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "-----------------------------\n", "| time/              |      |\n", "|    fps             | 297  |\n", "|    iterations      | 1    |\n", "|    time_elapsed    | 6    |\n", "|    total_timesteps | 2048 |\n", "-----------------------------\n", "Move block index: 6, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 266         |\n", "|    iterations           | 2           |\n", "|    time_elapsed         | 15          |\n", "|    total_timesteps      | 4096        |\n", "| train/                  |             |\n", "|    approx_kl            | 0.014803924 |\n", "|    clip_fraction        | 0.135       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -3.86       |\n", "|    explained_variance   | -0.159      |\n", "|    learning_rate        | 0.0003      |\n", "|    loss                 | -0.0439     |\n", "|    n_updates            | 10          |\n", "|    policy_gradient_loss | -0.0123     |\n", "|    value_loss           | 0.00617     |\n", "-----------------------------------------\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 258         |\n", "|    iterations           | 3           |\n", "|    time_elapsed         | 23          |\n", "|    total_timesteps      | 6144        |\n", "| train/                  |             |\n", "|    approx_kl            | 0.017233402 |\n", "|    clip_fraction        | 0.176       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -3.83       |\n", "|    explained_variance   | 0.0444      |\n", "|    learning_rate        | 0.0003      |\n", "|    loss                 | -0.0102     |\n", "|    n_updates            | 20          |\n", "|    policy_gradient_loss | -0.0162     |\n", "|    value_loss           | 0.000239    |\n", "-----------------------------------------\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 258         |\n", "|    iterations           | 4           |\n", "|    time_elapsed         | 31          |\n", "|    total_timesteps      | 8192        |\n", "| train/                  |             |\n", "|    approx_kl            | 0.016009351 |\n", "|    clip_fraction        | 0.177       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -3.82       |\n", "|    explained_variance   | 0.351       |\n", "|    learning_rate        | 0.0003      |\n", "|    loss                 | -0.00387    |\n", "|    n_updates            | 30          |\n", "|    policy_gradient_loss | -0.0136     |\n", "|    value_loss           | 0.000159    |\n", "-----------------------------------------\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 5, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 2, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 3, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 256         |\n", "|    iterations           | 5           |\n", "|    time_elapsed         | 39          |\n", "|    total_timesteps      | 10240       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.023081407 |\n", "|    clip_fraction        | 0.155       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -3.8        |\n", "|    explained_variance   | -0.238      |\n", "|    learning_rate        | 0.0003      |\n", "|    loss                 | -0.0444     |\n", "|    n_updates            | 40          |\n", "|    policy_gradient_loss | -0.0115     |\n", "|    value_loss           | 6.49e-05    |\n", "-----------------------------------------\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 250         |\n", "|    iterations           | 6           |\n", "|    time_elapsed         | 48          |\n", "|    total_timesteps      | 12288       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.025608467 |\n", "|    clip_fraction        | 0.268       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -3.79       |\n", "|    explained_variance   | -0.871      |\n", "|    learning_rate        | 0.0003      |\n", "|    loss                 | -0.00214    |\n", "|    n_updates            | 50          |\n", "|    policy_gradient_loss | -0.019      |\n", "|    value_loss           | 0.000161    |\n", "-----------------------------------------\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 2, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 6, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 0, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "Move block index: 1, Direction 1, Remaining blocks: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 250         |\n", "|    iterations           | 7           |\n", "|    time_elapsed         | 57          |\n", "|    total_timesteps      | 14336       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.021002453 |\n", "|    clip_fraction        | 0.216       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -3.74       |\n", "|    explained_variance   | -0.314      |\n", "|    learning_rate        | 0.0003      |\n", "|    loss                 | -0.00275    |\n", "|    n_updates            | 60          |\n", "|    policy_gradient_loss | -0.0152     |\n", "|    value_loss           | 4.57e-05    |\n", "-----------------------------------------\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 11\u001b[39m\n\u001b[32m      5\u001b[39m policy_kwargs = \u001b[38;5;28mdict\u001b[39m(\n\u001b[32m      6\u001b[39m     features_extractor_class=SmallMapCNN,\n\u001b[32m      7\u001b[39m     features_extractor_kwargs=\u001b[38;5;28mdict\u001b[39m(features_dim=\u001b[32m256\u001b[39m)\n\u001b[32m      8\u001b[39m )\n\u001b[32m     10\u001b[39m model = PPO(\u001b[33m\"\u001b[39m\u001b[33mCnnPolicy\u001b[39m\u001b[33m\"\u001b[39m, env, policy_kwargs=policy_kwargs, verbose=\u001b[32m1\u001b[39m, device=\u001b[33m\"\u001b[39m\u001b[33mcuda\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m11\u001b[39m \u001b[43mmodel\u001b[49m\u001b[43m.\u001b[49m\u001b[43mlearn\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtotal_timesteps\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m100_000\u001b[39;49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\autogluon\\Lib\\site-packages\\stable_baselines3\\ppo\\ppo.py:311\u001b[39m, in \u001b[36mPPO.learn\u001b[39m\u001b[34m(self, total_timesteps, callback, log_interval, tb_log_name, reset_num_timesteps, progress_bar)\u001b[39m\n\u001b[32m    302\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mlearn\u001b[39m(\n\u001b[32m    303\u001b[39m     \u001b[38;5;28mself\u001b[39m: SelfPPO,\n\u001b[32m    304\u001b[39m     total_timesteps: \u001b[38;5;28mint\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m    309\u001b[39m     progress_bar: \u001b[38;5;28mbool\u001b[39m = \u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[32m    310\u001b[39m ) -> SelfPPO:\n\u001b[32m--> \u001b[39m\u001b[32m311\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mlearn\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    312\u001b[39m \u001b[43m        \u001b[49m\u001b[43mtotal_timesteps\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtotal_timesteps\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    313\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcallback\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcallback\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    314\u001b[39m \u001b[43m        \u001b[49m\u001b[43mlog_interval\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlog_interval\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    315\u001b[39m \u001b[43m        \u001b[49m\u001b[43mtb_log_name\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtb_log_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    316\u001b[39m \u001b[43m        \u001b[49m\u001b[43mreset_num_timesteps\u001b[49m\u001b[43m=\u001b[49m\u001b[43mreset_num_timesteps\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    317\u001b[39m \u001b[43m        \u001b[49m\u001b[43mprogress_bar\u001b[49m\u001b[43m=\u001b[49m\u001b[43mprogress_bar\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    318\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\autogluon\\Lib\\site-packages\\stable_baselines3\\common\\on_policy_algorithm.py:337\u001b[39m, in \u001b[36mOnPolicyAlgorithm.learn\u001b[39m\u001b[34m(self, total_timesteps, callback, log_interval, tb_log_name, reset_num_timesteps, progress_bar)\u001b[39m\n\u001b[32m    334\u001b[39m         \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m.ep_info_buffer \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mN<PERSON>\u001b[39;00m\n\u001b[32m    335\u001b[39m         \u001b[38;5;28mself\u001b[39m.dump_logs(iteration)\n\u001b[32m--> \u001b[39m\u001b[32m337\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mtrain\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    339\u001b[39m callback.on_training_end()\n\u001b[32m    341\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\autogluon\\Lib\\site-packages\\stable_baselines3\\ppo\\ppo.py:277\u001b[39m, in \u001b[36mPPO.train\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    275\u001b[39m     loss.backward()\n\u001b[32m    276\u001b[39m     \u001b[38;5;66;03m# Clip grad norm\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m277\u001b[39m     \u001b[43mth\u001b[49m\u001b[43m.\u001b[49m\u001b[43mnn\u001b[49m\u001b[43m.\u001b[49m\u001b[43mutils\u001b[49m\u001b[43m.\u001b[49m\u001b[43mclip_grad_norm_\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mpolicy\u001b[49m\u001b[43m.\u001b[49m\u001b[43mparameters\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mmax_grad_norm\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    278\u001b[39m     \u001b[38;5;28mself\u001b[39m.policy.optimizer.step()\n\u001b[32m    280\u001b[39m \u001b[38;5;28mself\u001b[39m._n_updates += \u001b[32m1\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\autogluon\\Lib\\site-packages\\torch\\nn\\utils\\clip_grad.py:36\u001b[39m, in \u001b[36m_no_grad.<locals>._no_grad_wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m     34\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_no_grad_wrapper\u001b[39m(*args, **kwargs):\n\u001b[32m     35\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m torch.no_grad():\n\u001b[32m---> \u001b[39m\u001b[32m36\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\autogluon\\Lib\\site-packages\\torch\\nn\\utils\\clip_grad.py:222\u001b[39m, in \u001b[36mclip_grad_norm_\u001b[39m\u001b[34m(parameters, max_norm, norm_type, error_if_nonfinite, foreach)\u001b[39m\n\u001b[32m    220\u001b[39m grads = [p.grad \u001b[38;5;28;01mfor\u001b[39;00m p \u001b[38;5;129;01min\u001b[39;00m parameters \u001b[38;5;28;01mif\u001b[39;00m p.grad \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m]\n\u001b[32m    221\u001b[39m total_norm = _get_total_norm(grads, norm_type, error_if_nonfinite, foreach)\n\u001b[32m--> \u001b[39m\u001b[32m222\u001b[39m \u001b[43m_clip_grads_with_norm_\u001b[49m\u001b[43m(\u001b[49m\u001b[43mparameters\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_norm\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtotal_norm\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mforeach\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    223\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m total_norm\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\autogluon\\Lib\\site-packages\\torch\\nn\\utils\\clip_grad.py:36\u001b[39m, in \u001b[36m_no_grad.<locals>._no_grad_wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m     34\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_no_grad_wrapper\u001b[39m(*args, **kwargs):\n\u001b[32m     35\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m torch.no_grad():\n\u001b[32m---> \u001b[39m\u001b[32m36\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\autogluon\\Lib\\site-packages\\torch\\nn\\utils\\clip_grad.py:155\u001b[39m, in \u001b[36m_clip_grads_with_norm_\u001b[39m\u001b[34m(parameters, max_norm, total_norm, foreach)\u001b[39m\n\u001b[32m    150\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m\n\u001b[32m    151\u001b[39m grouped_grads: \u001b[38;5;28mdict\u001b[39m[\n\u001b[32m    152\u001b[39m     \u001b[38;5;28mtuple\u001b[39m[torch.device, torch.dtype], \u001b[38;5;28mtuple\u001b[39m[\u001b[38;5;28mlist\u001b[39m[\u001b[38;5;28mlist\u001b[39m[Tensor]], \u001b[38;5;28mlist\u001b[39m[\u001b[38;5;28mint\u001b[39m]]\n\u001b[32m    153\u001b[39m ] = _group_tensors_by_device_and_dtype([grads])  \u001b[38;5;66;03m# type: ignore[assignment]\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m155\u001b[39m clip_coef = \u001b[43mmax_norm\u001b[49m\u001b[43m \u001b[49m\u001b[43m/\u001b[49m\u001b[43m \u001b[49m\u001b[43m(\u001b[49m\u001b[43mtotal_norm\u001b[49m\u001b[43m \u001b[49m\u001b[43m+\u001b[49m\u001b[43m \u001b[49m\u001b[32;43m1e-6\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m    156\u001b[39m \u001b[38;5;66;03m# Note: multiplying by the clamped coef is redundant when the coef is clamped to 1, but doing so\u001b[39;00m\n\u001b[32m    157\u001b[39m \u001b[38;5;66;03m# avoids a `if clip_coef < 1:` conditional which can require a CPU <=> device synchronization\u001b[39;00m\n\u001b[32m    158\u001b[39m \u001b[38;5;66;03m# when the gradients do not reside in CPU memory.\u001b[39;00m\n\u001b[32m    159\u001b[39m clip_coef_clamped = torch.clamp(clip_coef, \u001b[38;5;28mmax\u001b[39m=\u001b[32m1.0\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\anaconda3\\envs\\autogluon\\Lib\\site-packages\\torch\\_tensor.py:33\u001b[39m, in \u001b[36m_handle_torch_function_and_wrap_type_error_to_not_implemented.<locals>.wrapped\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m     30\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_handle_torch_function_and_wrap_type_error_to_not_implemented\u001b[39m(f):\n\u001b[32m     31\u001b[39m     assigned = functools.WRAPPER_ASSIGNMENTS\n\u001b[32m---> \u001b[39m\u001b[32m33\u001b[39m     \u001b[38;5;129m@functools\u001b[39m.wraps(f, assigned=assigned)\n\u001b[32m     34\u001b[39m     \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mwrapped\u001b[39m(*args, **kwargs):\n\u001b[32m     35\u001b[39m         \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m     36\u001b[39m             \u001b[38;5;66;03m# See https://github.com/pytorch/pytorch/issues/75462\u001b[39;00m\n\u001b[32m     37\u001b[39m             \u001b[38;5;28;01mif\u001b[39;00m has_torch_function(args):\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}], "source": ["# Tạo env\n", "env = PuzzleEnv(\"../Level_Updated/Level_12.json\", debug=True)\n", "\n", "# Dùng Custom CNN\n", "policy_kwargs = dict(\n", "    features_extractor_class=SmallMapCNN,\n", "    features_extractor_kwargs=dict(features_dim=256)\n", ")\n", "\n", "model = PPO(\"CnnPolicy\", env, policy_kwargs=policy_kwargs, verbose=1, device=\"cuda\")\n", "model.learn(total_timesteps=100_000)"]}, {"cell_type": "code", "execution_count": null, "id": "25fe349e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3f4d1e14", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "autogluon", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}