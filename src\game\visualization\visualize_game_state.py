import os
from typing import Literal

from matplotlib.patches import Rectangle
import matplotlib.pyplot as plt

from src.game.const import COLORS, DoorDirection, Special_Block, Special_Door
from src.game.game_state_base import GameStateBase


def visualize_state(
    game_state : GameStateBase, 
    saved_image=False, 
    index_image=0, 
    block_id = -1, 
    ax = None,
    regime:Literal["show", "draw"] = "draw"
) -> None:
    if not ax:
        fig, ax = plt.subplots(figsize=(8, 6))
    for block in game_state.blocks:
        edge_color = "black" if block.id != block_id else "red"
        for x, y in block.subBlocks:
            rect = None
            if block.special == Special_Block.VECTOR.value:
                rect = Rectangle(
                    (x - 0.5, y - 0.5),
                    1,
                    1,
                    facecolor=COLORS[block.color],
                    edgecolor=edge_color,
                    hatch="|" if block.moveType == 2 else "-",
                    linewidth=0.5 if block.id != block_id else 2,
                )
            elif block.special == Special_Block.HEART.value:
                rect = Rectangle(
                    (x - 0.5, y - 0.5),
                    1,
                    1,
                    facecolor=COLORS[block.color],
                    edgecolor=edge_color,
                    hatch="*",
                    linewidth=0.5 if block.id != block_id else 2,
                )
            elif block.special == Special_Block.ICE.value:
                rect = Rectangle(
                    (x - 0.5, y - 0.5),
                    1,
                    1,
                    facecolor=COLORS[block.color],
                    edgecolor=edge_color,
                    hatch="X",
                    linewidth=0.5 if block.id != block_id else 2,
                )
                # Hiển thị số turnCount ở giữa block
                ax.text(
                    x,
                    y,
                    str(block.turnCount),
                    color="black",
                    fontsize=12,
                    ha="center",
                    va="center",
                    fontweight="bold",
                )
            elif block.special == Special_Block.LOCK.value:
                rect = Rectangle(
                    (x - 0.5, y - 0.5),
                    1,
                    1,
                    facecolor=COLORS[block.color],
                    edgecolor=edge_color,
                    linewidth=0.5 if block.id != block_id else 2,
                )
                # Hiển thị icon khóa
                ax.text(x, y, "lock", fontsize=14, ha="center", va="center")
            elif block.special == Special_Block.LAYER.value:
                rect = Rectangle(
                    (x - 0.5 + 0.1, y - 0.5 + 0.1),
                    0.8,
                    0.8,
                    facecolor=COLORS[block.secondColor],
                    edgecolor=COLORS[block.color],
                    linewidth=8,
                )
            elif block.special == Special_Block.BOMB.value:
                rect = Rectangle(
                    (x - 0.5, y - 0.5),
                    1,
                    1,
                    facecolor=COLORS[block.color],
                    edgecolor=edge_color,
                    linewidth=0.5 if block.id != block_id else 2,
                )
                ax.text(
                    x,
                    y,
                    str(block.turnCount),
                    color="black",
                    fontsize=12,
                    ha="center",
                    va="center",
                    fontweight="bold",
                )
            elif block.special == Special_Block.NORMAL.value:
                rect = Rectangle(
                    (x - 0.5, y - 0.5),
                    1,
                    1,
                    facecolor=COLORS[block.color],
                    edgecolor=edge_color,
                    linewidth=0.5 if block.id != block_id else 2,
                )
                if block.hasKey:
                    # Hiển thị icon chìa khóa
                    ax.text(x, y, "key", fontsize=14, ha="center", va="center")

            ax.text(
                x - 0.48,
                y + 0.48,
                str(block.id),
                color="black",
                fontsize=8,
                ha="left",
                va="top",
                fontweight="bold",
            )

            ax.add_patch(rect)

        # Draw walls
        for wall in game_state.walls:
            for x, y in wall.subBlocks:
                rect = Rectangle(
                    (x - 0.5, y - 0.5),
                    1,
                    1,
                    facecolor=COLORS[wall.color],
                    edgecolor="black",
                    linewidth=0.5
                )
                ax.add_patch(rect)

    # Draw doors
    for door in game_state.doors:
        for x, y in door.subBlocks:
            symbol = "|" if door.direction == DoorDirection.VERTICAL else "-"
            if door.special == Special_Door.HEART.value:
                rect = Rectangle(
                    (x - 0.5, y - 0.5),
                    1,
                    1,
                    facecolor=COLORS[door.color],
                    edgecolor="black",
                    hatch="*" + symbol,
                    linewidth=0.5 if door.id != block_id else 2,
                )
            elif door.special == Special_Door.ICE.value:
                rect = Rectangle(
                    (x - 0.5, y - 0.5),
                    1,
                    1,
                    facecolor=COLORS[door.color],
                    edgecolor="black",
                    hatch="X" + symbol,
                    linewidth=0.5 if door.id != block_id else 2,
                )
                # Hiển thị số turnCount ở giữa block
                ax.text(
                    x,
                    y,
                    str(door.turnCount),
                    color="black",
                    fontsize=12,
                    ha="center",
                    va="center",
                    fontweight="bold",
                )
            elif door.special == Special_Door.SHUTTER.value:
                if door.open:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        1,
                        1,
                        facecolor=COLORS[door.color],
                        edgecolor="black",
                        hatch="//" + symbol,
                    )
                else:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        1,
                        1,
                        facecolor=COLORS[door.color],
                        edgecolor="black",
                        hatch="X" + symbol,
                        alpha=0.5,
                    )
            else:
                rect = Rectangle(
                    (x - 0.5, y - 0.5),
                    1,
                    1,
                    facecolor=COLORS[door.color],
                    edgecolor="black",
                    hatch="//" + symbol,
                )
            ax.add_patch(rect)

    # Visualize special tiles
    for tile in game_state.special_tiles:
        rect = Rectangle(
            (tile.x - 0.5, tile.y - 0.5),
            1,
            1,
            color=COLORS[tile.color],
            edgecolor="black",
            linewidth=0.5,
            alpha=0.3
        )
        ax.add_patch(rect)

        # Configure plot
    ax.set_xlim(-2, game_state.size_x + 1)
    ax.set_ylim(-2, game_state.size_y + 1)
    ax.set_aspect('equal')
    ax.grid(True, alpha = 0.1)
    plt.title(f"{game_state.name} (Level {game_state.level})")
    if saved_image:
        os.makedirs(game_state.level_path, exist_ok=True)
        plt.savefig(f"{game_state.level_path}/{index_image}.jpg")
        plt.close()
    else:
        if regime == "show":
            plt.show()
        elif regime == "draw":
            plt.draw()
        else:
            raise ValueError("Invalid regime. Use 'show' or 'draw'.")
        
        
        
        
def visualize_occupied(game_state : GameStateBase) -> None:
    fig, ax = plt.subplots(figsize=(8, 6))

    # Draw occupied blocks
    for x, y in game_state.blocks_occupied:
        rect = Rectangle(
            (x - 0.5, y - 0.5), 1, 1,
            facecolor='lightgray', edgecolor='black'
        )
        ax.add_patch(rect)

    # Draw occupied walls
    for x, y in game_state.walls_occupied:
        rect = Rectangle(
            (x - 0.5, y - 0.5), 1, 1,
            facecolor='darkgray', edgecolor='black'
        )
        ax.add_patch(rect)

    # Draw occupied doors
    for x, y in game_state.doors_occupied:
        rect = Rectangle(
            
            (x - 0.5, y - 0.5), 1, 1,
            facecolor='lightblue', edgecolor='black', hatch='//'
        )
        ax.add_patch(rect)

    # Configure plot
    ax.set_xlim(-2, game_state.size_x + 1)
    ax.set_ylim(-2, game_state.size_y + 1)
    ax.set_aspect('equal')
    ax.grid(True, alpha=0.3)
    plt.title("Occupied Cells")
    plt.show()