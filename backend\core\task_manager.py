from enum import Enum
import json
import logging
import multiprocessing
import os
import threading
import time
from typing import Any, Callable
import uuid


class TaskStatus(Enum):
    NOT_FOUND = "not_found"
    PROCESSING = "processing"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ERROR = "error"


logger = logging.getLogger(__name__)


def _run_task_wrapper(results, task_id, func, args):
    """
    Hàm bao bọc này sẽ được chạy trong process con.
    Nó thực thi hàm của bạn và lưu kết quả vào dictionary chia sẻ.
    """
    try:
        result = func(*args)
        results[task_id] = {"status": TaskStatus.COMPLETED.value, "data": result}
    except Exception as e:
        results[task_id] = {"status": TaskStatus.ERROR.value, "data": str(e)}


class TaskManager:
    def __init__(self):
        # Dictionary để lưu các process: {task_id: multiprocessing.Process}
        self.tasks: dict[str, multiprocessing.Process] = {}
        # Dictionary để lưu callback: {task_id: callback_function}
        self.callbacks = {}
        self.manager = None
        self.results = None
        # Thread để theo dõi callback
        self.callback_thread = None
        self.callback_thread_running = False
        self.callback_thread_lock = threading.Lock()

    def _ensure_manager(self):
        """Khởi tạo Manager chỉ khi cần thiết"""
        if self.manager is None:
            self.manager = multiprocessing.Manager()
            self.results = self.manager.dict()

    def _start_callback_thread(self):
        """Khởi động thread theo dõi callback nếu chưa chạy"""
        with self.callback_thread_lock:
            if not self.callback_thread_running:
                self.callback_thread_running = True
                self.callback_thread = threading.Thread(target=self._callback_monitor, daemon=True)
                self.callback_thread.start()
                logger.info("Callback thread đã được khởi động")

    def _callback_monitor(self):
        """Thread theo dõi các task và gọi callback khi hoàn thành"""
        while self.callback_thread_running:
            try:
                # Kiểm tra các task đã hoàn thành
                completed_tasks = []

                for task_id, process in list(self.tasks.items()):
                    if not process.is_alive() and task_id in self.callbacks:
                        # Kiểm tra xem task đã có kết quả chưa
                        if self.results and task_id in self.results:
                            completed_tasks.append(task_id)

                # Gọi callback cho các task đã hoàn thành
                for task_id in completed_tasks:
                    try:
                        callback = self.callbacks[task_id]
                        task_info = self.results.get(task_id, {})

                        # Gọi callback với thông tin task
                        if callback:
                            callback(task_id, task_info.get("status"), task_info.get("data"))

                        # Xóa callback đã sử dụng
                        del self.callbacks[task_id]

                    except Exception as e:
                        logger.error(f"Lỗi khi gọi callback cho task {task_id}: {e}")

                time.sleep(0.1)

            except Exception as e:
                logger.error(f"Lỗi trong callback monitor thread: {e}")
                time.sleep(1)

    def submit(
        self,
        func: Callable,
        *args: Any,
        callback: Callable | None = None,
        task_id_index: int | None = None,
    ):
        """
        Submit một tác vụ mới để chạy trong một tiến trình riêng biệt.

        Args:
            func (Callable): Hàm sẽ được thực thi trong tiến trình mới.
            *args (Any): Các đối số truyền vào cho hàm func.
            callback (Callable | None, optional): Hàm callback sẽ được gọi khi task hoàn thành.
                Callback nhận các tham số: (task_id, status, data). Mặc định là None.
            task_id_index (int | None, optional): Nếu được chỉ định, lấy task_id từ vị trí này trong args.
                Nếu không, sẽ tự động sinh một UUID mới làm task_id. Cái này là quick hack để tránh phải tạo lại task_id.

        Returns:
            str: ID duy nhất của tác vụ đã được submit.

        Notes:
            - Hàm này sẽ khởi tạo một process mới để thực thi func với các args đã cho.
            - Nếu có callback, sẽ lưu lại callback và chạy callback khi task hoàn thành, hủy hoặc lỗi.
            - Tiến độ task sẽ được cập nhật về 0% khi bắt đầu.
        """
        # Đảm bảo Manager đã được khởi tạo
        self._ensure_manager()

        if task_id_index is None:
            task_id = str(uuid.uuid4())
        else:
            task_id = args[task_id_index]
        # Khởi tạo một process mới
        p = multiprocessing.Process(
            target=_run_task_wrapper, args=(self.results, task_id, func, args)
        )
        self.tasks[task_id] = p
        self.update_progress(task_id=task_id, progress=0, message="Đang chạy...")

        # Lưu callback nếu có
        if callback:
            self.callbacks[task_id] = callback
            # Khởi động callback thread nếu chưa chạy
            self._start_callback_thread()

        p.start()

        return task_id

    def get_progress_message(self, task_id: str):
        """Lấy tiến độ của tác vụ"""
        try:
            with open(f"progress_logs/{task_id}.json", "r") as f:
                progress_data = json.load(f)
            return progress_data
        except FileNotFoundError:
            return None

    def update_progress(self, task_id: str, progress: int, message: str):
        """
        Cập nhật tiến độ của tác vụ.

        Args:
            task_id (str): ID của tác vụ cần cập nhật tiến độ.
            progress (int): Phần trăm tiến độ (0-100).
            message (str): Thông điệp mô tả trạng thái hiện tại.

        Notes:
            - Hàm này sẽ ghi log tiến độ ra file JSON trong thư mục 'progress_logs'.
            - Nếu thư mục chưa tồn tại sẽ tự động tạo mới.
            - Mỗi lần gọi sẽ ghi đè file tiến độ, chỉ lưu trạng thái mới nhất.
            - Có thể dùng để hiển thị tiến độ real-time cho user.
        """
        logger.info(f"{task_id}: {progress}% - {message}")
        progress_data = {
            "progress": progress,
            "message": message,
        }
        # log progress to file
        # create folder and file if not exists
        os.makedirs("progress_logs", exist_ok=True)

        # Ghi đè file tiến độ, chỉ lưu trạng thái mới nhất
        with open(f"progress_logs/{task_id}.json", "w") as f:
            json.dump(progress_data, f)

    def get_status(self, task_id: str):
        """Kiểm tra trạng thái của một tác vụ dựa trên ID."""
        p = self.tasks.get(task_id)
        if not p:
            return TaskStatus.NOT_FOUND.value

        if p.is_alive():
            return TaskStatus.PROCESSING.value
        else:
            # Đảm bảo Manager đã được khởi tạo
            if self.results is None:
                return TaskStatus.ERROR.value

            # Kiểm tra kết quả trong dictionary chia sẻ
            task_info = self.results.get(task_id)
            if task_info:
                return task_info.get("status", TaskStatus.COMPLETED.value)

            # Nếu process đã chết nhưng không có kết quả, có thể nó bị lỗi
            return TaskStatus.ERROR.value

    def get_data(self, task_id: str):
        """Lấy giá trị trả về của tác vụ."""
        p = self.tasks.get(task_id)
        if not p:
            raise ValueError("Task ID not found")

        p.join()  # Chờ process kết thúc

        # Đảm bảo Manager đã được khởi tạo
        if self.results is None:
            raise RuntimeError("No tasks have been submitted yet")

        task_info = self.results.get(task_id)
        if task_info and task_info.get("status") == TaskStatus.COMPLETED.value:
            return task_info.get("data")
        elif task_info:
            raise RuntimeError(f"Task failed with status: {task_info.get('status')}")
        else:
            raise RuntimeError("Task failed or was not completed successfully")

    def cancel(self, task_id: str):
        """
        Ngắt một tác vụ đang chạy.
        Trả về True nếu ngắt thành công, False nếu không.
        """
        p = self.tasks.get(task_id)
        if not p:
            return False

        if p.is_alive():
            logger.info(f"Ngắt process {p.pid} cho tác vụ {task_id}")
            p.terminate()  # Hủy process một cách đột ngột
            if p.is_alive():
                p.kill()

            # Đảm bảo Manager đã được khởi tạo
            if self.results is not None:
                # self.results[task_id] = {"status": TaskStatus.CANCELLED.value, "data": None}
                # Có data message thông báo
                self.results[task_id] = {
                    "status": TaskStatus.CANCELLED.value,
                    "data": "Task was cancelled",
                }
            return True

        return False  # Process đã không còn chạy nữa

    def shutdown(self):
        """Tắt tất cả các process đang chạy."""
        # Dừng callback thread
        with self.callback_thread_lock:
            self.callback_thread_running = False

        if self.callback_thread and self.callback_thread.is_alive():
            self.callback_thread.join(timeout=1)
            logger.info("Callback thread đã được dừng")

        for task_id, p in self.tasks.items():
            if p.is_alive():
                logger.info(f"Đóng process {p.pid} cho tác vụ {task_id}")
                p.terminate()
                p.join(timeout=2)
                if p.is_alive():
                    p.kill()

        # Chỉ shutdown Manager nếu đã được khởi tạo
        if self.manager is not None:
            self.manager.shutdown()

    def is_cancelled(self, task_id: str):
        """Kiểm tra xem tác vụ đã bị hủy hay chưa"""
        return self.get_status(task_id) == TaskStatus.CANCELLED.value

    def get_task_id(self, process: multiprocessing.Process):
        """Get the task ID of a process"""
        for task_id, task_process in self.tasks.items():
            if task_process == process:
                return task_id
        return None

    def to_dict(self):
        """Convert the tasks to a dictionary"""
        self._ensure_manager()
        progress_map = {
            TaskStatus.PROCESSING.value: 0,
            TaskStatus.COMPLETED.value: 100,
            TaskStatus.CANCELLED.value: 100,
            TaskStatus.ERROR.value: 100,
        }
        return {
            task_id: {
                "status": self.get_status(task_id),
                "process": str(self.tasks[task_id]),
                "process_address": id(self.tasks[task_id]),
                "process_pid": self.tasks[task_id].pid,
                "progress": progress_map[self.get_status(task_id)],
                "message": self.get_status(task_id),
            }
            for task_id in self.tasks
        }


task_manager = TaskManager()
