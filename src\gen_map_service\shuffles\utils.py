from collections import deque
from typing import List, <PERSON>ple

from src.game.game_state_base import Game<PERSON>tateBase
from src.game.objects import Object, Wall


def is_valid_move_wall(state: GameStateBase, object: Object, dx: int, dy: int) -> bool:
        adjacent_door_occupied = set()
        directions = [(-1, 0), (1, 0), (0, -1), (0, 1)]

        for door_pos in state.doors_occupied:
            for dx_adj, dy_adj in directions:
                neighbor = (door_pos[0] + dx_adj, door_pos[1] + dy_adj)
                adjacent_door_occupied.add(neighbor)

        for sub in object.get_subBlocks():
            new_x = sub[0] + dx
            new_y = sub[1] + dy

            new_pos = (new_x, new_y)
            for tile in state.special_tiles:
                if (tile.x, tile.y) == new_pos and object.color != tile.color:
                    return False

            if (
                new_pos in state.blocks_occupied
                or new_pos in state.doors_occupied
                or new_pos in adjacent_door_occupied
            ):
                return False
        return True

def get_valid_moves(state: GameStateBase, object: Object, max_step: int = 1) -> List[Tuple[int, int]]:
    max_step = min(max_step, state.size_x, state.size_y)
    valid_moves = []

    state.remove_occupied(object)
    visited = set()
    dq = deque([(0, 0, 0)])
    visited.add((0, 0))

    while dq:
        dx, dy, steps = dq.popleft()
        if dx != 0 or dy != 0:
            valid_moves.append((dx, dy))

        if steps >= max_step:
            continue

        for dir_dx, dir_dy in object.directions.values():
            new_dx = dx + dir_dx
            new_dy = dy + dir_dy

            if isinstance(object, Wall):
                if state.is_valid_move_wall(object, new_dx, new_dy):
                    new_pos = (new_dx, new_dy)
                    if new_pos not in visited:
                        visited.add(new_pos)
                        dq.append((new_dx, new_dy, steps + 1))
                continue

            if state.is_valid_move(object, new_dx, new_dy):
                new_pos = (new_dx, new_dy)
                if new_pos not in visited:
                    visited.add(new_pos)
                    dq.append((new_dx, new_dy, steps + 1))

    state.add_occupied(object)
    return valid_moves

def move_object(state: GameStateBase, object: Object, dx: int, dy: int) -> bool:
    state.remove_occupied(object)
    object.move(dx, dy)
    state.add_occupied(object)
    return True