from collections import deque
import json
import os
import struct
from typing import List, Set, Tuple

from src.game.const import DoorDirection, Special_Block, Special_Door
from src.game.objects import Block, Door, Object, Wall

from .special_tile import SpecialTile


class GameStateBase:
    def __init__(self, path: str) -> None:
        self.blocks: List[Block] = []
        self.walls: List[Wall] = []
        self.doors: List[Door] = []
        self.ice_blocks: Set[int] = set()
        self.key_blocks: Set[int] = set()
        self.ice_doors: Set[int] = set()
        self.shutter_doors: Set[int] = set()
        self.special_tiles: List[SpecialTile] = []

        # Tập hợp các ô đã chiếm
        self.blocks_occupied = set()
        self.walls_occupied = set()
        self.doors_occupied = set()
        self.special_tiles_occupied = set()

        """Khởi tạo GameState từ dữ liệu JSON"""
        data = json.load(open(path, "r"))
        if data is None:
            raise ValueError("Invalid game state data")
        self.level = data["level"]
        self.difficulty = data["difficulty"]
        self.name = self.level
        self.time = data["time"]
        tilelist_data = data.get("specialTileList", [])
        id = 0
        for tile in tilelist_data:
            temp_tile = SpecialTile(id, tile["specialTile"], tile["gridPos"], tile["color"])
            self.special_tiles.append(temp_tile)
            id += 1

        # Là typle (x, y)
        self.size_x = data["size"]["x"]
        self.size_y = data["size"]["y"]

        # Load Block objects
        id = 0
        for block_data in data["blockList"]:
            temp_block = Block(block_data, id)
            if temp_block.special == Special_Block.ICE.value:
                self.ice_blocks.add(temp_block.id)
            if temp_block.hasKey:
                self.key_blocks.add(temp_block.id)
            self.blocks.append(temp_block)
            id += 1

        # Load Wall objects
        for wall_data in data["wallList"]:
            self.walls.append(Wall(wall_data))

        # Load Door objects
        id = 0
        for door_data in data["doorList"]:
            temp_door = Door(door_data, id)
            self.doors.append(temp_door)
            if temp_door.special == Special_Door.ICE.value:
                self.ice_doors.add(temp_door.id)
            elif temp_door.special == Special_Door.SHUTTER.value:
                self.shutter_doors.add(temp_door.id)
            id += 1

        # Tạo tập hợp các ô đã chiếm
        for block in self.blocks:
            self.blocks_occupied.update(block.subBlocks)
        for wall in self.walls:
            self.walls_occupied.update(wall.subBlocks)
        for door in self.doors:
            self.doors_occupied.update(door.subBlocks)
        for special_tile in self.special_tiles:
            self.special_tiles_occupied.add((special_tile.x, special_tile.y))

        # Other properties
        self.unnormalTileList = data["unnormalTileList"]
        # self.tileList = data["tileList"]
        self.level_path = os.path.join("Output", f"level_{self.level}")

    def find_block(self, id: int) -> Block:
        """Tìm block theo id"""
        for block in self.blocks:
            if block.id == id:
                return block
        return None

    def move_block(self, block: Block, dx: int, dy: int, time: int = 1) -> bool:
        """Di chuyển block nếu hợp lệ"""
        # if not self.is_valid_move(block, dx, dy):
        #     return False
        for i in range(time):
            self.remove_occupied(block)
            block.move(dx, dy)
            self.add_occupied(block)
        return True

    def remove_occupied(self, block: Block) -> bool:
        """Remove occupied of a block"""
        if block.subBlocks:
            self.blocks_occupied.difference_update(block.subBlocks)
            return True
        return False

    def add_occupied(self, block: Block) -> bool:
        """Add occupied of a block"""
        if block.subBlocks:
            self.blocks_occupied.update(block.subBlocks)
            return True
        return False

    def eat_block(self, block: Block) -> bool:
        """Xoá block khỏi game state"""
        if block.special == Special_Block.LAYER.value:
            block.special = Special_Block.NORMAL.value
            block.color = block.secondColor
        else:
            self.remove_occupied(block)
            self.blocks.remove(block)

        # Cập nhật các ice blocks
        to_remove = []
        for b_id in self.ice_blocks:
            for b in self.blocks:
                if b.id == b_id:
                    b.turnCount -= 1
                    if b.turnCount <= 0:
                        to_remove.append(b_id)
        self.ice_blocks.difference_update(to_remove)

        # Cập nhật các ice doors
        to_remove = []
        for d_id in self.ice_doors:
            for d in self.doors:
                if d.id == d_id:
                    d.turnCount -= 1
                    if d.turnCount <= 0:
                        to_remove.append(d_id)
        self.ice_doors.difference_update(to_remove)

        # Cập nhật các shutter doors
        for d_id in self.shutter_doors:
            for d in self.doors:
                if d.id == d_id:
                    d.open = not d.open

        # Cập nhật các key blocks
        to_remove = []
        for b_id in self.key_blocks:
            if block.id == b_id:
                to_remove.append(b_id)
        self.key_blocks.difference_update(to_remove)
        return True

    def is_accessible_door(self, block: Block, door: Door) -> bool:
        """Check if a door is accessible for a specific block"""
        if door.color != block.color:
            return False

        door_is_accessible = False

        # Handle Heart blocks
        if block.special == Special_Block.HEART.value:
            if door.special == Special_Door.HEART.value:
                door_is_accessible = True

        # Handle Ice doors
        elif door.special == Special_Door.ICE.value:
            if door.turnCount <= 0:
                door_is_accessible = True

        # Handle Shutter doors
        elif door.special == Special_Door.SHUTTER.value:
            if door.open:
                door_is_accessible = True
        else:
            door_is_accessible = True

        min_x = min(block.subBlocks, key=lambda cell: cell[0])[0]
        min_y = min(block.subBlocks, key=lambda cell: cell[1])[1]
        max_x = max(block.subBlocks, key=lambda cell: cell[0])[0]
        max_y = max(block.subBlocks, key=lambda cell: cell[1])[1]

        if block.moveType != 0:
            min_door_x = min(door.subBlocks, key=lambda cell: cell[0])[0]
            min_door_y = min(door.subBlocks, key=lambda cell: cell[1])[1]
            max_door_x = max(door.subBlocks, key=lambda cell: cell[0])[0]
            max_door_y = max(door.subBlocks, key=lambda cell: cell[1])[1]

            if door.direction == DoorDirection.VERTICAL:
                if not (min_door_y <= min_y and max_y <= max_door_y):
                    door_is_accessible = False
            else:
                if not (min_door_x <= min_x and max_x <= max_door_x):
                    door_is_accessible = False

        if door.direction == DoorDirection.VERTICAL:
            if max_y - min_y + 1 > len(door.subBlocks):
                door_is_accessible = False
        else:
            if max_x - min_x + 1 > len(door.subBlocks):
                door_is_accessible = False

        return door_is_accessible

    def get_accessible_doors(self, block: Block) -> List[Door]:
        """Lấy danh sách các cửa có thể truy cập cho block"""
        accessible_doors = []
        for door in self.doors:
            if self.is_accessible_door(block, door):
                accessible_doors.append(door)
        return accessible_doors

    def can_move_block(self, block: Block) -> bool:
        """Kiểm tra xem block có thể di chuyển hay không"""
        if block.special == Special_Block.ICE.value and block.turnCount > 0:
            return False
        if block.special == Special_Block.LOCK.value and len(self.key_blocks) > 0:
            return False
        return True

    def can_cook(self, block: Block) -> bool:
        """Kiểm tra xem block có thể nấu ăn hay không"""
        if self.can_move_block(block) is False:
            return False
        occupied = set(self.doors_occupied)
        occupied.update(self.blocks_occupied)
        occupied.update(self.walls_occupied)

        for special_tile in self.special_tiles:
            if special_tile.special == 2 and special_tile.color != block.color:
                if (special_tile.x, special_tile.y) not in occupied:
                    occupied.add((special_tile.x, special_tile.y))

        # Loại bỏ các ô hiện tại của block
        for cell in block.subBlocks:
            if cell in occupied:
                occupied.remove(cell)

        min_x = min(block.subBlocks, key=lambda cell: cell[0])[0]
        min_y = min(block.subBlocks, key=lambda cell: cell[1])[1]
        max_x = max(block.subBlocks, key=lambda cell: cell[0])[0]
        max_y = max(block.subBlocks, key=lambda cell: cell[1])[1]

        eatable = False
        list_doors = self.get_accessible_doors(block)
        for door in list_doors:
            min_door_x = min(door.subBlocks, key=lambda cell: cell[0])[0]
            min_door_y = min(door.subBlocks, key=lambda cell: cell[1])[1]
            max_door_x = max(door.subBlocks, key=lambda cell: cell[0])[0]
            max_door_y = max(door.subBlocks, key=lambda cell: cell[1])[1]

            skip = False
            if door.direction == DoorDirection.VERTICAL:
                # Cua nam doc
                if not (min_door_y <= min_y and max_y <= max_door_y):
                    skip = True
                assert min_door_x == max_door_x
                for subBlock in block.subBlocks:
                    for i in range(min(subBlock[0], min_door_x) + 1, max(subBlock[0], min_door_x)):
                        if (i, subBlock[1]) in occupied:
                            skip = True
            else:
                # Cua nam ngang
                if not (min_door_x <= min_x and max_x <= max_door_x):
                    skip = True
                assert min_door_y == max_door_y
                for subBlock in block.subBlocks:
                    for i in range(min(subBlock[1], min_door_y) + 1, max(subBlock[1], min_door_y)):
                        if (subBlock[0], i) in occupied:
                            skip = True

            if not skip:
                eatable = True

            if eatable:
                break

        return eatable

    def can_exit(self, block: Block) -> bool:
        if block.special == Special_Block.ICE.value and block.turnCount > 0:
            return False
        occupied = set(self.doors_occupied)
        occupied.update(self.blocks_occupied)
        occupied.update(self.walls_occupied)

        # Loại bỏ các ô hiện tại của block
        for cell in block.subBlocks:
            if cell in occupied:
                occupied.remove(cell)

        for special_tile in self.special_tiles:
            if special_tile.special == 2 and special_tile.color != block.color:
                if (special_tile.x, special_tile.y) not in occupied:
                    occupied.add((special_tile.x, special_tile.y))

        # temp_game = copy.deepcopy(self)

        if self.can_cook(self.find_block(block.id)):
            return True

        # Thử từng hướng di chuyển
        visited = set((0, 0))
        stack = [(0, 0)]
        while stack:
            (ndx, ndy) = stack.pop()
            # visited.add(current)
            # (ndx, ndy) = (current[0] - block.subBlocks[0][0], current[1] - block.subBlocks[0][1])
            # current_cells = [(x + ndx, y + ndy) for (x, y) in block.subBlocks]

            # self.move_block(self.find_block(block.id), ndx, ndy)

            for dx, dy in block.directions.values():
                if (ndx + dx, ndy + dy) in visited:
                    continue

                moved_cells = [(x + ndx + dx, y + ndy + dy) for (x, y) in block.subBlocks]

                if any(cell in occupied for cell in moved_cells):
                    continue

                self.move_block(block, ndx + dx, ndy + dy)
                if self.can_cook(block):
                    self.move_block(block, -ndx - dx, -ndy - dy)
                    return True

                self.move_block(block, -ndx - dx, -ndy - dy)  # Undo move

                stack.append((ndx + dx, ndy + dy))
                visited.add((ndx + dx, ndy + dy))

            # self.move_block(self.find_block(block.id), -ndx, -ndy) # Undo move

        return False

    def can_eat(self) -> List[Block]:
        """Sử dụng DFS để tìm các block có thể ăn được"""
        result = []

        for block in self.blocks:
            has_matching_door = False
            for door in self.doors:
                if self.is_accessible_door(block, door):
                    has_matching_door = True
                    break

            if has_matching_door and self.can_exit(block):
                result.append(block)

        return result

    def is_valid_move(self, object: Object, dx: int, dy: int) -> bool:
        for sub in object.get_subBlocks():
            new_x = sub[0] + dx
            new_y = sub[1] + dy

            if -1 >= new_x or new_x >= self.size_x or -1 >= new_y or new_y >= self.size_y:
                return False

            new_pos = (new_x, new_y)
            for tile in self.special_tiles:
                if (tile.x, tile.y) == new_pos and object.color != tile.color:
                    return False
            if (
                new_pos in self.blocks_occupied
                or new_pos in self.walls_occupied
                or new_pos in self.doors_occupied
            ):
                return False
        return True

    def is_win(self) -> bool:
        return len(self.blocks) == 0

    def get_valid_moves(self, block: Block, max_step: int = 1) -> List[Tuple[int, int]]:
        """Lấy danh sách các hướng di chuyển hợp lệ cho block"""
        if block.special == Special_Block.ICE.value and block.turnCount > 0:
            return []
        if block.special == Special_Block.LOCK.value and len(self.key_blocks) > 0:
            return []

        max_step = min(max_step, self.size_x, self.size_y)
        valid_moves = []

        self.remove_occupied(block)
        visited = set()
        dq = deque([(0, 0, 0)])
        visited.add((0, 0))

        while dq:
            dx, dy, steps = dq.popleft()
            if dx != 0 or dy != 0:
                valid_moves.append((dx, dy))

            if steps >= max_step:
                continue

            for dir_dx, dir_dy in block.directions.values():
                new_dx = dx + dir_dx
                new_dy = dy + dir_dy

                if self.is_valid_move(block, new_dx, new_dy):
                    new_pos = (new_dx, new_dy)
                    if new_pos not in visited:
                        visited.add(new_pos)
                        dq.append((new_dx, new_dy, steps + 1))

        self.add_occupied(block)
        return valid_moves

    def __str__(self) -> str:
        """String representation of the game state."""
        return f"GameState(level={self.level}, difficulty={self.difficulty}, name={self.name}, time={self.time}, size=({self.size_x}, {self.size_y}), \
                blocks={len(self.blocks)}, walls={len(self.walls)}, doors={len(self.doors)})"

    def to_hashable(self) -> int:
        """Hash nhanh bằng encoding nhị phân, không sort."""
        buf = bytearray()

        # Encode blocks
        for b in self.blocks:
            buf.extend(
                struct.pack(
                    "<iiiiibi",
                    b.id,
                    b.color,
                    b.secondColor,
                    b.special,
                    b.turnCount,
                    int(b.hasKey),
                    b.moveType,
                )
            )
            for x, y in b.subBlocks:
                buf.extend(struct.pack("<hh", x, y))

        return hash(bytes(buf))

    def to_dict(self):
        """Convert GameState to dictionary for JSON export"""
        return {
            "level": self.level,
            "name": self.name,
            "difficulty": self.difficulty,
            "size_x": self.size_x,
            "size_y": self.size_y,
            "blocks": [
                block.to_dict() if hasattr(block, "to_dict") else block.__dict__
                for block in self.blocks
            ],
            "doors": [
                door.to_dict() if hasattr(door, "to_dict") else door.__dict__ for door in self.doors
            ],
            # Add other necessary fields
        }

    def to_json(self):
        """Convert GameState to JSON for file export"""
        # Map to Level JSON schema like Level_1.json
        block_positions = {b.id: self.get_block_position(b) for b in self.blocks}
        res = {
            "level": self.level,
            "difficulty": self.difficulty,
            "name": "Level_",
            "camPos": {"x": 0.0, "y": 0.0, "z": 0.0},
            "camFov": 0.0,
            "time": getattr(self, "time", 300),
            "size": {"x": self.size_x, "y": self.size_y},
            "unnormalTileList": self.unnormalTileList,
            "specialTileList": [tile.to_json() for tile in self.special_tiles],
            "tileList": [],
            "blockList": [
                {
                    "prefabPath": b.prefabPath,
                    "position": {
                        "x": block_positions[b.id][0],
                        "y": b.pos_y,
                        "z": block_positions[b.id][1],
                    },
                    "rotation": b.rotation,
                    "scale": {"x": 0.5, "y": 0.5, "z": 0.5},
                    "color": b.color,
                    "special": b.special,
                    "secondColor": b.secondColor,
                    "moveType": b.moveType,
                    "turnCount": b.turnCount,
                    "timeCount": b.timeCount,
                    "hasKey": b.hasKey,
                }
                for b in self.blocks
            ],
            "wallList": [
                {
                    "prefabPath": w.prefabPath,
                    "position": w.position,
                    "rotation": w.rotation,
                    "scale": getattr(w, "scale", {"x": 0.5, "y": 0.5, "z": 0.5}),
                }
                for w in self.walls
            ],
            "doorList": [
                {
                    "prefabPath": d.prefabPath,
                    "position": {"x": d.pos_x, "y": d.pos_y, "z": d.pos_z},
                    "rotation": d.rotation,
                    "scale": getattr(d, "scale", {"x": 0.5, "y": 0.5, "z": 0.5}),
                    "color": d.color,
                    "special": d.special,
                }
                for d in self.doors
            ],
        }
        return res

    def get_block_position(self, block: Block) -> Tuple[float, float]:
        min_x = block.subBlocks[0][0]
        max_x = block.subBlocks[0][0]
        min_y = block.subBlocks[0][1]
        max_y = block.subBlocks[0][1]
        for b in block.subBlocks:
            min_x = min(min_x, b[0])
            max_x = max(max_x, b[0])
            min_y = min(min_y, b[1])
            max_y = max(max_y, b[1])
        return (min_x + max_x) / 2, (min_y + max_y) / 2
