from dataclasses import dataclass, field
from typing import Any, Dict, List


@dataclass
class ExperimentConfig:
    # config for base block solver
    lookahead_depth: int = 2
    check_every: int = 5
    patience: int = 10
    max_iter: int = 30000
    debug_img: bool = False

    randomizing_steps: int = 15000
    max_shuffle: int = 3
    max_trials: int = 3

    stagnant_limit: int = 1000
    min_block_for_stagnant: int = 4

    # Logging
    log_level: str = "DEBUG"
    debug_viz: bool = True  # False


@dataclass
class SolverMetrics:
    shuffle_count: int = 0
    shuffle_moves: int = 0
    shuffle_events: List[Dict] = field(default_factory=list)

    def to_dict(self) -> Dict[str, Any]:
        return {k: v for k, v in self.__dict__.items()}
