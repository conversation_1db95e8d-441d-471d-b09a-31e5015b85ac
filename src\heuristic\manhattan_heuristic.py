from ..game.const import Special_Block, Special_Door
from ..game.game_state_base import GameStateBase
from .heuristic import Heuristic


class ManhattanHeuristic(Heuristic):
    def __init__(self):
        super().__init__()

    def run(self, state: GameStateBase) -> float:
        total_distance = 0.0
        
        for block in state.blocks:
            min_distance = float('inf')
            
            if block.special == Special_Block.HEART.value:
                matching_doors = [door for door in state.doors 
                                if door.color == block.color and door.special == Special_Door.HEART.value]
            else:
                matching_doors = [door for door in state.doors if door.color == block.color]
            for door in matching_doors:
                if door.color == block.color:
                    for block_cell in block.subBlocks:
                        for door_cell in door.subBlocks:
                            distance = abs(block_cell[0] - door_cell[0]) + abs(block_cell[1] - door_cell[1])
                            min_distance = min(min_distance, distance)
            
            total_distance += len(state.blocks) * 30

            total_distance += min_distance
            
        return total_distance