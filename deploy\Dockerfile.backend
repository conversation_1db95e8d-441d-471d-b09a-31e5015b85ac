FROM python:3.12-slim

# Install uv.
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

WORKDIR /app
# Copy the dependency file (e.g., pyproject.toml) first.
COPY pyproject.toml uv.lock* ./

# Install the application dependencies.
RUN uv sync --frozen --no-cache --no-dev

# Copy the application into the container.
COPY . ./

# Run the application.
CMD uv run --no-sync uvicorn api:app --port $BACKEND_PORT --host 0.0.0.0 --log-config log_conf.yaml