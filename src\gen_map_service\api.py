import copy
from enum import Enum
import random
from typing import List

from src.check_solvable_service.config import ExperimentConfig
from src.check_solvable_service.solver import PuzzleSolver
from src.game.game_state_base import GameStateBase
from src.game.objects import Block, Door
from src.game.visualization.visualize_game_state import visualize_state
from src.heuristic.manhattan_heuristic import ManhattanHeuristic

from .shuffles.random_shuffle import random_shuffle
from .shuffles.reverse_path_shuffle import strategic_shuffle
from .utils.block_utils import add_blocks, get_all_exitable_blocks, have_same_size
from .utils.map_utils import swap_color, swap_speciality


def permute_map_naive(original_state: GameStateBase):
    gened_state: GameStateBase = copy.deepcopy(original_state)

    # Đổi màu cửa (giữ nguyên prefabPath và direction)
    for door in gened_state.doors:
        transferable_doors: List[Door] = [
            d
            for d in gened_state.doors
            if d is not door
            and d.prefabPath == door.prefabPath
            and d.direction == door.direction
            and door.special == d.special
        ]
        if transferable_doors:
            selected_door = random.choice(transferable_doors)
            swap_color(door, selected_door)
            swap_speciality(door, selected_door)

    # Đổi màu block (giữ nguyên prefabPath và kích thước)
    for block in gened_state.blocks:
        transferable_blocks: List[Block] = [
            b
            for b in gened_state.blocks
            if b is not block
            and b.prefabPath == block.prefabPath
            and have_same_size(b, block)
            and b.special == block.special
        ]
        if transferable_blocks:
            selected_block = random.choice(transferable_blocks)
            swap_color(block, selected_block)
            # swap_speciality(block, selected_block)

    random_shuffle(gened_state, 1000)
    add_blocks(gened_state, num_blocks=random.randint(0, 4))

    return gened_state


def permute_map_strategic(original_state: GameStateBase):
    gened_state: GameStateBase = copy.deepcopy(original_state)

    # Đổi màu cửa (giữ nguyên prefabPath và direction)
    for door in gened_state.doors:
        transferable_doors: List[Door] = [
            d
            for d in gened_state.doors
            if d is not door and d.prefabPath == door.prefabPath and d.direction == door.direction
        ]
        if transferable_doors:
            selected_door = random.choice(transferable_doors)
            swap_color(door, selected_door)
            swap_speciality(door, selected_door)

    # Đổi màu block (giữ nguyên prefabPath và kích thước)
    for block in gened_state.blocks:
        transferable_blocks: List[Block] = [
            b
            for b in gened_state.blocks
            if b is not block and b.prefabPath == block.prefabPath and have_same_size(b, block)
        ]
        if transferable_blocks:
            selected_block = random.choice(transferable_blocks)
            swap_color(block, selected_block)
            # swap_speciality(block, selected_block)

    strategic_shuffle(gened_state, 4000)
    add_blocks(gened_state, num_blocks=random.randint(0, 4))

    return gened_state


class GenMapMode(Enum):
    PERMUTE = "permute"
    RANDOM = "random"


def generate_map(
    gamestate_path: str, mode: GenMapMode = GenMapMode.PERMUTE, debug=False, num_blocks=30
):
    if mode == GenMapMode.PERMUTE:
        state = GameStateBase(gamestate_path)

        config = ExperimentConfig(randomizing_steps=500, max_shuffle=4)
        solver = PuzzleSolver(config)
        heuristic = ManhattanHeuristic()
        gened_state = None

        while True:
            gened_state = permute_map_strategic(state)
            solution, metrics = solver.check_solvable(gened_state, heuristic)
            if solution:
                break

        return gened_state
    elif mode == GenMapMode.RANDOM:
        return generate_map_random(gamestate_path, debug, num_blocks)


def generate_map_random(gamestate_path: str, debug=False, num_blocks=30):
    state = GameStateBase(gamestate_path)
    exitable_blocks = get_all_exitable_blocks(state)
    block_data = []

    for block in exitable_blocks:
        block_data.append(
            {
                "prefabPath": block[0],
                "position": {"x": block[2], "y": 0.85, "z": block[3]},
                "rotation": {"x": block[4], "y": block[5], "z": block[6], "w": block[7]},
                "color": block[1],  # hoặc dùng state.doors[0].color nếu có state
                "secondColor": 0,
                "special": 0,
                "moveType": 0,
                "turnCount": 0,
                "timeCount": 0,
                "hasKey": False,
            }
        )

    state.blocks = []
    state.blocks_occupied = set()

    for _ in range(num_blocks):
        random_shuffle(state, 1000, max_step=3)
        if debug:
            visualize_state(state, regime="show")
        add_blocks(
            state=state,
            unique_blocks=block_data,
            num_blocks=1,
            n_attempts=10000,
            is_solvable=True,
            is_random_color=False,
            randomize_steps=500,
            max_shuffle=5,
            debug_viz=debug,
        )
    return state
