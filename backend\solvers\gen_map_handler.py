import json
import os

import matplotlib.pyplot as plt

from backend.core.config import config
from backend.core.task_manager import task_manager
from src.game.visualization.visualize_game_state import visualize_state
from src.gen_map_service.api import GenMapMode

# ===== CÁC HÀM MỚI SỬ DỤNG TASK_MANAGER =====


def generate_map_worker(
    temp_path: str, task_id: str, mode: GenMapMode = GenMapMode.PERMUTE, debug=False, num_blocks=30
):
    """Worker function để chạy trong task_manager process"""
    try:
        print(f"🗺️ Starting gen map worker for task {task_id}")

        # Import API gen map có sẵn
        from src.gen_map_service.api import generate_map

        # Tạo map mới từ map gốc
        task_manager.update_progress(task_id, 20, "Đang sinh map mới...")

        # Tạo thư mục output
        os.makedirs(f"{config.TEMP_VIDEOS_DIR}/{task_id}", exist_ok=True)

        new_gamestate = generate_map(temp_path, mode, debug, num_blocks)

        if not new_gamestate:
            print(f"❌ Task {task_id} - <PERSON>h<PERSON>ng sinh được map")
            raise Exception("Không sinh được map variant")

        print(f"✅ Task {task_id} - Map đã được sinh thành công")

        # Tạo ảnh visualization cho map mới
        task_manager.update_progress(task_id, 70, "Đang tạo ảnh map...")

        # Tạo ảnh visualization
        fig, ax = plt.subplots(figsize=(12, 10))
        try:
            visualize_state(new_gamestate, ax=ax)
            ax.set_title("Generated Map", fontsize=16, fontweight="bold")

            img_path = f"{config.TEMP_VIDEOS_DIR}/{task_id}/generated_map.png"
            plt.savefig(img_path, dpi=200, bbox_inches="tight", facecolor="white", edgecolor="none")

            # Lưu JSON của map mới
            json_path = f"{config.TEMP_VIDEOS_DIR}/{task_id}/generated_map.json"

            # Sử dụng hàm to_json từ GameStateBase
            map_json = new_gamestate.to_json()
            with open(json_path, "w", encoding="utf-8") as f:
                json.dump(map_json, f, indent=2, ensure_ascii=False)

            print(f"✅ Task {task_id} - JSON đã được lưu: {json_path}")

            result = {
                "success": True,
                "task_id": task_id,
                "mode": "gen_map",
                "image_path": img_path,
                "json_path": json_path,
                "message": "Đã sinh map mới thành công",
                "game_info": {
                    "level": getattr(new_gamestate, "level", "Generated"),
                    "name": getattr(new_gamestate, "name", "Generated Map"),
                    "difficulty": getattr(new_gamestate, "difficulty", "Custom"),
                    "size_x": new_gamestate.size_x,
                    "size_y": new_gamestate.size_y,
                    "blocks_count": len(new_gamestate.blocks),
                    "doors_count": len(new_gamestate.doors)
                    if hasattr(new_gamestate, "doors")
                    else 0,
                },
            }

            print(f"✅ Task {task_id} hoàn thành gen map + visualization + JSON")
            task_manager.update_progress(task_id, 100, "Hoàn thành")
            return result

        except Exception as vis_error:
            print(f"❌ Visualization error: {vis_error}")
            # Vẫn complete task nhưng báo lỗi visualization
            result = {
                "success": True,
                "task_id": task_id,
                "mode": "gen_map",
                "message": f"Map sinh thành công nhưng không tạo được ảnh: {str(vis_error)}",
                "game_info": {
                    "size_x": getattr(new_gamestate, "size_x", "Unknown"),
                    "size_y": getattr(new_gamestate, "size_y", "Unknown"),
                    "blocks_count": len(getattr(new_gamestate, "blocks", [])),
                    "doors_count": len(getattr(new_gamestate, "doors", [])),
                },
                "visualization": {"success": False, "error": str(vis_error)},
            }
            task_manager.update_progress(task_id, 100, "Hoàn thành với cảnh báo visualization")
            return result

        finally:
            plt.close(fig)

        # Cleanup file gốc
        try:
            os.unlink(temp_path)
        except:  # noqa: E722
            pass

    except Exception as e:
        print(f"❌ Gen map error: {e}")
        task_manager.update_progress(task_id, 100, f"Lỗi: {str(e)}")

        # Cleanup
        try:
            os.unlink(temp_path)
        except:  # noqa: E722
            pass

        raise e


def generate_map_with_task_manager(temp_path: str, task_id: str):
    """Submit gen map task vào task_manager"""
    try:
        print(f"🗺️ Submitting gen map task {task_id} to task_manager")

        # Submit task vào task_manager
        submitted_task_id = task_manager.submit(
            generate_map_worker,
            temp_path,
            task_id,
            task_id_index=1,  # task_id là tham số thứ 2 (index 1)
        )

        print(f"✅ Task {task_id} đã được submit với ID: {submitted_task_id}")
        return submitted_task_id

    except Exception as e:
        print(f"❌ Error submitting task {task_id}: {e}")
        raise e


def get_gen_map_status(task_id: str):
    """Lấy trạng thái của gen map task"""
    return task_manager.get_status(task_id)


def get_gen_map_result(task_id: str):
    """Lấy kết quả của gen map task"""
    return task_manager.get_data(task_id)


def cancel_gen_map_task(task_id: str):
    """Hủy gen map task"""
    return task_manager.cancel(task_id)


def get_gen_map_progress(task_id: str):
    """Lấy tiến độ của gen map task"""
    return task_manager.get_progress_message(task_id)
