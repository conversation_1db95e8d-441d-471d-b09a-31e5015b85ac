from src.check_solvable_service.config import ExperimentConfig
from src.check_solvable_service.solver import Puz<PERSON><PERSON><PERSON><PERSON>
from src.game.game_state_base import GameStateBase


def check_solve(state_path: str, config: ExperimentConfig = None):
    if not config:
        config = ExperimentConfig()

    solver = PuzzleSolver(config)
    initial_state = GameStateBase(state_path)


    is_solved, metrics = solver.check_solvable(initial_state)
    return is_solved, metrics

def get_current_state(initial_state: GameStateBase, current_state_route: list):
    for t in current_state_route:
        if t[0] == "eat":
            initial_state.eat_block(initial_state.find_block(t[1]))
        else:
            initial_state.move_block(initial_state.find_block(t[0]), t[1], t[2])


def get_next_block_exit_path(
    state_path: str, current_state_route: list, config: ExperimentConfig = None
) -> tuple[int, list]:
    if not config:
        config = ExperimentConfig()

    state = GameStateBase(state_path)
    get_current_state(state, current_state_route)

    solver = PuzzleSolver(config)

    return solver.get_next_block_exit(state)
