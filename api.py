import logging
import os
import secrets

import dotenv
from fastapi import Depends, FastAP<PERSON>, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import H<PERSON>PBasic, HTTPBasicCredentials
import uvicorn

from backend.api.endpoints import cleanup_task, get_map_image, get_map_json, get_video, get_zip
from backend.api.handler_v2 import (
    cancel_task_v2,
    generate_map_async_v2,
    get_all_tasks_v2,
    get_task_progress_v2,
    get_task_result_v2,
    get_task_status_v2,
    predict_task,
    solve_level_async_v2,
)
from backend.core.task_manager import task_manager

security = HTTPBasic()
dotenv.load_dotenv()


def verify_basic_auth(credentials: HTTPBasicCredentials = Depends(security)):
    expected_username = os.getenv("BASIC_AUTH_USERNAME")
    expected_password = os.getenv("BASIC_AUTH_PASSWORD")

    if not expected_username or not expected_password:
        # <PERSON><PERSON>u chưa cấu hình biến mô<PERSON> tr<PERSON><PERSON>ng, kh<PERSON><PERSON> cho truy cập bất kỳ endpoint nào
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Unauthorized",
            headers={"WWW-Authenticate": "Basic"},
        )

    is_username_valid = secrets.compare_digest(credentials.username, expected_username)
    is_password_valid = secrets.compare_digest(credentials.password, expected_password)

    if not (is_username_valid and is_password_valid):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Unauthorized",
            headers={"WWW-Authenticate": "Basic"},
        )

    return True


app = FastAPI(dependencies=[Depends(verify_basic_auth)])

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# v2 endpoints
app.post("/v2/solve-async")(solve_level_async_v2)
app.get("/v2/status/{task_id}")(get_task_status_v2)
app.get("/v2/result/{task_id}")(get_task_result_v2)
app.delete("/v2/cancel/{task_id}")(cancel_task_v2)
app.get("/v2/tasks")(get_all_tasks_v2)
app.get("/v2/progress/{task_id}")(get_task_progress_v2)
app.post("/v2/gen-map-async")(generate_map_async_v2)

app.post("v2/prediction")(predict_task)

# File service routes
app.get("/video/{task_id}")(get_video)
app.get("/zip/{task_id}")(get_zip)
app.get("/map-image/{task_id}")(get_map_image)
app.get("/map-json/{task_id}")(get_map_json)
app.delete("/cleanup/{task_id}")(cleanup_task)


@app.exception_handler(Exception)
async def app_exception_handler(request, exc):
    logging.error("Application error: %s", exc)
    return JSONResponse(status_code=500, content={"error": "Internal server error"})


@app.on_event("shutdown")
async def shutdown_event():
    """Graceful shutdown"""
    print("🛑 API shutting down...")
    task_manager.shutdown()


@app.on_event("startup")
async def startup_event():
    """Start background cleanup task"""
    print("🚀 API started with Queue Management")


if __name__ == "__main__":
    import multiprocessing

    PORT = int(os.getenv("BACKEND_PORT", 8000))
    print(f"🚀 API started with port {PORT}")

    multiprocessing.freeze_support()
    uvicorn.run(app, host="0.0.0.0", port=PORT)
