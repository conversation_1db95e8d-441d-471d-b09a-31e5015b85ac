from collections import deque
import json
import os
import struct
from typing import List, Optional, Set, Tuple

from matplotlib.patches import Rectangle
import matplotlib.pyplot as plt
from numba import njit
import numpy as np

from .const import COLORS, DoorDirection, Special_Block, Special_Door
from .objects import Block, Door, Wall
from .special_tile import SpecialTile


@njit
def check_position_validity(new_x: int, new_y: int, size_x: int, size_y: int) -> bool:
    """Numba optimized function to check if position is within bounds"""
    return not (-1 >= new_x or new_x >= size_x or -1 >= new_y or new_y >= size_y)


@njit
def calculate_min_max_coordinates(sub_blocks_array: np.ndarray) -> tuple:
    """Numba optimized function to calculate min/max coordinates"""
    min_x = np.min(sub_blocks_array[:, 0])
    min_y = np.min(sub_blocks_array[:, 1]) 
    max_x = np.max(sub_blocks_array[:, 0])
    max_y = np.max(sub_blocks_array[:, 1])
    return min_x, min_y, max_x, max_y


class GameState:
    def __init__(self, path: str) -> None:
        self.blocks: List[Block] = []
        self.walls: List[Wall] = []
        self.doors: List[Door] = []
        self.special_tiles: List[SpecialTile] = []
        self.ice_blocks: Set[int] = set()
        self.key_blocks: Set[int] = set()
        self.ice_doors: Set[int] = set()
        self.shutter_doors: Set[int] = set()

        # Tập hợp các ô đã chiếm
        self.blocks_occupied = set()
        self.walls_occupied = set()
        self.doors_occupied = set()

        """Khởi tạo GameState từ dữ liệu JSON"""
        data = json.load(open(path, "r"))
        if data is None:
            raise ValueError("Invalid game state data")
        self.level = data["level"]
        self.difficulty = data["difficulty"]
        self.name = self.level
        self.time = data["time"]

        tilelist_data = data.get("specialTileList", [])
        # Tối ưu và enumerate
        self.special_tiles = [
            SpecialTile(i, tile["specialTile"], tile["gridPos"], tile["color"])
            for i, tile in enumerate(tilelist_data)
        ]

        # Là typle (x, y)
        self.size_x = data["size"]["x"]
        self.size_y = data["size"]["y"]

        # Load Block objects và enumerate
        for i, block_data in enumerate(data["blockList"]):
            temp_block = Block(block_data, i)
            if temp_block.special == Special_Block.ICE.value:
                self.ice_blocks.add(temp_block.id)
            if temp_block.hasKey:
                self.key_blocks.add(temp_block.id)
            self.blocks.append(temp_block)

        # Load Wall objects
        self.walls = [Wall(wall_data) for wall_data in data["wallList"]]

        # Load Door objects và enumerate  
        for i, door_data in enumerate(data["doorList"]):
            temp_door = Door(door_data, i)
            self.doors.append(temp_door)
            if temp_door.special == Special_Door.ICE.value:
                self.ice_doors.add(temp_door.id)
            elif temp_door.special == Special_Door.SHUTTER.value:
                self.shutter_doors.add(temp_door.id)

        # Tạo tập hợp các ô đã chiếm
        for block in self.blocks:
            self.blocks_occupied.update(block.subBlocks)
        for wall in self.walls:
            self.walls_occupied.update(wall.subBlocks)
        for door in self.doors:
            self.doors_occupied.update(door.subBlocks)

        self.level_path = os.path.join("Output-random", f"level_{self.level}")

    def remove_occupied(self, block: Block) -> bool:
        if block.subBlocks:
            self.blocks_occupied.difference_update(block.subBlocks)
            return True
        return False

    def add_occupied(self, block: Block) -> bool:
        if block.subBlocks:
            self.blocks_occupied.update(block.subBlocks)
            return True
        return False

    def is_accessible_door(self, block: Block, door: Door) -> bool:
        """Check if a door is accessible for a specific block"""
        if door.color != block.color:
            return False

        door_is_accessible = False

        # Handle Heart blocks
        if block.special == Special_Block.HEART.value:
            if door.special == Special_Door.HEART.value:
                door_is_accessible = True

        # Handle Ice doors
        elif door.special == Special_Door.ICE.value:
            if door.turnCount <= 0:
                door_is_accessible = True

        # Handle Shutter doors
        elif door.special == Special_Door.SHUTTER.value:
            if door.open:
                door_is_accessible = True
        else:
            door_is_accessible = True

        min_x = min(block.subBlocks, key=lambda cell: cell[0])[0]
        min_y = min(block.subBlocks, key=lambda cell: cell[1])[1]
        max_x = max(block.subBlocks, key=lambda cell: cell[0])[0]
        max_y = max(block.subBlocks, key=lambda cell: cell[1])[1]
        
        if block.moveType != 0:
            min_door_x = min(door.subBlocks, key=lambda cell: cell[0])[0]
            min_door_y = min(door.subBlocks, key=lambda cell: cell[1])[1]
            max_door_x = max(door.subBlocks, key=lambda cell: cell[0])[0]
            max_door_y = max(door.subBlocks, key=lambda cell: cell[1])[1]
            
            if door.direction == DoorDirection.VERTICAL:
                if not (min_door_y <= min_y and max_y <= max_door_y):
                    door_is_accessible = False
            else:
                if not (min_door_x <= min_x and max_x <= max_door_x):
                    door_is_accessible = False

        if door.direction == DoorDirection.VERTICAL:
            if max_y - min_y + 1 > len(door.subBlocks):
                door_is_accessible = False
        else:
            if max_x - min_x + 1 > len(door.subBlocks):
                door_is_accessible = False

        return door_is_accessible

    def get_accessible_doors(self, block: Block) -> List[Door]:
        """Lấy danh sách các cửa có thể truy cập cho block"""
        return [door for door in self.doors if self.is_accessible_door(block, door)]

    def can_move_block(self, block: Block) -> bool:
        """Kiểm tra xem block có thể di chuyển hay không"""
        if block.special == Special_Block.ICE.value and block.turnCount > 0:
            return False
        if block.special == Special_Block.LOCK.value and len(self.key_blocks) > 0:
            return False
        return True

    def can_cook(self, block: Block) -> bool:
        if self.can_move_block(block) is False:
            return False
        occupied = set(self.doors_occupied)
        occupied.update(self.blocks_occupied)
        occupied.update(self.walls_occupied)

        # Tối ưu
        special_tiles_occupied = {
            (special_tile.x, special_tile.y) 
            for special_tile in self.special_tiles 
            if special_tile.special == 2 and special_tile.color != block.color
        }
        occupied.update(special_tiles_occupied)

        # Tối ưu cho accessible doors
        accessible_doors = self.get_accessible_doors(block)
        for door in accessible_doors:
            occupied.difference_update(door.subBlocks)

        # Loại bỏ các ô hiện tại của block
        occupied.difference_update(block.subBlocks)

        # Sử dụng numpy array để tối ưu tính toán min/max
        block_positions = np.array(list(block.subBlocks))
        min_x, min_y, max_x, max_y = calculate_min_max_coordinates(block_positions)

        eatable = False
        for door in accessible_doors:
            door_positions = np.array(list(door.subBlocks))
            min_door_x, min_door_y, max_door_x, max_door_y = calculate_min_max_coordinates(door_positions)

            skip = False
            if door.direction == DoorDirection.VERTICAL:
                # Cua nam doc
                if not (min_door_y <= min_y and max_y <= max_door_y):
                    skip = True
                assert min_door_x == max_door_x
                # Tối ưu với any() và generator expression
                skip = skip or any(
                    (i, subBlock[1]) in occupied
                    for subBlock in block.subBlocks
                    for i in range(min(subBlock[0], min_door_x) + 1, max(subBlock[0], min_door_x))
                )
            else:
                # Cua nam ngang
                if not (min_door_x <= min_x and max_x <= max_door_x):
                    skip = True
                assert min_door_y == max_door_y
                # Tối ưu với any() và generator expression
                skip = skip or any(
                    (subBlock[0], i) in occupied
                    for subBlock in block.subBlocks
                    for i in range(min(subBlock[1], min_door_y) + 1, max(subBlock[1], min_door_y))
                )

            if not skip:
                eatable = True
                break

        return eatable

    def can_exit(self, block: Block) -> bool:
        if block.special == Special_Block.ICE.value and block.turnCount > 0:
            return False
        occupied = set(self.doors_occupied)
        occupied.update(self.blocks_occupied)
        occupied.update(self.walls_occupied)
        
        # Loại bỏ các ô hiện tại của block
        for cell in block.subBlocks:
            if cell in occupied:
                occupied.remove(cell)

        for special_tile in self.special_tiles:
            if (special_tile.special == 2 and special_tile.color != block.color):
                if (special_tile.x, special_tile.y) not in occupied:
                    occupied.add((special_tile.x, special_tile.y))

        if self.can_cook(self.find_block(block.id)):
            return True

        # Thử từng hướng di chuyển
        visited = set((0, 0))
        stack = [(0, 0)]
        while stack:
            (ndx, ndy) = stack.pop()
            for (dx, dy) in block.directions.values():
                if (ndx + dx, ndy + dy) in visited:
                    continue
                
                moved_cells = [(x + ndx + dx, y + ndy + dy) for (x, y) in block.subBlocks]

                if any(cell in occupied for cell in moved_cells):
                    continue

                self.move_block(block, ndx + dx, ndy + dy)
                if self.can_cook(block):
                    self.move_block(block, -ndx - dx, -ndy -dy)
                    return True
                
                self.move_block(block, -ndx - dx, -ndy -dy)

                stack.append((ndx + dx, ndy + dy))
                visited.add((ndx + dx, ndy + dy))
        return False

    def is_valid_move(self, block: Block, dx: int, dy: int) -> bool:
        # Tối ưu và any()
        sub_blocks = block.get_subBlocks()
        new_positions = [(sub[0] + dx, sub[1] + dy) for sub in sub_blocks]
        
        # Kiểm tra bounds với numba
        if not all(check_position_validity(new_x, new_y, self.size_x, self.size_y) 
                  for new_x, new_y in new_positions):
            return False

        # Kiểm tra va chạm với special tiles
        special_positions = {(tile.x, tile.y) for tile in self.special_tiles if tile.color != block.color}
        if any(pos in special_positions for pos in new_positions):
            return False
            
        # Kiểm tra va chạm với occupied positions
        all_occupied = self.blocks_occupied | self.walls_occupied | self.doors_occupied
        return not any(pos in all_occupied for pos in new_positions)

    def is_win(self) -> bool:
        return len(self.blocks) == 0

    def get_valid_moves(self, block: Block, max_step: int = 1) -> List[Tuple[int, int]]:
        """Lấy danh sách các hướng di chuyển hợp lệ cho block"""
        if block.special == Special_Block.ICE.value and block.turnCount > 0:
            return []
        if block.special == Special_Block.LOCK.value and len(self.key_blocks) > 0:
            return []

        max_step = min(max_step, (self.size_x + self.size_y + 2)) 
        valid_moves = []

        self.remove_occupied(block)
        visited = set()
        dq = deque([(0, 0, 0)])
        visited.add((0, 0))

        while dq:
            dx, dy, steps = dq.popleft()
            if dx != 0 or dy != 0:
                valid_moves.append((dx, dy))

            if steps >= max_step:
                continue

            next_moves = [
                (dx + dir_dx, dy + dir_dy) 
                for dir_dx, dir_dy in block.directions.values()
            ]
            
            for new_dx, new_dy in next_moves:
                if self.is_valid_move(block, new_dx, new_dy):
                    new_pos = (new_dx, new_dy)
                    if new_pos not in visited:
                        visited.add(new_pos)
                        dq.append((new_dx, new_dy, steps + 1))

        self.add_occupied(block)
        return valid_moves

    def find_block(self, id: int) -> Optional[Block]:
        return next((block for block in self.blocks if block.id == id), None)

    def find_door(self, id: int) -> Optional[Door]:
        return next((door for door in self.doors if door.id == id), None)

    def move_block(self, block: Block, dx: int, dy: int, time: int = 1) -> bool:
        """Di chuyển block"""
        for i in range(time):
            self.remove_occupied(block)
            block.move(dx, dy)
            self.add_occupied(block)
        return True

    def can_eat(self) -> List[Block]:
        result = []
        for block in self.blocks:
            has_matching_door = False
            for door in self.doors:
                if self.is_accessible_door(block, door):
                    has_matching_door = True
                    break
            if has_matching_door and self.can_exit(block):
                result.append(block)
        return result

    def eat_block(self, block: Block) -> bool:
        """Xoá block khỏi game state"""
        if block.special == Special_Block.LAYER.value:
            block.special = Special_Block.NORMAL.value
            block.color = block.secondColor
        else:
            self.remove_occupied(block)
            # Cập nhật các key blocks
            self.key_blocks.difference_update([b_id for b_id in self.key_blocks if block.id == b_id])

            ice_blocks_to_remove = []
            for b_id in self.ice_blocks:
                matching_blocks = [b for b in self.blocks if b.id == b_id]
                for b in matching_blocks:
                    b.turnCount -= 1
                    if b.turnCount <= 0:
                        ice_blocks_to_remove.append(b_id)
            self.ice_blocks.difference_update(ice_blocks_to_remove)
            
            self.blocks.remove(block)

        # Cập nhật các ice doors
        ice_doors_to_remove = []
        for d_id in self.ice_doors:
            matching_doors = [d for d in self.doors if d.id == d_id]
            for d in matching_doors:
                d.turnCount -= 1
                if d.turnCount <= 0:
                    ice_doors_to_remove.append(d_id)
        self.ice_doors.difference_update(ice_doors_to_remove)

        # Cập nhật các shutter doors
        for d_id in self.shutter_doors:
            for d in (d for d in self.doors if d.id == d_id):
                d.open = not d.open
        return True

    def visualize_state(
        self, saved_image=False, save_path: str = "Output", index_image=0, block_id = -1, ax = None
    ) -> None:
        """Visualize the current game state using matplotlib."""
        # ax.clear()
        if not ax:
            fig, ax = plt.subplots(figsize=(8, 6))

        # Draw blocks
        for block in self.blocks:
            edge_color = "black" if block.id != block_id else "red"
            for x, y in block.subBlocks:
                rect = None
                if block.special == Special_Block.VECTOR.value:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        1,
                        1,
                        facecolor=COLORS[block.color],
                        edgecolor=edge_color,
                        hatch="|" if block.moveType == 2 else "-",
                        linewidth=0.5 if block.id != block_id else 2,
                    )
                elif block.special == Special_Block.HEART.value:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        1,
                        1,
                        facecolor=COLORS[block.color],
                        edgecolor=edge_color,
                        hatch="*",
                        linewidth=0.5 if block.id != block_id else 2,
                    )
                elif block.special == Special_Block.ICE.value:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        1,
                        1,
                        facecolor=COLORS[block.color],
                        edgecolor=edge_color,
                        hatch="X",
                        linewidth=0.5 if block.id != block_id else 2,
                    )
                    # Hiển thị số turnCount ở giữa block
                    ax.text(
                        x,
                        y,
                        str(block.turnCount),
                        color="black",
                        fontsize=12,
                        ha="center",
                        va="center",
                        fontweight="bold",
                    )
                elif block.special == Special_Block.LOCK.value:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        1,
                        1,
                        facecolor=COLORS[block.color],
                        edgecolor=edge_color,
                        linewidth=0.5 if block.id != block_id else 2,
                    )
                    # Hiển thị icon khóa
                    ax.text(x, y, "lock", fontsize=14, ha="center", va="center")
                elif block.special == Special_Block.LAYER.value:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        0.8,
                        0.8,
                        facecolor=COLORS[block.secondColor],
                        edgecolor=COLORS[block.color],
                        linewidth=7,
                    )
                elif block.special == Special_Block.BOMB.value:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        1,
                        1,
                        facecolor=COLORS[block.color],
                        edgecolor=edge_color,
                        linewidth=0.5 if block.id != block_id else 2,
                    )
                    ax.text(
                        x,
                        y,
                        str(block.turnCount),
                        color="black",
                        fontsize=12,
                        ha="center",
                        va="center",
                        fontweight="bold",
                    )
                elif block.special == Special_Block.NORMAL.value:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        1,
                        1,
                        facecolor=COLORS[block.color],
                        edgecolor=edge_color,
                        linewidth=0.5 if block.id != block_id else 2,
                    )
                    if block.hasKey:
                        # Hiển thị icon chìa khóa
                        ax.text(x, y, "key", fontsize=14, ha="center", va="center")

                ax.text(
                    x - 0.48,
                    y + 0.48,
                    str(block.id),
                    color="black",
                    fontsize=8,
                    ha="left",
                    va="top",
                    fontweight="bold",
                )

                ax.add_patch(rect)

            # Draw walls
            for wall in self.walls:
                for x, y in wall.subBlocks:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        1,
                        1,
                        facecolor=COLORS[wall.color],
                        edgecolor="black",
                        linewidth=0.5
                    )
                    ax.add_patch(rect)

        # Draw doors
        for door in self.doors:
            for x, y in door.subBlocks:
                symbol = "|" if door.direction == DoorDirection.VERTICAL else "-"
                if door.special == Special_Door.HEART.value:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        1,
                        1,
                        facecolor=COLORS[door.color],
                        edgecolor="black",
                        hatch="*" + symbol,
                        linewidth=0.5 if door.id != block_id else 2,
                    )
                elif door.special == Special_Door.ICE.value:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        1,
                        1,
                        facecolor=COLORS[door.color],
                        edgecolor="black",
                        hatch="X" + symbol,
                        linewidth=0.5 if door.id != block_id else 2,
                    )
                    # Hiển thị số turnCount ở giữa block
                    ax.text(
                        x,
                        y,
                        str(door.turnCount),
                        color="black",
                        fontsize=12,
                        ha="center",
                        va="center",
                        fontweight="bold",
                    )
                elif door.special == Special_Door.SHUTTER.value:
                    if door.open:
                        rect = Rectangle(
                            (x - 0.5, y - 0.5),
                            1,
                            1,
                            facecolor=COLORS[door.color],
                            edgecolor="black",
                            hatch="//" + symbol,
                        )
                    else:
                        rect = Rectangle(
                            (x - 0.5, y - 0.5),
                            1,
                            1,
                            facecolor=COLORS[door.color],
                            edgecolor="black",
                            hatch="X" + symbol,
                            alpha=0.5,
                        )
                else:
                    rect = Rectangle(
                        (x - 0.5, y - 0.5),
                        1,
                        1,
                        facecolor=COLORS[door.color],
                        edgecolor="black",
                        hatch="//" + symbol,
                    )
                ax.add_patch(rect)

        # Visualize special tiles
        for tile in self.special_tiles:
            rect = Rectangle(
                (tile.x - 0.5, tile.y - 0.5),
                1,
                1,
                color=COLORS[tile.color],
                edgecolor="black",
                linewidth=0.5,
                alpha=0.3
            )
            ax.add_patch(rect)

            # Configure plot
        ax.set_xlim(-2, self.size_x + 1)
        ax.set_ylim(-2, self.size_y + 1)
        ax.set_aspect('equal')
        ax.grid(True, alpha = 0.3)
        plt.title(f"{self.name} (Level {self.level})")
        if saved_image:
            os.makedirs(self.level_path, exist_ok=True)
            plt.savefig(f"{self.level_path}/{index_image}.jpg")
            plt.close()
        else:
            # plt.show()
            # st.pyplot(plt)
            plt.draw()

    def visualize_occupied(self) -> None:
        """Visualize the occupied cells in the game state."""
        fig, ax = plt.subplots(figsize=(8, 6))

        # Draw occupied blocks
        occupied_types = [
            (self.blocks_occupied, "lightgray", "Blocks"),
            (self.walls_occupied, "darkgray", "Walls"), 
            (self.doors_occupied, "lightblue", "Doors")
        ]
        
        for occupied_set, color, label in occupied_types:
            for x, y in occupied_set:
                hatch = "/" if label == "Doors" else None
                rect = Rectangle(
                    (x - 0.5, y - 0.5), 1, 1,
                    facecolor=color, edgecolor="black", hatch=hatch
                )
                ax.add_patch(rect)

        # Configure plot
        ax.set_xlim(-2, self.size_x + 1)
        ax.set_ylim(-2, self.size_y + 1)
        ax.set_aspect("equal")
        ax.grid(True, alpha=0.3)
        plt.title("Occupied Cells")
        plt.show()

    def copy(self) -> 'GameState':
        # Tạo instance mới mà không gọi __init__
        new_state = GameState.__new__(GameState)
        
        # Copy các thuộc tính cơ bản
        new_state.level = self.level
        new_state.difficulty = self.difficulty
        new_state.name = self.name
        new_state.time = self.time
        new_state.size_x = self.size_x
        new_state.size_y = self.size_y
        new_state.level_path = self.level_path
        
        new_state.special_tiles = [tile.copy() for tile in self.special_tiles]

        new_state.blocks = [block.copy() for block in self.blocks]
        new_state.walls = [wall.copy() for wall in self.walls]
        new_state.doors = [door.copy() for door in self.doors]
        
        # Copy các tập hợp theo dõi block/door đặc biệt
        new_state.ice_blocks = self.ice_blocks.copy()
        new_state.key_blocks = self.key_blocks.copy()
        new_state.ice_doors = self.ice_doors.copy()
        new_state.shutter_doors = self.shutter_doors.copy()
        
        # Tái tạo các tập hợp occupied từ các object đã copy
        new_state.blocks_occupied = set()
        new_state.walls_occupied = set()
        new_state.doors_occupied = set()
        
        for block in new_state.blocks:
            new_state.blocks_occupied.update(block.subBlocks)
        for wall in new_state.walls:
            new_state.walls_occupied.update(wall.subBlocks)
        for door in new_state.doors:
            new_state.doors_occupied.update(door.subBlocks)
        
        return new_state

    def to_hashable(self) -> int:
        buf = bytearray()

        # Encode blocks
        for b in self.blocks:
            buf.extend(struct.pack("<iiiiibi", 
                                b.id, b.color, b.secondColor, b.special, b.turnCount, int(b.hasKey), b.moveType))
            for x, y in b.subBlocks:
                buf.extend(struct.pack("<hh", x, y))

        return hash(bytes(buf))