from dataclasses import dataclass, field
from typing import Any, Dict, List


@dataclass
class ExperimentConfig:
    randomizing_steps: int = 7000
    max_shuffle: int = 4
    max_trials: int = 3

    stagnant_limit: int = 1500
    state_repeat_limit: int = 100
    min_block_for_stagnant: int = 4

    # Logging
    log_level: str = "DEBUG"
    debug_viz: bool = False


@dataclass 
class SolverMetrics:
    path: List = field(default_factory=list)
    shuffle_count: int = 0
    shuffle_moves: int = 0
    iteration: int = 0
    error_block_id: int = -1
    
    def to_dict(self) -> Dict[str, Any]:
        return {k: v for k, v in self.__dict__.items()}