import numpy as np
from sklearn.base import clone
from sklearn.linear_model import <PERSON>
from sklearn.model_selection import KFold


class EnsembleRegressor:
    def __init__(self, models, mode='mean', weights=None):
        self.models = models
        self.mode = mode
        self.weights = weights
        if self.mode not in ('mean', 'median'):
            raise ValueError("mode must be 'mean' or 'median'")

    def predict(self, X):
        preds = np.column_stack([m.predict(X) for m in self.models])
        if self.mode == 'mean':
            if self.weights is not None:
                w = np.asarray(self.weights, dtype=float)
                w = w / w.sum()
                return np.average(preds, axis=1, weights=w)
            return preds.mean(axis=1)
        else:
            return np.median(preds, axis=1)


class StackingRegressor:
    def __init__(self, models, n_splits=5, meta_model=None, shuffle=True, random_state=42):
        self.base_models = models
        self.n_splits = n_splits
        self.meta_model = meta_model if meta_model is not None else Ridge(alpha=1.0)
        self.shuffle = shuffle
        self.random_state = random_state

    def _as_2d_y(self, y):
        y_arr = np.asarray(y)
        if y_arr.ndim == 1:
            y_arr = y_arr.reshape(-1, 1)
        return y_arr

    def fit(self, X, y):
        X_arr = np.asarray(X)
        y_arr = self._as_2d_y(y)
        n_samples = X_arr.shape[0]
        n_targets = y_arr.shape[1]
        n_models = len(self.base_models)

        meta_features = np.zeros((n_samples, n_models * n_targets), dtype=float)

        kf = KFold(n_splits=self.n_splits, shuffle=self.shuffle, random_state=self.random_state)

        self.base_models_oof_ = [clone(m) for m in self.base_models]

        for m_idx, base in enumerate(self.base_models_oof_):
            oof_pred = np.zeros((n_samples, n_targets), dtype=float)

            for tr_idx, val_idx in kf.split(X_arr):
                X_tr, X_val = X_arr[tr_idx], X_arr[val_idx]
                y_tr = y_arr[tr_idx]

                model_fold = clone(base)
                model_fold.fit(X_tr, y_tr if y_tr.shape[1] > 1 else y_tr.ravel())
                pred_val = model_fold.predict(X_val)

                pred_val = np.asarray(pred_val)
                if pred_val.ndim == 1:
                    pred_val = pred_val.reshape(-1, 1)

                oof_pred[val_idx, :] = pred_val

            start = m_idx * n_targets
            end = start + n_targets
            meta_features[:, start:end] = oof_pred

        self.meta_model_ = clone(self.meta_model).fit(meta_features, y_arr if n_targets > 1 else y_arr.ravel())

        self.base_models_ = [clone(m).fit(X_arr, y_arr if y_arr.shape[1] > 1 else y_arr.ravel())
                             for m in self.base_models]
        self.n_targets_ = n_targets
        return self

    def _stack_features(self, X):
        X_arr = np.asarray(X)
        preds = []
        for m in self.base_models_:
            p = m.predict(X_arr)
            p = np.asarray(p)
            if p.ndim == 1:
                p = p.reshape(-1, 1)
            preds.append(p)
        return np.column_stack(preds)

    def predict(self, X):
        meta_features = self._stack_features(X)
        pred = self.meta_model_.predict(meta_features)
        return pred
