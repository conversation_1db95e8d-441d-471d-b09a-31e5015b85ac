import itertools
from queue import PriorityQueue
from typing import Optional

from ..game.game_state_medium_path import GameState
from ..heuristic.clean_cost_heuristic import CleanCostHeuristic

# from .utils import MediumPathSolverUtils
from .config import ShortestPathSolverConfig


class ShortestPathSolver:
    def __init__(self, config: ShortestPathSolverConfig):
        self.config = config
    @staticmethod
    def lookahead_expand_unique_block_base(nstate: GameState, path, visited, id, heuristic: CleanCostHeuristic):
        """Trả về list (score, path, state) của các leaf sau lookahead DFS"""
        if nstate.is_win():
            return [(0, 0, path)]

        state = nstate.copy()
        results = []
        can_eat_blocks = state.can_eat()
        if can_eat_blocks:
            for block in can_eat_blocks:
                state.eat_block(state.find_block(block.id))
                path = path + [('eat', block.id)]
            
            return [(len(state.blocks), 0, path)]
        
        new_path = path[:]
        for i in range(len(state.blocks)):
            for dx, dy in state.get_valid_moves(state.blocks[i], max_step=10):
                state.move_block(state.blocks[i], dx, dy)
                new_path = path + [(state.blocks[i].id, dx, dy)]

                can_eat_blocks = state.can_eat()
                if can_eat_blocks:
                    for block in can_eat_blocks:
                        state.eat_block(block)
                        new_path = new_path + [('eat', block.id)]
                    return [(len(state.blocks), 0, new_path)]
                
                for j in range(len(state.blocks)):
                    if i == j:
                        continue
                    
                    for dx2, dy2 in state.get_valid_moves(state.blocks[j], max_step=10):
                        if state.move_block(state.blocks[j], dx2, dy2):
                            sig = state.to_hashable()
                            if (id, sig) not in visited:
                                new_path = new_path + [(state.blocks[j].id, dx2, dy2)]
                                append_path = new_path[:]
                                results.append((len(state.blocks), heuristic.calculate(state, id), append_path))
                                new_path.pop()

                            state.move_block(state.blocks[j], -dx2, -dy2)
                
                new_path.pop()
                state.move_block(state.blocks[i], -dx, -dy)
                                
        return results
    def solve_custom_base_on_unique_block(self, state: GameState, path = [], 
                                        block_id = -1, iteration_init = 0, counter = itertools.count(), visited = set(),
                                        check_every = 1, patience = 10, max_iter = 100000, heuristic: Optional[CleanCostHeuristic] = None):
        pq = PriorityQueue()
        iteration = iteration_init

        best_remaining_blocks = len(state.blocks)
        num_iter = 0

        if block_id == -1:
            current_state = state.copy()
            while True:
                list_block = current_state.blocks
                list_block.sort(key=lambda x: heuristic.calculate(current_state, x.id))
                for block in list_block:
                    if current_state.find_block(block.id) is None:
                        continue

                    if not current_state.can_move_block(current_state.find_block(block.id)):
                        continue

                    # if heuristic.calculate(current_state, block.id) > 100000:
                    #     continue
                    
                    # if not current_state.can_move_block(current_state.find_block(block.id)) or len(current_state.get_accessible_doors(current_state.find_block(block.id))) < 1:
                    #     continue

                    path, current_state, visited, iteration = self.solve_custom_base_on_unique_block(current_state, path=path, 
                                                            block_id=block.id, iteration_init=iteration, visited=visited,
                                                            check_every=check_every, patience=patience, max_iter=max_iter, heuristic=heuristic)

                    if path is None:
                        # current_state.visualize_state()
                        return None, current_state, visited, iteration

                    can_eat_blocks = current_state.can_eat()
                    if can_eat_blocks:
                        for bl in can_eat_blocks:
                            current_state.eat_block(bl)
                            path = path + [('eat', bl.id)]
                            print(f"🍴 Eating block id: {bl.id}, Remaining blocks: {len(current_state.blocks)}")
                        visited.clear()
                        heuristic.restart(current_state)

                    if current_state.is_win():
                        return path, current_state, visited, iteration

                if current_state.is_win():
                    return path, current_state, visited, iteration
                
                if iteration >= max_iter:
                    current_state.visualize_state()
                    return None, current_state, visited, iteration

        else:
            pq.put((len(state.blocks), 0, block_id, next(counter), state, path))

        visited.add((block_id, state.to_hashable()))

        last_num_block = 0
        last_score = 0
        while not pq.empty():
            current_state: GameState
            num_block, score, block_id, _, current_state, path = pq.get()
            iteration += 1

            # Check win
            if current_state.is_win():
                visited.clear()
                return path, current_state, visited, iteration

            # Cập nhật best_remaining_blocks
            if num_block < best_remaining_blocks:
                best_remaining_blocks = num_block

            # Nếu số block đang chưa bằng tối ưu thì bỏ qua
            if num_block > best_remaining_blocks:
                continue

            if last_num_block == num_block and last_score == score:
                num_iter += 1
            else:
                num_iter = 0 
                last_num_block = num_block
                last_score = score

            if num_iter > patience:
                return path, current_state, visited, iteration
            
            if iteration >= max_iter:
                return [], current_state, visited, iteration

            # Lookahead
            results = self.lookahead_expand_unique_block_base(current_state, path, visited, block_id, heuristic)
            results.sort(key=lambda x: (x[0], x[1]))

            # Logging
            if iteration % check_every == 0:
                print(f"Iter {iteration}, Blocks: {num_block}, Heuristic: {score}, Block ID: {block_id} Len : {len(results)}")

            for num_b, score_leaf, new_path in results:
                
                eat = False
                for i in range(len(path), len(new_path)):
                    if new_path[i][0] == 'eat':
                        eat = True
                        current_state.eat_block(current_state.find_block(new_path[i][1]))
                        print(f"🍴 Eating block id: {new_path[i][1]}, Remaining blocks: {len(current_state.blocks)}")
                        heuristic.restart(current_state)
                        continue
                    id = new_path[i][0]
                    dx = new_path[i][1]
                    dy = new_path[i][2]
                    current_state.move_block(current_state.find_block(id), dx, dy)

                can_eat = current_state.can_eat()
                if can_eat:
                    for bl in can_eat:
                        current_state.eat_block(bl)
                        new_path = new_path + [('eat', bl.id)]
                        eat = True
                        print(f"🍴 Eating block id: {bl.id}, Remaining blocks: {len(current_state.blocks)}")
                    heuristic.restart(current_state)

                if eat:
                    del visited
                    return self.solve_custom_base_on_unique_block(current_state, block_id=-1,
                                                            iteration_init=iteration, path=new_path,
                                                            check_every=check_every, patience=patience, max_iter=max_iter, heuristic=heuristic)

                sig = current_state.to_hashable()
                if (block_id, sig) not in visited:
                    visited.add((block_id, sig))

                    pq.put((len(current_state.blocks), score_leaf, block_id, next(counter), current_state.copy(), new_path))

                for i in range(len(path), len(new_path)):
                    j = len(new_path) - (i - len(path)) - 1
                    id = new_path[j][0]
                    dx = new_path[j][1]
                    dy = new_path[j][2]
                    current_state.move_block(current_state.find_block(id), -dx, -dy)

        return path, current_state, visited, iteration