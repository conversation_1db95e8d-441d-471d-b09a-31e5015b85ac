import logging

import streamlit as st


class UserEmailFilter(logging.Filter):
    """B<PERSON> lọc tự động thêm user_email vào mỗi bản ghi log."""

    def filter(self, record):
        # Lấy email của người dùng từ đối tượng st.user
        user_email = st.user.email if st.user and st.user.get("email") is not None else "anonymous"

        # Thêm thuộc tính `user_email` vào bản ghi log
        record.user_email = user_email
        return True


logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
handler = logging.StreamHandler()
handler.setLevel(logging.INFO)
formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s - %(user_email)s")
handler.setFormatter(formatter)
handler.addFilter(UserEmailFilter())

# Xóa tất cả handler cũ và thêm handler mới
logger.handlers.clear()
logger.addHandler(handler)
