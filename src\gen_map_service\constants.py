PREFABS_CORNOR_WALL = "Prefabs/Wall/WallCorner"

UNIQUE_BASE_BLOCKS = [
  {
    "prefabPath": "Prefabs/Block/1x1",
    "position": {
      "x": 0.0,
      "y": 0.85,
      "z": 0.0
    },
    "rotation": {
      "x": -0.70711,
      "y": 0.0,
      "z": 0.0,
      "w": -0.70711
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  },
  {
    "prefabPath": "Prefabs/Block/1x2",
    "position": {
      "x": 0.0,
      "y": 0.85,
      "z": 0.5
    },
    "rotation": {
      "x": -0.70711,
      "y": 0.0,
      "z": 0.0,
      "w": -0.70711
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  },
  {
    "prefabPath": "Prefabs/Block/1x2",
    "position": {
      "x": 0.5,
      "y": 0.85,
      "z": 0.0
    },
    "rotation": {
      "x": -0.5,
      "y": -0.5,
      "z": 0.5,
      "w": -0.5
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  },
  {
    "prefabPath": "Prefabs/Block/1x3",
    "position": {
      "x": 0.0,
      "y": 0.85,
      "z": 0.0
    },
    "rotation": {
      "x": -0.70711,
      "y": 0.0,
      "z": 0.0,
      "w": -0.70711
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  },
  {
    "prefabPath": "Prefabs/Block/1x3",
    "position": {
      "x": 0.0,
      "y": 0.85,
      "z": 0.0
    },
    "rotation": {
      "x": -0.5,
      "y": -0.5,
      "z": 0.5,
      "w": -0.5
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  },
  {
    "prefabPath": "Prefabs/Block/2x2",
    "position": {
      "x": 0.5,
      "y": 0.85,
      "z": 0.5
    },
    "rotation": {
      "x": 0.0,
      "y": -0.70711,
      "z": 0.70711,
      "w": 0.0
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  },
  {
    "prefabPath": "Prefabs/Block/L",
    "position": {
      "x": 0.5,
      "y": 0.85,
      "z": 0.0
    },
    "rotation": {
      "x": -0.70711,
      "y": 0.0,
      "z": 0.0,
      "w": -0.70711
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  },
  {
    "prefabPath": "Prefabs/Block/L",
    "position": {
      "x": 0.0,
      "y": 0.85,
      "z": 0.5
    },
    "rotation": {
      "x": -0.5,
      "y": -0.5,
      "z": 0.5,
      "w": -0.5
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  },
  {
    "prefabPath": "Prefabs/Block/L",
    "position": {
      "x": 0.0,
      "y": 0.85,
      "z": 0.5
    },
    "rotation": {
      "x": -0.5,
      "y": 0.5,
      "z": -0.5,
      "w": -0.5
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  },
  {
    "prefabPath": "Prefabs/Block/L",
    "position": {
      "x": 0.5,
      "y": 0.85,
      "z": 0.0
    },
    "rotation": {
      "x": 0.0,
      "y": -0.70711,
      "z": 0.70711,
      "w": 0.0
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  },
  {
    "prefabPath": "Prefabs/Block/L Reverse",
    "position": {
      "x": 0.5,
      "y": 0.85,
      "z": 0.0
    },
    "rotation": {
      "x": -0.70711,
      "y": 0.0,
      "z": 0.0,
      "w": -0.70711
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  },
  {
    "prefabPath": "Prefabs/Block/L Reverse",
    "position": {
      "x": 0.0,
      "y": 0.85,
      "z": 0.5
    },
    "rotation": {
      "x": -0.5,
      "y": -0.5,
      "z": 0.5,
      "w": -0.5
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  },
  {
    "prefabPath": "Prefabs/Block/L Reverse",
    "position": {
      "x": 0.0,
      "y": 0.85,
      "z": 0.5
    },
    "rotation": {
      "x": -0.5,
      "y": 0.5,
      "z": -0.5,
      "w": -0.5
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  },
  {
    "prefabPath": "Prefabs/Block/L Reverse",
    "position": {
      "x": 0.5,
      "y": 0.85,
      "z": 0.0
    },
    "rotation": {
      "x": 0.0,
      "y": -0.70711,
      "z": 0.70711,
      "w": 0.0
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  },
  {
    "prefabPath": "Prefabs/Block/Plus",
    "position": {
      "x": 0.0,
      "y": 0.85,
      "z": 0.0
    },
    "rotation": {
      "x": -0.70711,
      "y": 0.0,
      "z": 0.0,
      "w": -0.70711
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  },
  {
    "prefabPath": "Prefabs/Block/Small L",
    "position": {
      "x": 0.5,
      "y": 0.85,
      "z": 0.5
    },
    "rotation": {
      "x": -0.70711,
      "y": 0.0,
      "z": 0.0,
      "w": -0.70711
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  },
  {
    "prefabPath": "Prefabs/Block/Small L",
    "position": {
      "x": 0.5,
      "y": 0.85,
      "z": 0.5
    },
    "rotation": {
      "x": -0.5,
      "y": -0.5,
      "z": 0.5,
      "w": -0.5
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  },
  {
    "prefabPath": "Prefabs/Block/Small L",
    "position": {
      "x": 0.5,
      "y": 0.85,
      "z": 0.5
    },
    "rotation": {
      "x": -0.5,
      "y": 0.5,
      "z": -0.5,
      "w": -0.5
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  },
  {
    "prefabPath": "Prefabs/Block/Small L",
    "position": {
      "x": 0.5,
      "y": 0.85,
      "z": 0.5
    },
    "rotation": {
      "x": 0.0,
      "y": -0.70711,
      "z": 0.70711,
      "w": 0.0
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  },
  {
    "prefabPath": "Prefabs/Block/T",
    "position": {
      "x": 0.0,
      "y": 0.85,
      "z": 0.5
    },
    "rotation": {
      "x": -0.70711,
      "y": 0.0,
      "z": 0.0,
      "w": -0.70711
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  },
  {
    "prefabPath": "Prefabs/Block/T",
    "position": {
      "x": 0.5,
      "y": 0.85,
      "z": 0.0
    },
    "rotation": {
      "x": -0.5,
      "y": -0.5,
      "z": 0.5,
      "w": -0.5
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  },
  {
    "prefabPath": "Prefabs/Block/T",
    "position": {
      "x": 0.5,
      "y": 0.85,
      "z": 0.0
    },
    "rotation": {
      "x": -0.5,
      "y": 0.5,
      "z": -0.5,
      "w": -0.5
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  },
  {
    "prefabPath": "Prefabs/Block/T",
    "position": {
      "x": 0.0,
      "y": 0.85,
      "z": 0.5
    },
    "rotation": {
      "x": 0.0,
      "y": -0.70711,
      "z": 0.70711,
      "w": 0.0
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  },
  {
    "prefabPath": "Prefabs/Block/U",
    "position": {
      "x": 0.5,
      "y": 0.85,
      "z": 0.0
    },
    "rotation": {
      "x": -0.5,
      "y": 0.5,
      "z": -0.5,
      "w": -0.5
    },
    "scale": {
      "x": 0.5,
      "y": 0.5,
      "z": 0.5
    },
    "color": 0,
    "special": 0,
    "secondColor": 0,
    "moveType": 0,
    "turnCount": 0,
    "timeCount": 0,
    "hasKey": False,
    "hasClawHammer": False
  }
]