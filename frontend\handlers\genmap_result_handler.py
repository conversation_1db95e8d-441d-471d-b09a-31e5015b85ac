import requests
import streamlit as st

from frontend.utils.const import BACKEND_URL, BASIC_AUTH_PASSWORD, BASIC_AUTH_USERNAME
from frontend.utils.logger import logger


class GenMapResultHandler:
    def __init__(self):
        self.auth = (BASIC_AUTH_USERNAME, BASIC_AUTH_PASSWORD)

    def handle_gen_map_result(self, result, task_id, uploaded_file, wait_time):
        """Xử lý kết quả sinh map"""
        if result["success"]:
            # Hiển thị thông tin map mới
            game_info = result.get("game_info", {})
            st.success(f"""
                    ✅ **Map mới đã được sinh thành công!**  
                    📏 <PERSON><PERSON>ch thước: {game_info.get("size_x", "N/A")} x {game_info.get("size_y", "N/A")}  
                    🧩 Số blocks: {game_info.get("blocks_count", "N/A")}  
                    🚪 Số doors: {game_info.get("doors_count", "N/A")}  
                    ⚡ Thời gian: {wait_time}s
                    """)

            # Hiển thị ảnh map mới
            try:
                logger.info(f"🖼️ Gửi request map-image - Task ID: {task_id}")
                img_response = requests.get(
                    f"{BACKEND_URL}/map-image/{task_id}",
                    auth=self.auth,
                )
                if img_response.status_code == 200:
                    st.image(img_response.content, caption="Map mới được sinh")

                    # Download buttons
                    col_dl1, col_dl2 = st.columns(2)

                    with col_dl1:
                        st.download_button(
                            label="📥 Download PNG",
                            data=img_response.content,
                            file_name=f"{uploaded_file.name.replace('.json', '')}_generated_map.png",
                            mime="image/png",
                            on_click="ignore"  # Mặc định là rerun()
                        )

                    with col_dl2:
                        try:
                            logger.info(f"📄 Gửi request map-json - Task ID: {task_id}")
                            json_response = requests.get(
                                f"{BACKEND_URL}/map-json/{task_id}",
                                auth=self.auth,
                            )
                            if json_response.status_code == 200:
                                st.download_button(
                                    label="📥 Download JSON",
                                    data=json_response.content,
                                    file_name=f"{uploaded_file.name.replace('.json', '')}_generated_map.json",
                                    mime="application/json",
                                    on_click="ignore"  # Mặc định là rerun()
                                )
                        except Exception as e:
                            st.warning(f"Không tải được JSON: {str(e)}")

            except Exception as e:
                st.warning(f"Không tải được ảnh: {str(e)}")
        else:
            st.error(f"❌ {result.get('message', 'Không sinh được map')}")