#!/bin/sh

# T<PERSON><PERSON> thư mục .streamlit nếu chưa tồn tại
mkdir -p .streamlit

# <PERSON><PERSON> các biến môi trường vào file secrets.toml
echo "[auth]" > .streamlit/secrets.toml
echo "redirect_uri = \"${STREAMLIT_REDIRECT_URI}\"" >> .streamlit/secrets.toml
echo "cookie_secret = \"${STREAMLIT_COOKIE_SECRET}\"" >> .streamlit/secrets.toml
echo "client_id = \"${GOOGLE_CLIENT_ID}\"" >> .streamlit/secrets.toml
echo "client_secret = \"${GOOGLE_CLIENT_SECRET}\"" >> .streamlit/secrets.toml
echo "server_metadata_url = \"https://accounts.google.com/.well-known/openid-configuration\"" >> .streamlit/secrets.toml

# Kiểm tra file đã tạo
echo "Secrets file created"