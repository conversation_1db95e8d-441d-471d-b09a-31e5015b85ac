import copy
import random

from src.check_solvable_service.config import ExperimentConfig
from src.check_solvable_service.solver import <PERSON>uz<PERSON><PERSON>olver
from src.game.game_state_base import GameStateBase
from src.game.objects import Block, Object
from src.gen_map_service.constants import UNIQUE_BASE_BLOCKS
from src.heuristic.manhattan_heuristic import ManhattanHeuristic


def have_same_size(object1: Object, object2: Object) -> bool:
    object1_min_x = min(object1.subBlocks, key=lambda cell: cell[0])[0]
    object1_min_y = min(object1.subBlocks, key=lambda cell: cell[1])[1]
    object1_max_x = max(object1.subBlocks, key=lambda cell: cell[0])[0]
    object1_max_y = max(object1.subBlocks, key=lambda cell: cell[1])[1]

    object2_min_x = min(object2.subBlocks, key=lambda cell: cell[0])[0]
    object2_min_y = min(object2.subBlocks, key=lambda cell: cell[1])[1]
    object2_max_x = max(object2.subBlocks, key=lambda cell: cell[0])[0]
    object2_max_y = max(object2.subBlocks, key=lambda cell: cell[1])[1]

    width1 = object1_max_x - object1_min_x
    height1 = object1_max_y - object1_min_y

    width2 = object2_max_x - object2_min_x
    height2 = object2_max_y - object2_min_y

    return width1 == width2 and height1 == height2


def get_all_exitable_blocks(state: GameStateBase):
    exitable_blocks = set()
    door_colors = list({door.color for door in state.doors})

    original_blocks = state.blocks
    original_blocks_occupied = state.blocks_occupied.copy()

    try:
        state.blocks = []
        state.blocks_occupied = set()

        xs = list(range(state.size_x))
        zs = list(range(state.size_y))

        max_positions_per_shape = min(state.size_x * state.size_y, 100)

        config = ExperimentConfig(log_level="ERROR", debug_viz=False)
        solver = PuzzleSolver(config)
        heuristic = ManhattanHeuristic()

        for base_data in UNIQUE_BASE_BLOCKS:
            positions = [(dx, dz) for dx in xs for dz in zs]
            random.shuffle(positions)
            positions = positions[:max_positions_per_shape]

            for color in door_colors:
                found_for_color = False
                for dx, dz in positions:
                    block_data = copy.deepcopy(base_data)
                    block_data["color"] = color
                    block_data["position"]["x"] = base_data["position"]["x"] + dx
                    block_data["position"]["z"] = base_data["position"]["z"] + dz

                    new_block = Block(block_data, len(state.blocks))

                    conflict = False
                    for cell in new_block.subBlocks:
                        if (
                            cell in state.walls_occupied
                            or cell in state.doors_occupied
                            or cell in state.special_tiles_occupied
                        ):
                            conflict = True
                            break
                    if conflict:
                        continue

                    state.blocks.append(new_block)
                    state.add_occupied(new_block)

                    try:
                        solvable, _ = solver.check_solvable(state, heuristic)

                        if solvable:
                            exitable_blocks.add(
                                (
                                    base_data["prefabPath"],
                                    color,
                                    block_data["position"]["x"],
                                    block_data["position"]["z"],
                                    block_data["rotation"]["x"],
                                    block_data["rotation"]["y"],
                                    block_data["rotation"]["z"],
                                    block_data["rotation"]["w"],
                                )
                            )
                            found_for_color = True
                            break
                    finally:
                        state.remove_occupied(new_block)
                        state.blocks.pop()

                if found_for_color:
                    continue

        return list(exitable_blocks)
    finally:
        state.blocks = original_blocks
        state.blocks_occupied = original_blocks_occupied


def add_blocks(
    state: GameStateBase,
    num_blocks: int = 1,
    unique_blocks=UNIQUE_BASE_BLOCKS,
    n_attempts: int = 1000,
    is_solvable: bool = False,
    is_random_color: bool = True,
    randomize_steps: int = 2000,
    max_shuffle: int = 3,
    debug_viz: bool = True,
):
    import numpy as np

    config = ExperimentConfig(
        randomizing_steps=randomize_steps,
        max_shuffle=max_shuffle,
        max_trials=1,
        debug_viz=debug_viz,
        log_level="ERROR",
    )
    solver = PuzzleSolver(config)
    heuristic = ManhattanHeuristic()

    attempts = 0
    count = 0
    all_colors = [door.color for door in state.doors]
    block_indices = list(range(len(unique_blocks)))

    is_space_left = False
    for dx in range(state.size_x):
        for dz in range(state.size_y):
            if (
                (dx, dz) not in state.blocks_occupied
                and (dx, dz) not in state.doors_occupied
                and (dx, dz) not in state.walls_occupied
                and (dx, dz) not in state.special_tiles_occupied
            ):
                is_space_left = True
                break
    if not is_space_left:
        if debug_viz:
            print("[WARNING] No space left on the board.")
        return

    while count < num_blocks and attempts < n_attempts:
        attempts += 1
        idx = random.choice(block_indices)
        base_data = copy.deepcopy(unique_blocks[idx])

        if is_random_color:
            base_data["color"] = random.choice(all_colors)

        xs = list(range(state.size_x))
        zs = list(range(state.size_y))

        # add negative values
        xs = [-x for x in xs] + xs
        zs = [-z for z in zs] + zs

        np.random.shuffle(xs)
        np.random.shuffle(zs)

        is_added = False
        for dx in xs:
            for dz in zs:
                block_data = copy.deepcopy(base_data)
                block_data["position"]["x"] += dx
                block_data["position"]["z"] += dz

                new_block = Block(block_data, len(state.blocks))
                flag = True
                for subBlock in new_block.subBlocks:
                    if (
                        subBlock[0] < 0
                        or subBlock[0] >= state.size_x
                        or subBlock[1] < 0
                        or subBlock[1] >= state.size_y
                    ):
                        flag = False
                        break
                if not flag:
                    continue

                flag = True
                for subBlock in new_block.subBlocks:
                    if (
                        subBlock in state.doors_occupied
                        or subBlock in state.blocks_occupied
                        or subBlock in state.walls_occupied
                        or subBlock in state.special_tiles_occupied
                    ):
                        flag = False
                        break

                if flag:
                    state.blocks.append(new_block)
                    state.add_occupied(new_block)
                    if is_solvable:
                        solvable, _ = solver.check_solvable(state, heuristic)
                        if not solvable:
                            state.remove_occupied(new_block)
                            state.blocks.pop()
                            break

                    if debug_viz:
                        print(
                            f"[ADD BLOCK] {base_data['prefabPath']} at ({block_data['position']['x']}, {block_data['position']['z']}) with color {base_data['color']}, total: {count}"
                        )
                    count += 1
                    is_added = True
                    break
            if is_added:
                break

    if count < num_blocks and debug_viz:
        print(f"[WARNING] Only added {count} / {num_blocks} blocks after {attempts} attempts.")
