import requests
import streamlit as st

from frontend.utils.const import BACKEND_URL, BASIC_AUTH_PASSWORD, BASIC_AUTH_USERNAME
from frontend.utils.logger import logger


class TaskManager:
    def __init__(self):
        self.auth = (BASIC_AUTH_USERNAME, BASIC_AUTH_PASSWORD)

    def create_solve_task(self, uploaded_file, solve_mode, order_input=None):
        """Tạo task giải puzzle"""
        try:
            uploaded_file.seek(0)

            files = {
                "file": (
                    uploaded_file.name,
                    uploaded_file.getvalue(),
                    "application/json",
                )
            }

            data = {"mode": solve_mode}
            if solve_mode == "order_block" and order_input and order_input.strip():
                data["order"] = order_input.strip()

            logger.info(
                f"🚀 Gửi request solve-async - Mode: {solve_mode}, File: {uploaded_file.name}"
            )
            response = requests.post(
                f"{BACKEND_URL}/v2/solve-async",
                files=files,
                data=data,
                auth=self.auth,
            )
            logger.info(
                f"📥 Response solve-async - Status: {response.status_code}, Content: {response.text[:200]}..."
            )

            if response.status_code == 200:
                task_info = response.json()
                task_id = task_info["task_id"]

                # Update session state
                st.session_state.current_task_id = task_id
                st.session_state.task_running = True
                st.session_state.cancel_task = False
                st.session_state.task_mode = solve_mode

                return True, task_id
            else:
                return False, f"❌ Lỗi tạo task: {response.text}"

        except Exception as e:
            logger.error(f"❌ Lỗi kết nối API: {str(e)}")
            return False, f"❌ Lỗi kết nối API: {str(e)}"

    def create_gen_map_task(self, uploaded_file, mode=None, num_blocks=None):
        """Tạo task sinh map"""
        try:
            uploaded_file.seek(0)

            files = {
                "file": (
                    uploaded_file.name,
                    uploaded_file.getvalue(),
                    "application/json",
                )
            }

            query_params = {}
            if mode:
                query_params["mode"] = mode
            if num_blocks:
                query_params["num_blocks"] = num_blocks

            logger.info(f"🗺️ Gửi request gen-map-async - File: {uploaded_file.name}")
            response = requests.post(
                f"{BACKEND_URL}/v2/gen-map-async",
                files=files,
                auth=self.auth,
                params=query_params,
            )

            logger.info(
                f"📥 Response gen-map-async - Status: {response.status_code}, Content: {response.text[:200]}..."
            )

            if response.status_code == 200:
                task_info = response.json()
                task_id = task_info["task_id"]

                # Update session state
                st.session_state.current_task_id = task_id
                st.session_state.task_running = True
                st.session_state.cancel_task = False
                st.session_state.task_mode = "gen_map"

                return True, task_id
            else:
                return False, f"❌ Lỗi tạo gen map task: {response.text}"

        except Exception as e:
            logger.error(f"❌ Lỗi kết nối API: {str(e)}")
            return False, f"❌ Lỗi kết nối API: {str(e)}"

    def cancel_task(self, task_id):
        """Hủy task"""
        try:
            cancel_response = requests.delete(f"{BACKEND_URL}/v2/cancel/{task_id}", auth=self.auth)

            # Reset session state
            st.session_state.task_running = False
            st.session_state.current_task_id = None
            st.session_state.cancel_task = False
            st.session_state.task_mode = None

            return cancel_response.status_code
        except Exception as e:
            raise e

    def get_task_progress(self, task_id) -> tuple[dict | None, str | None]:
        """Lấy tiến độ task"""
        try:
            status_response = requests.get(
                f"{BACKEND_URL}/v2/progress/{task_id}",
                auth=self.auth,
            )

            if status_response.status_code == 404:
                return None, "Task không tồn tại hoặc đã bị hủy"

            return status_response.json(), None
        except Exception as e:
            return None, str(e)

    def get_task_result(self, task_id):
        """Lấy kết quả task"""
        try:
            logger.info(f"📊 Gửi request result - Task ID: {task_id}")
            result_response = requests.get(
                f"{BACKEND_URL}/v2/result/{task_id}",
                auth=self.auth,
            )
            logger.info(f"📥 Response result - Status: {result_response.status_code}")
            return result_response.json()
        except Exception as e:
            return {"error": str(e)}

    def cleanup_task(self, task_id):
        """Dọn dẹp task"""
        try:
            logger.info(f"🧹 Gửi request cleanup - Task ID: {task_id}")
            cleanup_response = requests.delete(
                f"{BACKEND_URL}/cleanup/{task_id}",
                auth=self.auth,
            )
            logger.info(f"📥 Response cleanup - Status: {cleanup_response.status_code}")
        except:  # noqa: E722
            pass  # Ignore cleanup errors

    def reset_session_state(self):
        """Reset session state"""
        st.session_state.task_running = False
        st.session_state.current_task_id = None
        st.session_state.task_mode = None


task_manager = TaskManager()
