class SpecialTile:
    def __init__(self, id, specialTile, gridPos, color):
        self.id = id
        self.special = specialTile
        self.x = gridPos["x"]
        self.y = gridPos["y"]
        self.color = color

    def copy(self):
        return SpecialTile(self.id, self.special, {"x": self.x, "y": self.y}, self.color)
    def to_json(self):
        return {
            "specialTile": 2,
            "gridPos": {"x": self.x, "y": self.y},
            "color": self.color,
        }
