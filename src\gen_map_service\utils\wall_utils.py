from typing import List

from src.game.game_state_base import GameStateBase
from src.game.objects import Wall

from ..constants import PREFABS_CORNOR_WALL
from .map_utils import is_adjacent_door


def get_internal_wall(state: GameStateBase):
    external_set = set(get_external_wall(state))
    return [wall for wall in state.walls if wall not in external_set]

def get_external_wall(state: GameStateBase) -> List[Wall]:
    wall_external_list = set()
    for wall in state.walls:
        if len(state.get_valid_moves_for_gen(wall, max_step=20)) >= 10:
            wall_external_list.add(wall)
        elif wall.prefabPath == PREFABS_CORNOR_WALL:
            wall_external_list.add(wall)
        elif is_adjacent_door(wall, state.doors):
            wall_external_list.add(wall)
            
    return list(wall_external_list)