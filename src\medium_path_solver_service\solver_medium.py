import copy
from datetime import datetime
import itertools
import logging
from queue import PriorityQueue
import random
from typing import Any, List, Set, Tuple

from ..game.game_state_medium_path import GameState
from ..game.objects import Door
from ..heuristic.block_based_heuristic import BaseBlockHeuristic
from .config import MediumPathSolverConfig

logger = logging.getLogger(__name__)


class MediumPathSolver:
    def __init__(self, config: MediumPathSolverConfig):
        self.config = config

    def solve(self, init_state: GameState, heuristic: BaseBlockHeuristic) -> List[Tuple[str, Any]]:
        stats = {
            "start_time": datetime.now(),
            "end_time": None,
            "iteration": 0,
            "cache_size": 0,
        }

        # Initial setup
        block_target = None
        while True:
            priority_blocks = init_state.blocks[:]
            priority_blocks.sort(
                key=lambda b: heuristic.heuristic_base_block(init_state, b.id)
            )

            priority_blocks = priority_blocks[:5]
            if not priority_blocks:
                logger.debug("⚠️ No priority blocks available, skipping 789")
                continue
            block_target = random.choice(priority_blocks)
            door_target = init_state.get_accessible_doors(block_target)[0]
            if door_target:
                break

        # Initial heuristic score
        init_heuristic = float("inf")
        logger.debug(
            f"Iteration {0}, Remaining Blocks: {len(init_state.blocks)}, Heuristic Score: {init_heuristic}, Target Block: {block_target.id}, Target Door: {door_target.id} heap_size: {0}, Last Score: {float('inf')}"
        )
        # init_state.visualize_state()

        # Keep a copy for image generation
        init_copy = copy.deepcopy(init_state)

        # A* algorithm setup
        pq = PriorityQueue()
        counter = itertools.count()
        init_set = {block_target.id}
        pq.put(
            (
                len(init_state.blocks),
                init_heuristic,
                next(counter),
                init_state,
                [],
                init_set,
                block_target.id,
                door_target,
            )
        )

        visited = set()
        visited.add(
            (
                len(init_state.blocks),
                block_target.id,
                door_target,
                init_state.to_hashable(),
                -1,
                -1,
                -1,
            )
        )

        iteration = 0
        switch_block_count = 0
        last_score = init_heuristic

        while not pq.empty() and iteration < self.config.max_iteration:
            score: int
            current_improved: Set[int]
            block_target_id: int
            door_target: Door
            (
                block_remain,
                score,
                _,
                current_state,
                path,
                current_improved,
                block_target_id,
                door_target,
            ) = pq.get()

            # Cập nhật iteration và count_switch
            iteration += 1
            if score >= last_score:
                switch_block_count += 1
            else:
                last_score = score
                switch_block_count = 0

            if (
                switch_block_count >= self.config.max_improving
                and switch_block_count % self.config.max_improving == 0
            ):
                while True:
                    # print(
                    #     f"Current set: {current_improved}, switch_block_count: {switch_block_count}"
                    # )

                    # if len(current_improved) >= 4:
                    #     print("🔁 current_improved quá lớn, reset lại set...")
                    #     current_improved.clear()
                    #     continue

                    # Lọc các block chưa improve
                    priority_blocks = [
                        block for block in current_state.blocks if block.id not in current_improved
                    ]

                    # Nếu không còn block nào -> reset set và thử lại
                    if not priority_blocks:
                        logger.debug(
                            "⚠️ No priority blocks available, resetting current_improved..."
                        )
                        current_improved.clear()
                        continue  # Quay lại đầu vòng lặp để rebuild priority_blocks

                    # Sắp xếp theo score
                    try:
                        priority_blocks.sort(
                            key=lambda b: heuristic.heuristic_base_block(current_state, b.id)
                        )
                    except IndexError:
                        logger.debug("⚠️ Một block không có cửa nào! Bỏ qua block này.")
                        continue

                    # Giữ lại top 5 block
                    priority_blocks = priority_blocks[:5]

                    # Chọn block mục tiêu
                    block_target = random.choice(priority_blocks)
                    block_target_id = block_target.id
                    current_improved.add(block_target_id)

                    # Chọn cửa tương ứng
                    matching_doors = current_state.get_accessible_doors(block_target)
                    if matching_doors:
                        door_target = matching_doors[0]
                        break

            # Trực quan hóa mỗi CHECK_EVERY bước
            if iteration % self.config.check_every == 0:
                logger.debug(
                    f"Iteration {iteration}, Remaining Blocks: {block_remain}, Heuristic Score: {score}, Target Block: {block_target_id}, Target Door: {door_target.id} heap_size: {pq.qsize()}, Last Score: {last_score}, current_improved: {len(current_improved)}, visited: {len(visited)}"
                )
                # current_state.visualize_state()
                # time.sleep(1)

            # Kiểm tra điều kiện thắng
            if current_state.is_win():
                stats["end_time"] = datetime.now()
                logger.debug(
                    f"🎉 Solution found in {iteration} iterations in {str(stats['end_time'] - stats['start_time'])}!"
                )
                stats["iteration"] = iteration
                stats["cache_size"] = len(visited)

                # Save logs
                # MediumPathSolverUtils.save_logs(
                #     f"Level_{init_state.level}",
                #     path=path,
                #     stats=stats,
                #     found=True,
                #     config=self.config,
                # )

                # # Save images if requested
                # if self.config.save_image:
                #     MediumPathSolverUtils._save_solution_images(init_copy, path, self.config)
                return path

            # Xử lý khả năng ăn blocks
            can_eat_blocks = current_state.can_eat()
            if can_eat_blocks:
                new_state = copy.deepcopy(current_state)
                new_path = path[:]

                for block in can_eat_blocks:
                    block_in_new_state = new_state.find_block(block.id)
                    if block_in_new_state:
                        new_state.eat_block(block_in_new_state)
                        new_path.append(("eat", block.id))
                    logger.debug(f"Eating block 🍽️ Block remaing: {len(new_state.blocks)}")

                # pq = PriorityQueue()
                # counter = itertools.count()

                # Nếu ăn được, cập nhật target block và door
                while True and not new_state.is_win():
                    current_improved.clear()
                    priority_blocks = [
                        block for block in new_state.blocks if block.id not in current_improved
                    ]
                    priority_blocks.sort(
                        key=lambda b: heuristic.heuristic_base_block(new_state, b.id)
                    )
                    priority_blocks = priority_blocks[:5]
                    if not priority_blocks:
                        # print("⚠️ No priority blocks available, skipping 456")
                        continue
                    block_target = random.choice(priority_blocks)
                    block_target_id = block_target.id
                    current_improved.add(block_target.id)
                    door_target = new_state.get_accessible_doors(block_target)[0]
                    if door_target:
                        break

                # Kiểm tra thắng sau khi ăn
                if new_state.is_win():
                    stats["end_time"] = datetime.now()
                    stats["iteration"] = iteration
                    stats["cache_size"] = len(visited)
                    logger.debug(
                        f"🎉 Solution found by eating blocks in {iteration} iterations in {str(stats['end_time'] - stats['start_time'])}!"
                    )
                    # MediumPathSolverUtils.save_logs(
                    #     f"Level_{init_state.level}",
                    #     new_path,
                    #     stats,
                    #     found=True,
                    #     config=self.config,
                    # )
                    # if self.config.save_image:
                    #     MediumPathSolverUtils._save_solution_images(
                    #         init_copy, new_path, config=self.config
                    #     )
                    return new_path

                # Thêm vào hàng đợi nếu chưa visited
                new_sig = new_state.to_hashable()
                eat_key = (
                    len(new_state.blocks),
                    block_target_id,
                    door_target,
                    new_sig,
                    -1,
                    -1,
                    -1,
                )
                if eat_key not in visited:
                    visited.add(
                        (
                            len(new_state.blocks),
                            block_target_id,
                            door_target,
                            new_sig,
                            -1,
                            -1,
                            -1,
                        )
                    )
                    new_heuristic = heuristic.heuristic_base_block(
                        new_state, block_target_id
                    )
                    pq.put(
                        (
                            len(new_state.blocks),
                            new_heuristic,
                            next(counter),
                            new_state,
                            new_path,
                            current_improved,
                            block_target_id,
                            door_target,
                        )
                    )

            else:
                candidate_moves = []
                for block in current_state.blocks:
                    moves = current_state.get_valid_moves(block, max_step=self.config.max_depth)
                    if not moves:
                        continue
                    for move in moves:
                        new_state = copy.deepcopy(current_state)
                        block_in_new_state = new_state.find_block(block.id)
                        if block_in_new_state and new_state.move_block(
                            block_in_new_state, move[0], move[1]
                        ):
                            new_heuristic = heuristic.heuristic_base_block(
                                new_state, block_target_id
                            )
                            candidate_moves.append(
                                (new_heuristic, block.id, move, new_state)
                            )
                candidate_moves.sort(key=lambda x: x[0])
                for new_heuristic, block_id, move, new_state in candidate_moves[:1]:
                    new_sig = new_state.to_hashable()
                    if True:
                        visited.add(
                            (
                                len(new_state.blocks),
                                block_target_id,
                                door_target,
                                new_sig,
                                block_id,
                                move[0],
                                move[1],
                            )
                        )
                        new_path = path[:] + [(block_id, move[0], move[1])]
                        pq.put(
                            (
                                len(new_state.blocks),
                                new_heuristic,
                                next(counter),
                                new_state,
                                new_path,
                                current_improved,
                                block_target_id,
                                door_target,
                            )
                        )

        # No solution found
        stats["end_time"] = datetime.now()
        stats["iteration"] = iteration
        print(
            f"❌ No solution found after {iteration} iterations in {str(stats['end_time'] - stats['start_time'])}!"
        )
        # MediumPathSolverUtils.save_logs(
        #     f"Level_{init_state.level}", [], stats, False, config=self.config
        # )
        return []
