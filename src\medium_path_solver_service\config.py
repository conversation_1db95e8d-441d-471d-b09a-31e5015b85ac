from dataclasses import dataclass


@dataclass
class MediumPathSolverConfig:
    max_depth: int = 20
    look_ahead_depth: int = 3
    max_iteration: int = 150000
    max_improving: int = 50
    check_every: int = 1
    save_image: bool = True
    save_log_path: str = "heuristic_clean_cost_unordered/logs"
    save_image_path: str = "heuristic_clean_cost_unordered/images"
    max_priority_blocks: int = 10


class SolverWithEatOrderConfig:
    cache_log: str = "solver/logs"
    save_log_path: str = "heuristic_clean_cost_ordered/logs"
    save_image_path: str = "heuristic_clean_cost_ordered/images"
    
class ShortestPathSolverConfig:
    save_images: bool = False
    save_logs: bool = False
    save_log_path: str = "shortest_path_unordered_results/logs"
    save_image_path: str = "shortest_path_unordered_results/images"
    check_every: int = 100000
    patience: int = 100
    max_iteration: int = 30000
