{"cells": [{"cell_type": "code", "execution_count": 1, "id": "6e94577f", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "\n", "sys.path.append(os.path.abspath(\"..\"))\n", "\n", "from src.game.game_state_base import GameStateBase"]}, {"cell_type": "code", "execution_count": 2, "id": "1019c66d", "metadata": {}, "outputs": [], "source": ["from src.game.visualization.visualize_game_state import visualize_state\n", "from src.gen_map_service.api import generate_map"]}, {"cell_type": "code", "execution_count": 3, "id": "5502f331", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["path = \"../Level_Updated/Level_24.1.json\"\n", "initial_state = GameStateBase(path)\n", "visualize_state(initial_state)"]}, {"cell_type": "code", "execution_count": null, "id": "ff165f2c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-15 10:57:23,503 - DEBUG - Starting solver: 18 blocks\n", "2025-08-15 10:57:23,538 - DEBUG - All Randomizing: 18 blocks, 500 steps\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[ADD BLOCK] Prefabs/Block/1x2 at (1.0, 1.5) with color 0, total: 0\n", "[ADD BLOCK] Prefabs/Block/1x1 at (3.0, 4.0) with color 4, total: 1\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[7]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m gened_map = \u001b[43mgenerate_map\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpath\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m      2\u001b[39m visualize_state(gened_map)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Documents\\WorkSpace\\SlideJam-Solver\\src\\gen_map_service\\api.py:93\u001b[39m, in \u001b[36mgenerate_map\u001b[39m\u001b[34m(gamestate_path)\u001b[39m\n\u001b[32m     91\u001b[39m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[32m     92\u001b[39m     gened_state = permute_map_strategic(state)\n\u001b[32m---> \u001b[39m\u001b[32m93\u001b[39m     solution, metrics = \u001b[43msolver\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcheck_solvable\u001b[49m\u001b[43m(\u001b[49m\u001b[43mgened_state\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mheuristic\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     94\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m solution:\n\u001b[32m     95\u001b[39m         \u001b[38;5;28;01mbreak\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Documents\\WorkSpace\\SlideJam-Solver\\src\\check_solvable_service\\solver.py:238\u001b[39m, in \u001b[36mPuzzleSolver.check_solvable\u001b[39m\u001b[34m(self, state, heuristic)\u001b[39m\n\u001b[32m    235\u001b[39m     visualize_state(trial_state)\n\u001b[32m    236\u001b[39m previous_num_blocks = \u001b[38;5;28mlen\u001b[39m(trial_state.blocks)\n\u001b[32m--> \u001b[39m\u001b[32m238\u001b[39m trial_state, shuffle_path = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mshuffle_all_blocks\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtrial_state\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    240\u001b[39m \u001b[38;5;28mself\u001b[39m.path.extend(shuffle_path)\n\u001b[32m    241\u001b[39m \u001b[38;5;28mself\u001b[39m.metrics.shuffle_count += \u001b[32m1\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Documents\\WorkSpace\\SlideJam-Solver\\src\\check_solvable_service\\solver.py:54\u001b[39m, in \u001b[36mPuzzleSolver.shuffle_all_blocks\u001b[39m\u001b[34m(self, state)\u001b[39m\n\u001b[32m     51\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m state.blocks:\n\u001b[32m     52\u001b[39m     \u001b[38;5;28;01mbreak\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m54\u001b[39m edible_blocks = \u001b[43mstate\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcan_eat\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     55\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m block \u001b[38;5;129;01min\u001b[39;00m edible_blocks:\n\u001b[32m     56\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.config.debug_viz:\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Documents\\WorkSpace\\SlideJam-Solver\\src\\game\\game_state_base.py:352\u001b[39m, in \u001b[36mGameStateBase.can_eat\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    348\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m block \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m.blocks:\n\u001b[32m    350\u001b[39m     has_matching_door = \u001b[38;5;28many\u001b[39m(\u001b[38;5;28mself\u001b[39m.is_accessible_door(block, door) \u001b[38;5;28;01mfor\u001b[39;00m door \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m.doors)\n\u001b[32m--> \u001b[39m\u001b[32m352\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m has_matching_door \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mcan_exit\u001b[49m\u001b[43m(\u001b[49m\u001b[43mblock\u001b[49m\u001b[43m)\u001b[49m:\n\u001b[32m    353\u001b[39m         result.append(block)\n\u001b[32m    355\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m result\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Documents\\WorkSpace\\SlideJam-Solver\\src\\game\\game_state_base.py:329\u001b[39m, in \u001b[36mGameStateBase.can_exit\u001b[39m\u001b[34m(self, block)\u001b[39m\n\u001b[32m    326\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m moved_cells[\u001b[32m0\u001b[39m] \u001b[38;5;129;01min\u001b[39;00m visited:\n\u001b[32m    327\u001b[39m     \u001b[38;5;28;01mcontinue\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m329\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28;43many\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mcell\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43moccupied\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mcell\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mmoved_cells\u001b[49m\u001b[43m)\u001b[49m:\n\u001b[32m    330\u001b[39m     \u001b[38;5;28;01mcontinue\u001b[39;00m\n\u001b[32m    332\u001b[39m temp_game.move_block(temp_game.find_block(block.id), dx, dy)\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m<PERSON><PERSON> crashed while executing code in the current cell or a previous cell. \n", "\u001b[1;31m<PERSON><PERSON>se review the code in the cell(s) to identify a possible cause of the failure. \n", "\u001b[1;31mClick <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["gened_map = generate_map(path)\n", "visualize_state(gened_map)"]}, {"cell_type": "code", "execution_count": null, "id": "658f76a2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "autogluon", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}