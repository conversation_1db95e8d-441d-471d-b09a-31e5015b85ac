import os
import traceback


def solve_level_wrapper(temp_path: str, task_id: str, mode: str, order: list = None):
    """Wrapper function để migrate logic từ solve_level_async_v2, sử dụng task manager"""
    try:
        print(f"🚀 Starting wrapper solve for task {task_id} with mode {mode}")

        # Import solver modules
        # Load game state
        from backend.core.task_manager import task_manager
        from src.check_solvable_service.api import check_solve
        from src.game.game_state_base import GameStateBase as GameState
        from src.medium_path_solver_service.api import solve
        from src.solver_block_based_service.api import solve_base_block, solve_order_block
        from src.visualization.streamlit_style_generator import StreamlitStyleGenerator

        task_manager.update_progress(task_id, 10, "Đang load level...")
        game = GameState(temp_path)

        result = {"success": False, "task_id": task_id, "mode": mode}

        if mode == "random":
            task_manager.update_progress(task_id, 20, "<PERSON><PERSON>t đ<PERSON>u kiểm tra random...")
            is_solve, solve_result = check_solve(temp_path)
            path = solve_result.get("path", [])
            if is_solve:
                len_path = len(path)
                task_manager.update_progress(task_id, 90, f"Tìm thấy lời giải với {len_path} moves")
                result = {
                    "success": True,
                    "solvable": True,
                    "path": path,
                    "path_length": len_path,
                    "task_id": task_id,
                    "mode": "random",
                    "message": f"Level co the giai duoc voi {len_path} moves",
                }
            else:
                error_block_id = solve_result.get("error_block_id")
                task_manager.update_progress(task_id, 90, "Level không thể giải được")
                result = {
                    "success": True,
                    "solvable": False,
                    "path": path,
                    "error_block_id": error_block_id,
                    "task_id": task_id,
                    "mode": "random",
                    "message": "Level khong the giai duoc",
                }

        elif mode == "check_then_solve":
            # PHASE 1: Random check
            task_manager.update_progress(task_id, 15, "Bước 1/2: Kiểm tra khả năng giải...")
            is_solve, random_result = check_solve(temp_path)

            if not is_solve:
                task_manager.update_progress(task_id, 90, "❌ Level không thể giải được")
                result = {
                    "success": True,
                    "solvable": False,
                    "message": "Level khong the giai duoc",
                }
            else:
                random_length = len(random_result.get("path", []))
                task_manager.update_progress(
                    task_id, 35, f"✅ Level có thể giải! Random: {random_length} moves"
                )

                # Check cancelled
                if task_manager.is_cancelled(task_id):
                    return

                # PHASE 2: Heuristic solve
                task_manager.update_progress(
                    task_id, 40, "Bước 2/2: Bắt đầu tối ưu với Heuristic..."
                )
                heuristic_result = solve_base_block(temp_path)

                if not heuristic_result or len(heuristic_result) == 0:
                    task_manager.update_progress(
                        task_id,
                        90,
                        f"⚠️ Heuristic thất bại, fallback Random ({random_length} moves)",
                    )
                    result = {
                        "success": True,
                        "solvable": True,
                        "random_path_length": random_length,
                        "message": f"Random solved ({random_length} moves) but heuristic failed",
                        "fallback_to_random": True,
                    }
                else:
                    # Có heuristic result - tạo visualization
                    heuristic_length = len(heuristic_result)
                    improvement = random_length - heuristic_length
                    task_manager.update_progress(
                        task_id,
                        60,
                        f"✅ Heuristic hoàn thành: {heuristic_length} moves (cải thiện {improvement})",
                    )

                    # Check cancelled
                    if task_manager.is_cancelled(task_id):
                        return

                    # Tạo visualization
                    task_manager.update_progress(task_id, 70, "Đang tạo video và ảnh demo...")
                    try:
                        visualizer = StreamlitStyleGenerator(game, task_id)
                        vis_result = visualizer.create_step_images_and_video(heuristic_result)
                        task_manager.update_progress(task_id, 95, "✅ Hoàn thành video và file zip")

                        result = {
                            "success": True,
                            "path": heuristic_result,
                            "task_id": task_id,
                            "mode": mode,
                            "random_path_length": random_length,
                            "heuristic_path_length": heuristic_length,
                            "improvement": improvement,
                            "visualization": vis_result,
                            "game_info": {
                                "level": game.level,
                                "name": game.name,
                                "difficulty": game.difficulty,
                                "size_x": game.size_x,
                                "size_y": game.size_y,
                                "blocks_count": len(game.blocks),
                            },
                        }
                    except Exception as vis_error:
                        print(f"❌ Visualization error: {vis_error}")
                        task_manager.update_progress(
                            task_id, 90, "⚠️ Giải được nhưng tạo video thất bại"
                        )
                        result = {
                            "success": True,
                            "path": heuristic_result,
                            "task_id": task_id,
                            "mode": mode,
                            "random_path_length": random_length,
                            "heuristic_path_length": heuristic_length,
                            "improvement": improvement,
                            "message": f"Solved but visualization failed: {str(vis_error)}",
                            "visualization": {"success": False, "error": str(vis_error)},
                            "game_info": {
                                "level": game.level,
                                "name": game.level,
                                "difficulty": game.difficulty,
                                "size_x": game.size_x,
                                "size_y": game.size_y,
                                "blocks_count": len(game.blocks),
                            },
                        }

        elif mode in ["heuristic_only", "medium_path_only"]:
            solve_result = None

            if mode == "heuristic_only":
                task_manager.update_progress(task_id, 20, "Bắt đầu giải với Heuristic...")
                solve_result = solve_base_block(temp_path)
            elif mode == "medium_path_only":
                task_manager.update_progress(task_id, 20, "Bắt đầu giải với Medium Path...")
                solve_result = solve(temp_path)

            # Check cancelled
            if task_manager.is_cancelled(task_id):
                return

            if solve_result and len(solve_result) > 0:
                algorithm_name = "Heuristic" if mode == "heuristic_only" else "Medium Path"
                task_manager.update_progress(
                    task_id, 60, f"✅ {algorithm_name} hoàn thành: {len(solve_result)} moves"
                )

                # Tạo visualization
                task_manager.update_progress(task_id, 70, "Đang tạo video và ảnh demo...")
                try:
                    visualizer = StreamlitStyleGenerator(game, task_id)
                    vis_result = visualizer.create_step_images_and_video(solve_result)
                    task_manager.update_progress(task_id, 95, "✅ Hoàn thành video và file zip")

                    result = {
                        "success": True,
                        "path": solve_result,
                        "task_id": task_id,
                        "mode": mode,
                        "visualization": vis_result,
                        "game_info": {
                            "level": game.level,
                            "name": game.name,
                            "difficulty": game.difficulty,
                            "size_x": game.size_x,
                            "size_y": game.size_y,
                            "blocks_count": len(game.blocks),
                        },
                    }

                except Exception as vis_error:
                    print(f"❌ Visualization error: {vis_error}")
                    task_manager.update_progress(
                        task_id, 90, "⚠️ Giải được nhưng tạo video thất bại"
                    )
                    result = {
                        "success": True,
                        "path": solve_result,
                        "task_id": task_id,
                        "mode": mode,
                        "message": f"Solved but visualization failed: {str(vis_error)}",
                        "visualization": {"success": False, "error": str(vis_error)},
                        "game_info": {
                            "level": game.level,
                            "name": game.name,
                            "difficulty": game.difficulty,
                            "size_x": game.size_x,
                            "size_y": game.size_y,
                            "blocks_count": len(game.blocks),
                        },
                    }
            else:
                task_manager.update_progress(task_id, 90, f"❌ {mode} solver failed")
                result = {"success": False, "message": f"{mode} solver failed - no path found"}

        elif mode == "order_block":
            # Sử dụng order được truyền vào
            if not order or len(order) == 0:
                task_manager.update_progress(task_id, 90, "❌ Thiếu danh sách thứ tự blocks")
                result = {
                    "success": False,
                    "message": "Cần cung cấp danh sách thứ tự blocks (order)",
                    "error": "MISSING_ORDER",
                }
            else:
                task_manager.update_progress(task_id, 20, f"Bắt đầu giải theo thứ tự: {order}")

                # Gọi hàm solve_order_block
                solve_result = solve_order_block(temp_path, order)

                # Check cancelled
                if task_manager.is_cancelled(task_id):
                    return

                if solve_result and len(solve_result) > 0:
                    task_manager.update_progress(
                        task_id, 60, f"✅ Order Block hoàn thành: {len(solve_result)} moves"
                    )

                    # Tạo visualization
                    task_manager.update_progress(task_id, 70, "Đang tạo video và ảnh demo...")
                    try:
                        visualizer = StreamlitStyleGenerator(game, task_id)
                        vis_result = visualizer.create_step_images_and_video(solve_result)
                        task_manager.update_progress(task_id, 95, "✅ Hoàn thành video và file zip")

                        result = {
                            "success": True,
                            "path": solve_result,
                            "task_id": task_id,
                            "mode": mode,
                            "order_used": order,
                            "visualization": vis_result,
                            "game_info": {
                                "level": game.level,
                                "name": game.name,
                                "difficulty": game.difficulty,
                                "size_x": game.size_x,
                                "size_y": game.size_y,
                                "blocks_count": len(game.blocks),
                            },
                        }

                    except Exception as vis_error:
                        print(f"❌ Visualization error: {vis_error}")
                        task_manager.update_progress(
                            task_id, 90, "⚠️ Giải được nhưng tạo video thất bại"
                        )
                        result = {
                            "success": True,
                            "path": solve_result,
                            "task_id": task_id,
                            "mode": mode,
                            "order_used": order,
                            "message": f"Solved but visualization failed: {str(vis_error)}",
                            "visualization": {"success": False, "error": str(vis_error)},
                            "game_info": {
                                "level": game.level,
                                "name": game.name,
                                "difficulty": game.difficulty,
                                "size_x": game.size_x,
                                "size_y": game.size_y,
                                "blocks_count": len(game.blocks),
                            },
                        }
                else:
                    task_manager.update_progress(task_id, 90, "❌ Order Block solver failed")
                    result = {
                        "success": False,
                        "message": "Không thể giải theo thứ tự blocks được chỉ định",
                        "order_used": order,
                    }

        else:
            result = {
                "success": False,
                "task_id": task_id,
                "mode": mode,
                "message": f"Unknown mode: {mode}",
            }

        print(f"✅ Task {task_id} wrapper solver completed")

        # Cleanup
        try:
            os.unlink(temp_path)
        except:  # noqa: E722
            pass

        return result

    except Exception as e:
        print(f"❌ Wrapper solve error: {e}")
        print(traceback.format_exc())

        # Cleanup
        try:
            os.unlink(temp_path)
        except:  # noqa: E722
            pass

        raise e
