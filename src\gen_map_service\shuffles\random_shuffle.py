import random

from src.game.game_state_base import GameStateBase

from .utils import get_valid_moves, move_object


def random_shuffle(state: GameStateBase, shuffle_steps, max_step=1):
    for _ in range(shuffle_steps):
        for block in state.blocks:
            moves = get_valid_moves(state, block, max_step=max_step)
            if moves:
                dx, dy = random.choice(moves)
                move_object(state, block, dx, dy)

    # internal_walls: List[Wall] = get_internal_wall(state)
    # for _ in range(shuffle_steps):
    #     for wall in internal_walls:
    #         moves = state.get_valid_moves_for_gen(wall, max_step=4)
    #         if moves:
    #             dx, dy = random.choice(moves)
    #             state.move_object(wall, dx, dy)
