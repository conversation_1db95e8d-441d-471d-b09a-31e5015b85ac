from typing import Dict


class Config:

    # Timeout settings
    timeout_map: Dict[str, int] = {
        "random": 300,
        "heuristic_only": 1200,
        "check_then_solve": 1800,
        "medium_path_only": 1200,
    }

    # Directory settings
    TEMP_RESULTS_DIR = "temp_results"
    TEMP_VIDEOS_DIR = "temp_videos"

    # Existing settings (if any)
    # Add other existing config variables here...


config = Config()
