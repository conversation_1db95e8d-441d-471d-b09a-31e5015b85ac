import copy
from typing import List

from src.game.const import DoorDirection, Special_Block, Special_Door
from src.game.game_state_base import Game<PERSON>tateBase
from src.game.objects import Block, Door


def is_accessible_door_precheck(state: GameStateBase, block: Block, door: Door) -> bool:
    if door.color != block.color:
        return False

    door_is_accessible = False

    if block.special == Special_Block.HEART.value:
        if door.special == Special_Door.HEART.value:
            door_is_accessible = True

    elif door.special == Special_Door.ICE.value:
        if door.turnCount - len(state.blocks) - 1 <= 0:
            door_is_accessible = True

    elif door.special == Special_Door.SHUTTER.value:
        door_is_accessible = True
    else:
        door_is_accessible = True

    return door_is_accessible

def get_accessible_doors_precheck(state: GameStateBase, block: Block) -> List[Door]:
    accessible_doors = []
    for door in state.doors:
        if is_accessible_door_precheck(state, block, door):
            accessible_doors.append(door)
    return accessible_doors

def can_cook_precheck(state: GameStateBase, block: Block) -> bool:
    occupied = set(state.doors_occupied)
    occupied.update(state.walls_occupied)

    for special_tile in state.special_tiles:
        if special_tile.special == 2 and special_tile.color != block.color:
            if (special_tile.x, special_tile.y) not in occupied:
                occupied.add((special_tile.x, special_tile.y))

    min_x = min(block.subBlocks, key=lambda cell: cell[0])[0]
    min_y = min(block.subBlocks, key=lambda cell: cell[1])[1]
    max_x = max(block.subBlocks, key=lambda cell: cell[0])[0]
    max_y = max(block.subBlocks, key=lambda cell: cell[1])[1]

    eatable = False
    list_doors = get_accessible_doors_precheck(state, block)
    for door in list_doors:
        min_door_x = min(door.subBlocks, key=lambda cell: cell[0])[0]
        min_door_y = min(door.subBlocks, key=lambda cell: cell[1])[1]
        max_door_x = max(door.subBlocks, key=lambda cell: cell[0])[0]
        max_door_y = max(door.subBlocks, key=lambda cell: cell[1])[1]

        skip = False
        if door.direction == DoorDirection.VERTICAL:
            if not (min_door_y <= min_y and max_y <= max_door_y):
                skip = True
            assert min_door_x == max_door_x
            for subBlock in block.subBlocks:
                for i in range(min(subBlock[0], min_door_x) + 1, max(subBlock[0], min_door_x)):
                    if (i, subBlock[1]) in occupied:
                        skip = True
        else:
            if not (min_door_x <= min_x and max_x <= max_door_x):
                skip = True
            assert min_door_y == max_door_y
            for subBlock in block.subBlocks:
                for i in range(min(subBlock[1], min_door_y) + 1, max(subBlock[1], min_door_y)):
                    if (subBlock[0], i) in occupied:
                        skip = True

        if not skip:
            eatable = True

        if eatable:
            break

    return eatable

def can_exit_precheck(state: GameStateBase, block: Block) -> bool:
    if block.special == Special_Block.ICE.value and block.turnCount - len(state.blocks) - 1 > 0:
        return False

    occupied = set(state.doors_occupied)
    occupied.update(state.walls_occupied)

    for special_tile in state.special_tiles:
        if special_tile.special == 2 and special_tile.color != block.color:
            if (special_tile.x, special_tile.y) not in occupied:
                occupied.add((special_tile.x, special_tile.y))

    temp_game = copy.deepcopy(state)

    if can_cook_precheck(temp_game, temp_game.find_block(block.id)):
        return True

    visited = set()
    stack = [block.subBlocks[0]]
    while stack:
        current = stack.pop()
        visited.add(current)
        (ndx, ndy) = (current[0] - block.subBlocks[0][0], current[1] - block.subBlocks[0][1])
        current_cells = [(x + ndx, y + ndy) for (x, y) in block.subBlocks]

        temp_game.move_block(temp_game.find_block(block.id), ndx, ndy)

        for dx, dy in block.directions.values():
            moved_cells = [(x + dx, y + dy) for (x, y) in current_cells]
            if moved_cells[0] in visited:
                continue

            if any(cell in occupied for cell in moved_cells):
                continue

            temp_game.move_block(temp_game.find_block(block.id), dx, dy)
            if can_cook_precheck(temp_game, temp_game.find_block(block.id)):
                return True

            temp_game.move_block(temp_game.find_block(block.id), -dx, -dy)

            stack.append(moved_cells[0])

        temp_game.move_block(temp_game.find_block(block.id), -ndx, -ndy)

    return False