import time

import streamlit as st

from frontend.handlers.genmap_result_handler import GenMapResultHandler
from frontend.handlers.task_manager import task_manager
from frontend.utils.const import MAX_WAIT
from frontend.utils.logger import logger


class TaskPoller:
    def poll_solve_task(
        self, task_id, solve_mode, order_input, result_handler, game, uploaded_file
    ):
        """Poll task giải puzzle"""
        # Hiển thị thông tin task
        order_display = ""
        if solve_mode == "order_block" and order_input and order_input.strip():
            order_display = f"🔢 Order: {order_input.strip()}"

        st.info(f"""
                🔄 Task ID: {task_id} - Mode: {solve_mode} - <PERSON><PERSON> xử lý...
                {order_display}
                """)

        # Setup progress tracking
        progress_bar = st.progress(0)
        status_text = st.empty()
        queue_text = st.empty()

        max_wait = MAX_WAIT if solve_mode != "random" else 300
        wait_time = 0

        while wait_time < max_wait:
            # <PERSON><PERSON><PERSON> tra cancel request
            if st.session_state.cancel_task:
                logger.info(f"❌ Cancel request for task {task_id}")
                try:
                    cancel_code = task_manager.cancel_task(task_id)
                    st.info(f"🚫 Đã gửi yêu cầu hủy task: {cancel_code}")
                    st.rerun()
                except Exception as e:
                    st.error(f"Lỗi hủy task: {str(e)}")
                break

            # Get task progress
            status_data, error = task_manager.get_task_progress(task_id)
            if error:
                if "không tồn tại" in error:
                    st.error(error)
                    task_manager.reset_session_state()
                    break
                else:
                    st.error(f"Lỗi lấy progress: {error}")
                    break

            if not status_data:
                st.error(f"Lỗi lấy progress: {error}")
                break

            current_status = status_data.get("status", "processing")

            # Get progress info
            if status_data.get("detailed_progress"):
                status_message = status_data.get("subprocess_message", "Đang xử lý...")
                progress = status_data.get("subprocess_progress", 0)
            else:
                status_message = status_data.get("message", "Đang xử lý...")
                progress = status_data.get("progress", 0)

            position = status_data.get("position_in_queue", 0)
            estimated_wait = status_data.get("estimated_wait_time", 0)

            if current_status == "completed":
                # Task hoàn thành
                task_manager.reset_session_state()

                result = task_manager.get_task_result(task_id)
                progress_bar.progress(100)
                status_text.success("✅ Hoàn thành!")
                queue_text.empty()

                if "error" in result:
                    st.error(f"❌ Lỗi: {result['error']}")
                elif result["success"]:
                    # Hiển thị kết quả theo mode
                    if solve_mode == "random":
                        result_handler.handle_random_result(result, game, task_id, wait_time)
                    elif solve_mode in [
                        "heuristic_only",
                        "check_then_solve",
                        "medium_path_only",
                        "order_block",
                    ]:
                        result_handler.handle_heuristic_result(
                            result, solve_mode, uploaded_file, task_id, wait_time
                        )
                else:
                    st.error(f"❌ {result.get('message', 'Không tìm thấy lời giải')}")

                # Cleanup
                task_manager.cleanup_task(task_id)
                break

            elif current_status in ["cancelled", "error"]:
                if current_status == "cancelled":
                    st.warning("⚠️ Task đã bị hủy")
                else:
                    st.error(f"❌ {status_message}")
                task_manager.reset_session_state()
                break

            else:
                # Cập nhật progress
                if current_status == "waiting":
                    progress_bar.progress(5)
                    status_text.info(f"⏳ {status_message}")
                    queue_text.info(
                        f"📍 Vị trí trong hàng chờ: {position} | ⏳ Ước tính: {estimated_wait}s"
                    )
                else:
                    progress_bar.progress(int(progress))
                    status_text.info(f"🔄 {status_message} ({wait_time}s)")
                    queue_text.empty()

                sleep_time = 2
                time.sleep(sleep_time)
                wait_time += sleep_time

        if wait_time >= max_wait:
            st.error("⏰ Timeout - Quá thời gian xử lý")
            task_manager.reset_session_state()

    def poll_gen_map_task(self, task_id, result_handler: GenMapResultHandler, uploaded_file):
        """Poll task sinh map"""
        st.info(f"🔄 Task ID: {task_id} - Gen Map")

        progress_bar = st.progress(0)
        status_text = st.empty()

        max_wait = 300
        wait_time = 0

        while wait_time < max_wait:
            # Kiểm tra cancel request
            if st.session_state.cancel_task:
                logger.info(f"❌ Cancel request for task {task_id}")
                try:
                    cancel_code = task_manager.cancel_task(task_id)
                    st.info(f"🚫 Đã gửi yêu cầu hủy task: {cancel_code}")
                    st.rerun()
                except Exception as e:
                    st.error(f"Lỗi hủy task: {str(e)}")
                break

            # Get task progress
            status_data, error = task_manager.get_task_progress(task_id)
            if error:
                if "không tồn tại" in error:
                    st.error(error)
                    task_manager.reset_session_state()
                    break
                else:
                    st.error(f"Lỗi lấy progress: {error}")
                    break

            if not status_data:
                st.error(f"Lỗi lấy progress: {error}")
                break

            current_status = status_data.get("status", "processing")

            # Get progress info
            if status_data.get("detailed_progress"):
                status_message = status_data.get("subprocess_message", "Đang xử lý...")
                progress = status_data.get("subprocess_progress", 0)
            else:
                status_message = status_data.get("message", "Đang xử lý...")
                progress = status_data.get("progress", 0)

            if current_status == "completed":
                # Task hoàn thành
                task_manager.reset_session_state()

                result = task_manager.get_task_result(task_id)
                progress_bar.progress(100)
                status_text.success("✅ Hoàn thành!")

                if "error" in result:
                    st.error(f"❌ Lỗi: {result['error']}")
                else:
                    result_handler.handle_gen_map_result(result, task_id, uploaded_file, wait_time)

                # Cleanup
                task_manager.cleanup_task(task_id)
                break

            elif current_status in ["cancelled", "error"]:
                if current_status == "cancelled":
                    st.warning("⚠️ Task đã bị hủy")
                else:
                    st.error(f"❌ {status_message}")
                task_manager.reset_session_state()
                break

            else:
                # Cập nhật progress
                if current_status == "generating":
                    progress = min(wait_time / max_wait * 70, 60)
                    icon = "🔄"
                elif current_status == "visualizing":
                    progress = min(60 + wait_time / max_wait * 30, 90)
                    icon = "🎨"
                else:
                    progress = min(wait_time / max_wait * 100, 95)
                    icon = "⏳"

                progress_bar.progress(int(progress))
                status_text.info(f"{icon} {status_message} ({wait_time}s)")

                time.sleep(2)
                wait_time += 2

        if wait_time >= max_wait:
            st.error("⏰ Timeout - Quá thời gian gen map")
            task_manager.cancel_task(task_id)
            task_manager.reset_session_state()
