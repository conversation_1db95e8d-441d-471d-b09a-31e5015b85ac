import logging
from typing import Tu<PERSON>, List, Optional
from collections import deque
from .config import ExperimentConfig
from ..game.game_state_custom import GameState

class Optimizer:
    """Utility functions for puzzle solver"""
    
    def __init__(self, config: ExperimentConfig, logger: logging.Logger):
        self.config = config
        self.logger = logger
    
    @staticmethod
    def get_first_solution(game_state : GameState, max_depth=7) -> Optional[Tuple[int, List[Tuple[int, int, int]]]]:
        """
        Tìm một đường đi đầu tiên cho bất kỳ block nào có thể vào door.
        Sử dụng BFS để tìm đường ngắn nhất.
        
        Returns:
            Tuple[block_id, move_sequence] hoặc None nếu không tìm được
        """
        # Sử dụng BFS thay vì DFS để tìm đường ngắn nhất
        queue = deque([(game_state, [])])  # (state, move_sequence)
        visited_states = set()
        
        while queue:
            current_state, move_sequence = queue.popleft()
            
            # Kiểm tra độ sâu
            if len(move_sequence) > max_depth:
                continue
                
            # Tạo hash của state hiện tại để tránh lặp
            state_hash = current_state.to_hashable()
            if state_hash in visited_states:
                continue
            visited_states.add(state_hash)
            
            # Kiểm tra tất cả blocks xem có block nào có thể thoát không
            for block in current_state.blocks:
                if current_state.can_cook_custom(block):
                    return (block.id, move_sequence)
            
            # Thử di chuyển tất cả các block
            for block in current_state.blocks:
                # Bỏ qua các block không thể di chuyển (sử dụng đúng tên thuộc tính)
                if block.special == "ICE" and block.turnCount > 0:
                    continue
                if block.special == "LOCK" and len(current_state.key_blocks) > 0:
                    continue
                    
                # Lấy tất cả các di chuyển hợp lệ cho block này
                valid_moves = current_state.get_valid_moves_hung(block, max_step=3)
                
                for dx, dy in valid_moves:
                    # Tạo bản sao của state để thử di chuyển
                    new_state = current_state.copy()
                    new_block = new_state.find_block(block.id)
                    
                    if new_block and new_state.move_block(new_block, dx, dy):
                        new_move_sequence = move_sequence + [(block.id, dx, dy)]
                        
                        # Nếu block vừa di chuyển có thể ăn được, thử ăn nó
                        if new_state.can_exit_dfs(new_block):
                            # Tạo state sau khi ăn block
                            eat_state = new_state.copy()
                            eat_block = eat_state.find_block(block.id)
                            if eat_block:
                                eat_state.eat_block(eat_block)
                                eat_move_sequence = new_move_sequence + [(-block.id, 0, 0)]
                                queue.append((eat_state, eat_move_sequence))
                        
                        # Thêm state mới vào queue
                        queue.append((new_state, new_move_sequence))
        
        return None