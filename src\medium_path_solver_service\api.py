from datetime import datetime
import gc

from src.game.game_state_medium_path import GameState
from src.heuristic.block_based_heuristic import BaseBlockHeuristic
from src.heuristic.clean_cost_heuristic import CleanCostHeuristic

from .config import MediumPathSolverConfig, ShortestPathSolverConfig
from .solver_medium import MediumPathSolver
from .solver_shortest import ShortestPathSolver
from .utils import clean_path, save_images, save_logs


def solve_medium(state_path: str):
    config = MediumPathSolverConfig()

    solver = MediumPathSolver(config)
    initial_state = GameState(state_path)
    heuristic = BaseBlockHeuristic()

    solution = clean_path(solver.solve(initial_state, heuristic))
    return solution


def solve(state_path: str):
    initial_state = GameState(state_path)
    if initial_state is None:
        print("Failed to load game state.")
        return []

    # initial_state.visualize_state()
    heuristic = CleanCostHeuristic(game=initial_state)
    config = ShortestPathSolverConfig()
    solver = ShortestPathSolver(config=config)

    start_time = datetime.now()

    path, current_state, visited, iteration = solver.solve_custom_base_on_unique_block(
        state=initial_state,
        check_every=config.check_every,
        patience=config.patience,
        max_iter=config.max_iteration,
        heuristic=heuristic,
    )

    end_time = datetime.now()
    elapsed = end_time - start_time

    found = path is not None

    if config.save_logs:
        save_logs(
            file=state_path,
            path=path,
            start_time=start_time,
            end_time=end_time,
            iteration=iteration,
            found=found,
            config=config,
        )

    if found and config.save_images:
        save_images(game=initial_state, path=path, config=config)

    status_msg = (
        f"✅ Solution found for {state_path} in {elapsed.total_seconds():.2f}s with {iteration} iterations."
        if found
        else f"❌ No solution found for {state_path} after {iteration} iterations."
    )
    print(status_msg)

    # Delete variables
    variables_to_delete = [
        "game",
        "path",
        "current_state",
        "visited",
        "iteration",
        "start_time",
        "end_time",
        "elapsed",
        "found",
        "heuristic",
        "config",
    ]

    for var in variables_to_delete:
        if var in locals():
            del locals()[var]

    gc.collect()
    return clean_path(path)
