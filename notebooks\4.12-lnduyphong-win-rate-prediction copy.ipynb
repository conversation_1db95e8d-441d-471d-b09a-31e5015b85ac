{"cells": [{"cell_type": "code", "execution_count": 1, "id": "91a3d7f3", "metadata": {}, "outputs": [], "source": ["import copy\n", "import json\n", "import os\n", "import sys\n", "import warnings\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "import seaborn as sns\n", "from tqdm import tqdm\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "sys.path.append(os.path.abspath(\"..\"))\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 2, "id": "e3ee645e", "metadata": {}, "outputs": [], "source": ["from src.game.game_state_base import GameStateBase\n", "from src.map_exploration_service.train import train_all_models, train_single_model"]}, {"cell_type": "code", "execution_count": 3, "id": "3ec549eb", "metadata": {}, "outputs": [], "source": ["FEATURE_COLUMNS = [\n", "'avg_digest_block_move_avg', 'std_digest_block_move_avg',\n", "'avg_digest_block_move_std', 'std_digest_block_move_std',\n", "'avg_digest_block_move_min', 'avg_digest_block_move_max',\n", "'avg_runtime_sec', 'med_runtime_sec', 'std_runtime_sec',\n", "'min_runtime_sec', 'max_runtime_sec', 'avg_iteration', 'med_iteration',\n", "'std_iteration', 'min_iteration', 'max_iteration', 'avg_steps',\n", "'med_steps', 'std_steps', 'min_steps', 'max_steps',\n", "\n", "'time_limit', 'num_blocks','early_edible_blocks', 'num_tile_path', 'num_unique_colors',\n", "'obstacles', 'num_space', 'num_normal_blocks', 'num_vector_blocks',\n", "'num_layer_blocks', 'num_heart_blocks', 'num_ice_blocks',\n", "'num_bomb_blocks', 'num_lock_blocks', 'num_doors', 'num_normal_doors',\n", "'num_shutter_doors', 'num_heart_doors', 'num_ice_doors'\n", "]\n", "\n", "LABEL_COLUMN = 'win_rate'"]}, {"cell_type": "code", "execution_count": 4, "id": "0e0fe62b", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['level', 'avg_digest_block_move_avg', 'std_digest_block_move_avg',\n", "       'avg_digest_block_move_std', 'std_digest_block_move_std',\n", "       'avg_digest_block_move_min', 'avg_digest_block_move_max',\n", "       'avg_runtime_sec', 'med_runtime_sec', 'std_runtime_sec',\n", "       'min_runtime_sec', 'max_runtime_sec', 'avg_iteration', 'med_iteration',\n", "       'std_iteration', 'min_iteration', 'max_iteration', 'avg_steps',\n", "       'med_steps', 'std_steps', 'min_steps', 'max_steps', 'win_rate',\n", "       'use_booster_count_avg', 'time_limit', 'num_blocks',\n", "       'early_edible_blocks', 'num_tile_path', 'num_unique_colors',\n", "       'obstacles', 'num_space', 'num_normal_blocks', 'num_vector_blocks',\n", "       'num_layer_blocks', 'num_heart_blocks', 'num_ice_blocks',\n", "       'num_bomb_blocks', 'num_lock_blocks', 'num_doors', 'num_normal_doors',\n", "       'num_shutter_doors', 'num_heart_doors', 'num_ice_doors'],\n", "      dtype='object')"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["game_data = pd.read_csv('final_data.csv')\n", "game_data.columns"]}, {"cell_type": "code", "execution_count": 5, "id": "4fa1764f", "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split"]}, {"cell_type": "code", "execution_count": 31, "id": "f6ff5778", "metadata": {}, "outputs": [], "source": ["X = game_data[FEATURE_COLUMNS].values\n", "y = game_data[LABEL_COLUMN].values.reshape(-1, 1)\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.1, random_state=1, shuffle=True\n", ")"]}, {"cell_type": "code", "execution_count": 32, "id": "3b659090", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[I 2025-08-21 13:09:15,455] A new study created in memory with name: no-name-a69a26f2-aaca-4992-a3e7-025bf5a5f346\n", "[I 2025-08-21 13:09:16,157] Trial 0 finished with value: 0.7533957442662451 and parameters: {'lambda_l1': 0.005404757800383064, 'lambda_l2': 0.0002772726207255073, 'num_leaves': 107, 'feature_fraction': 0.7967265369062094, 'bagging_fraction': 0.690344664805825, 'bagging_freq': 5, 'min_child_samples': 72, 'max_depth': 4, 'learning_rate': 0.0979054030900086, 'subsample': 0.5908194368869486, 'n_estimators': 992}. Best is trial 0 with value: 0.7533957442662451.\n", "[I 2025-08-21 13:09:16,337] Trial 1 finished with value: 0.7232734755648226 and parameters: {'lambda_l1': 7.29506787863666, 'lambda_l2': 0.15097503474692106, 'num_leaves': 178, 'feature_fraction': 0.6492116276296291, 'bagging_fraction': 0.6447557607690948, 'bagging_freq': 3, 'min_child_samples': 91, 'max_depth': 8, 'learning_rate': 0.053705723632925674, 'subsample': 0.5336512154688258, 'n_estimators': 321}. Best is trial 0 with value: 0.7533957442662451.\n", "[I 2025-08-21 13:09:16,995] Trial 2 finished with value: 0.7693580188493074 and parameters: {'lambda_l1': 0.01357519261462045, 'lambda_l2': 0.10495608386931184, 'num_leaves': 85, 'feature_fraction': 0.909849413835458, 'bagging_fraction': 0.8429470786613382, 'bagging_freq': 5, 'min_child_samples': 69, 'max_depth': 4, 'learning_rate': 0.05996496738317342, 'subsample': 0.5011050534224873, 'n_estimators': 851}. Best is trial 2 with value: 0.7693580188493074.\n", "[I 2025-08-21 13:09:17,342] Trial 3 finished with value: 0.6784163060861879 and parameters: {'lambda_l1': 9.817784397442212, 'lambda_l2': 0.0002343336755330862, 'num_leaves': 225, 'feature_fraction': 0.914954194132423, 'bagging_fraction': 0.5514581700587682, 'bagging_freq': 4, 'min_child_samples': 73, 'max_depth': 4, 'learning_rate': 0.04739998397343134, 'subsample': 0.8792323587908005, 'n_estimators': 620}. Best is trial 2 with value: 0.7693580188493074.\n", "[I 2025-08-21 13:09:17,903] Trial 4 finished with value: 0.7670516193241437 and parameters: {'lambda_l1': 0.02941702069843728, 'lambda_l2': 0.006239881959384278, 'num_leaves': 183, 'feature_fraction': 0.6802085721053264, 'bagging_fraction': 0.7515425244508749, 'bagging_freq': 1, 'min_child_samples': 73, 'max_depth': 5, 'learning_rate': 0.06443421326838705, 'subsample': 0.7549799301756144, 'n_estimators': 737}. Best is trial 2 with value: 0.7693580188493074.\n", "[I 2025-08-21 13:09:18,291] Trial 5 finished with value: 0.7754894434245372 and parameters: {'lambda_l1': 0.03530318071557456, 'lambda_l2': 1.0275159846737308e-08, 'num_leaves': 253, 'feature_fraction': 0.56692942755789, 'bagging_fraction': 0.6876520918810907, 'bagging_freq': 5, 'min_child_samples': 36, 'max_depth': 0, 'learning_rate': 0.06840485471424157, 'subsample': 0.6257713285078454, 'n_estimators': 312}. Best is trial 5 with value: 0.7754894434245372.\n", "[I 2025-08-21 13:09:18,986] Trial 6 finished with value: 0.776871589755119 and parameters: {'lambda_l1': 2.449546290487483e-06, 'lambda_l2': 0.0017158473930281586, 'num_leaves': 92, 'feature_fraction': 0.9598098395772757, 'bagging_fraction': 0.6612020326030388, 'bagging_freq': 6, 'min_child_samples': 70, 'max_depth': 9, 'learning_rate': 0.027825819958386323, 'subsample': 0.9761946467378171, 'n_estimators': 980}. Best is trial 6 with value: 0.776871589755119.\n", "[I 2025-08-21 13:09:19,384] Trial 7 finished with value: 0.6299681467299119 and parameters: {'lambda_l1': 9.432166586055112, 'lambda_l2': 2.7700396269003775e-06, 'num_leaves': 244, 'feature_fraction': 0.5272379629623105, 'bagging_fraction': 0.4280341520756199, 'bagging_freq': 1, 'min_child_samples': 68, 'max_depth': 1, 'learning_rate': 0.030176947159663157, 'subsample': 0.7825235134940673, 'n_estimators': 897}. Best is trial 6 with value: 0.776871589755119.\n", "[I 2025-08-21 13:09:19,615] Trial 8 finished with value: 0.7675509177793789 and parameters: {'lambda_l1': 0.0001549828198919148, 'lambda_l2': 0.014363749851577434, 'num_leaves': 159, 'feature_fraction': 0.591323773854557, 'bagging_fraction': 0.818459927449023, 'bagging_freq': 4, 'min_child_samples': 75, 'max_depth': 1, 'learning_rate': 0.04044537466417796, 'subsample': 0.8285465946222157, 'n_estimators': 508}. Best is trial 6 with value: 0.776871589755119.\n", "[I 2025-08-21 13:09:20,000] Trial 9 finished with value: 0.7308256709334378 and parameters: {'lambda_l1': 7.936894936339204, 'lambda_l2': 0.9052156338569995, 'num_leaves': 199, 'feature_fraction': 0.8994495905691309, 'bagging_fraction': 0.8773227264194189, 'bagging_freq': 3, 'min_child_samples': 86, 'max_depth': 0, 'learning_rate': 0.01669700259037838, 'subsample': 0.9371223261252026, 'n_estimators': 733}. Best is trial 6 with value: 0.776871589755119.\n", "[I 2025-08-21 13:09:20,317] Trial 10 finished with value: 0.7025970033807738 and parameters: {'lambda_l1': 5.153027102629082e-08, 'lambda_l2': 1.1416550128699017e-06, 'num_leaves': 11, 'feature_fraction': 0.41997400004097496, 'bagging_fraction': 0.9691967787277374, 'bagging_freq': 7, 'min_child_samples': 11, 'max_depth': 12, 'learning_rate': 0.007990836387929284, 'subsample': 0.9902472708445771, 'n_estimators': 167}. Best is trial 6 with value: 0.776871589755119.\n", "[I 2025-08-21 13:09:20,695] Trial 11 finished with value: 0.7671260787543137 and parameters: {'lambda_l1': 2.185052953502644e-06, 'lambda_l2': 1.5835764395517784e-08, 'num_leaves': 62, 'feature_fraction': 0.775286274925746, 'bagging_fraction': 0.574263930916501, 'bagging_freq': 7, 'min_child_samples': 37, 'max_depth': 9, 'learning_rate': 0.07832843424259311, 'subsample': 0.6641358115046725, 'n_estimators': 395}. Best is trial 6 with value: 0.776871589755119.\n", "[I 2025-08-21 13:09:20,955] Trial 12 finished with value: 0.7756147682185748 and parameters: {'lambda_l1': 4.874299738528139e-05, 'lambda_l2': 8.330433891067613e-06, 'num_leaves': 130, 'feature_fraction': 0.9972884322767769, 'bagging_fraction': 0.5918387887427943, 'bagging_freq': 6, 'min_child_samples': 43, 'max_depth': 8, 'learning_rate': 0.07729368895452085, 'subsample': 0.6685022033167807, 'n_estimators': 219}. Best is trial 6 with value: 0.776871589755119.\n", "[I 2025-08-21 13:09:21,098] Trial 13 finished with value: 0.7759200124072863 and parameters: {'lambda_l1': 1.0647471756714303e-05, 'lambda_l2': 1.2651295574379548e-05, 'num_leaves': 131, 'feature_fraction': 0.9966774190730116, 'bagging_fraction': 0.48424711558209343, 'bagging_freq': 6, 'min_child_samples': 48, 'max_depth': 8, 'learning_rate': 0.08537586059945003, 'subsample': 0.6926429345443993, 'n_estimators': 114}. Best is trial 6 with value: 0.776871589755119.\n", "[I 2025-08-21 13:09:21,214] Trial 14 finished with value: 0.7681771789987979 and parameters: {'lambda_l1': 1.4617543219317165e-06, 'lambda_l2': 3.269476177657301e-05, 'num_leaves': 52, 'feature_fraction': 0.8058006570657121, 'bagging_fraction': 0.41672396485152285, 'bagging_freq': 6, 'min_child_samples': 54, 'max_depth': 11, 'learning_rate': 0.0981385921004731, 'subsample': 0.7088381383173458, 'n_estimators': 106}. Best is trial 6 with value: 0.776871589755119.\n", "[I 2025-08-21 13:09:21,595] Trial 15 finished with value: 0.7813206136944173 and parameters: {'lambda_l1': 2.7618367963076697e-08, 'lambda_l2': 9.510478782368338, 'num_leaves': 137, 'feature_fraction': 0.9964816245260241, 'bagging_fraction': 0.49965515186696263, 'bagging_freq': 6, 'min_child_samples': 54, 'max_depth': 10, 'learning_rate': 0.03381464548560768, 'subsample': 0.8602427629097296, 'n_estimators': 539}. Best is trial 15 with value: 0.7813206136944173.\n", "[I 2025-08-21 13:09:22,287] Trial 16 finished with value: 0.7855229730274897 and parameters: {'lambda_l1': 1.4025856746031417e-08, 'lambda_l2': 7.865454338453911, 'num_leaves': 98, 'feature_fraction': 0.891620182942934, 'bagging_fraction': 0.5067274046136736, 'bagging_freq': 7, 'min_child_samples': 20, 'max_depth': 10, 'learning_rate': 0.030446908637817197, 'subsample': 0.9063525716802624, 'n_estimators': 540}. Best is trial 16 with value: 0.7855229730274897.\n", "[I 2025-08-21 13:09:23,159] Trial 17 finished with value: 0.7877099753730219 and parameters: {'lambda_l1': 1.0209568637794928e-08, 'lambda_l2': 6.384091300844452, 'num_leaves': 151, 'feature_fraction': 0.8459726093598519, 'bagging_fraction': 0.5164807692074176, 'bagging_freq': 7, 'min_child_samples': 15, 'max_depth': 11, 'learning_rate': 0.030867436942611844, 'subsample': 0.8846101174978663, 'n_estimators': 529}. Best is trial 17 with value: 0.7877099753730219.\n", "[I 2025-08-21 13:09:24,162] Trial 18 finished with value: 0.7993239738619382 and parameters: {'lambda_l1': 1.8638466273446044e-07, 'lambda_l2': 8.676660851425613, 'num_leaves': 28, 'feature_fraction': 0.8336147297884177, 'bagging_fraction': 0.522773707914801, 'bagging_freq': 7, 'min_child_samples': 7, 'max_depth': 12, 'learning_rate': 0.018677505359387975, 'subsample': 0.9220024885015041, 'n_estimators': 426}. Best is trial 18 with value: 0.7993239738619382.\n", "[I 2025-08-21 13:09:24,758] Trial 19 finished with value: 0.8068285937163093 and parameters: {'lambda_l1': 1.9382570185454126e-07, 'lambda_l2': 0.6970110146877168, 'num_leaves': 12, 'feature_fraction': 0.7547354288826225, 'bagging_fraction': 0.594848467666505, 'bagging_freq': 7, 'min_child_samples': 9, 'max_depth': 12, 'learning_rate': 0.01692313807081298, 'subsample': 0.8128271249518288, 'n_estimators': 426}. Best is trial 19 with value: 0.8068285937163093.\n", "[I 2025-08-21 13:09:25,416] Trial 20 finished with value: 0.8067772993829507 and parameters: {'lambda_l1': 1.5048578691657608e-07, 'lambda_l2': 0.5444700148874833, 'num_leaves': 12, 'feature_fraction': 0.7288437320149764, 'bagging_fraction': 0.6114814136609358, 'bagging_freq': 3, 'min_child_samples': 5, 'max_depth': 12, 'learning_rate': 0.015643030484608446, 'subsample': 0.7985978177602119, 'n_estimators': 420}. Best is trial 19 with value: 0.8068285937163093.\n", "[I 2025-08-21 13:09:25,984] Trial 21 finished with value: 0.7963362549569475 and parameters: {'lambda_l1': 2.4078651113865805e-07, 'lambda_l2': 0.5539310427722149, 'num_leaves': 12, 'feature_fraction': 0.7490780166295726, 'bagging_fraction': 0.6211611082685297, 'bagging_freq': 2, 'min_child_samples': 24, 'max_depth': 12, 'learning_rate': 0.017452630286121776, 'subsample': 0.8072032414979554, 'n_estimators': 418}. Best is trial 19 with value: 0.8068285937163093.\n", "[I 2025-08-21 13:09:27,198] Trial 22 finished with value: 0.7690843401353658 and parameters: {'lambda_l1': 2.9306667414005485e-07, 'lambda_l2': 0.9985752155073202, 'num_leaves': 33, 'feature_fraction': 0.7155678620506979, 'bagging_fraction': 0.7540246352551391, 'bagging_freq': 3, 'min_child_samples': 6, 'max_depth': 12, 'learning_rate': 0.005133364491684087, 'subsample': 0.7482134360648108, 'n_estimators': 411}. Best is trial 19 with value: 0.8068285937163093.\n", "[I 2025-08-21 13:09:27,579] Trial 23 finished with value: 0.7942856662080109 and parameters: {'lambda_l1': 3.47269229509422e-07, 'lambda_l2': 0.04544307497747078, 'num_leaves': 40, 'feature_fraction': 0.8385609852037716, 'bagging_fraction': 0.6123600565097576, 'bagging_freq': 2, 'min_child_samples': 27, 'max_depth': 6, 'learning_rate': 0.018195235449393856, 'subsample': 0.8383881750075752, 'n_estimators': 285}. Best is trial 19 with value: 0.8068285937163093.\n", "[I 2025-08-21 13:09:29,048] Trial 24 finished with value: 0.7958308603027549 and parameters: {'lambda_l1': 1.7503224111480938e-05, 'lambda_l2': 1.0683437534170304, 'num_leaves': 26, 'feature_fraction': 0.7211552108690474, 'bagging_fraction': 0.45743001018775387, 'bagging_freq': 4, 'min_child_samples': 6, 'max_depth': 11, 'learning_rate': 0.021821250347599026, 'subsample': 0.9208331195085977, 'n_estimators': 616}. Best is trial 19 with value: 0.8068285937163093.\n", "[I 2025-08-21 13:09:29,744] Trial 25 finished with value: 0.7976775026686814 and parameters: {'lambda_l1': 1.0386682231048133e-07, 'lambda_l2': 0.01809744768960093, 'num_leaves': 69, 'feature_fraction': 0.6430186024494452, 'bagging_fraction': 0.5480173912308136, 'bagging_freq': 5, 'min_child_samples': 17, 'max_depth': 10, 'learning_rate': 0.011056203869314565, 'subsample': 0.7665195775149414, 'n_estimators': 452}. Best is trial 19 with value: 0.8068285937163093.\n", "[I 2025-08-21 13:09:30,242] Trial 26 finished with value: 0.7872233395362724 and parameters: {'lambda_l1': 1.0765116224155954e-06, 'lambda_l2': 0.22277897810210065, 'num_leaves': 50, 'feature_fraction': 0.8372637620236868, 'bagging_fraction': 0.7482223195657661, 'bagging_freq': 2, 'min_child_samples': 28, 'max_depth': 7, 'learning_rate': 0.04129016777116104, 'subsample': 0.954016539726477, 'n_estimators': 361}. Best is trial 19 with value: 0.8068285937163093.\n", "[I 2025-08-21 13:09:30,836] Trial 27 finished with value: 0.7992551912549206 and parameters: {'lambda_l1': 0.0009470007474207701, 'lambda_l2': 0.0013780861509291142, 'num_leaves': 24, 'feature_fraction': 0.7523538092168351, 'bagging_fraction': 0.5552199556955281, 'bagging_freq': 7, 'min_child_samples': 5, 'max_depth': 12, 'learning_rate': 0.0234309654680361, 'subsample': 0.8106606514162188, 'n_estimators': 242}. Best is trial 19 with value: 0.8068285937163093.\n", "[I 2025-08-21 13:09:31,580] Trial 28 finished with value: 0.8080477694184317 and parameters: {'lambda_l1': 9.154069409507402e-06, 'lambda_l2': 2.722061664911648, 'num_leaves': 10, 'feature_fraction': 0.668590508910572, 'bagging_fraction': 0.6391557957720552, 'bagging_freq': 4, 'min_child_samples': 13, 'max_depth': 11, 'learning_rate': 0.012478489333621712, 'subsample': 0.725612062082545, 'n_estimators': 623}. Best is trial 28 with value: 0.8080477694184317.\n", "[I 2025-08-21 13:09:32,434] Trial 29 finished with value: 0.8052106025065469 and parameters: {'lambda_l1': 7.904576929110589e-06, 'lambda_l2': 1.3244625684466862, 'num_leaves': 11, 'feature_fraction': 0.6126342361262714, 'bagging_fraction': 0.7194155513492531, 'bagging_freq': 3, 'min_child_samples': 15, 'max_depth': 9, 'learning_rate': 0.010993946076773089, 'subsample': 0.7315256378628833, 'n_estimators': 640}. Best is trial 28 with value: 0.8080477694184317.\n", "[I 2025-08-21 13:09:33,187] Trial 30 finished with value: 0.7770071583498999 and parameters: {'lambda_l1': 0.0012273230539948839, 'lambda_l2': 0.06538868038431923, 'num_leaves': 74, 'feature_fraction': 0.48388675100228495, 'bagging_fraction': 0.6813523908676679, 'bagging_freq': 4, 'min_child_samples': 32, 'max_depth': 11, 'learning_rate': 0.03833094994224376, 'subsample': 0.6063553185429167, 'n_estimators': 683}. Best is trial 28 with value: 0.8080477694184317.\n", "[I 2025-08-21 13:09:33,843] Trial 31 finished with value: 0.8082922707787293 and parameters: {'lambda_l1': 1.209461302982968e-05, 'lambda_l2': 2.2410631828177343, 'num_leaves': 8, 'feature_fraction': 0.6095353470016823, 'bagging_fraction': 0.7131684625939216, 'bagging_freq': 3, 'min_child_samples': 15, 'max_depth': 9, 'learning_rate': 0.013792945999559478, 'subsample': 0.7251106102217528, 'n_estimators': 628}. Best is trial 31 with value: 0.8082922707787293.\n", "[I 2025-08-21 13:09:34,925] Trial 32 finished with value: 0.7984696549924918 and parameters: {'lambda_l1': 9.955618331633616e-05, 'lambda_l2': 0.18409424966593357, 'num_leaves': 43, 'feature_fraction': 0.6734060647149921, 'bagging_fraction': 0.634958313092442, 'bagging_freq': 3, 'min_child_samples': 12, 'max_depth': 10, 'learning_rate': 0.012189023026950754, 'subsample': 0.7169813434691213, 'n_estimators': 499}. Best is trial 31 with value: 0.8082922707787293.\n", "[I 2025-08-21 13:09:35,604] Trial 33 finished with value: 0.8016249676594832 and parameters: {'lambda_l1': 3.421369191778082e-05, 'lambda_l2': 2.978409936661047, 'num_leaves': 10, 'feature_fraction': 0.6371818731450725, 'bagging_fraction': 0.7934045390381663, 'bagging_freq': 3, 'min_child_samples': 21, 'max_depth': 11, 'learning_rate': 0.024913460357864306, 'subsample': 0.7867007370112089, 'n_estimators': 590}. Best is trial 31 with value: 0.8082922707787293.\n", "[I 2025-08-21 13:09:36,884] Trial 34 finished with value: 0.787761445904627 and parameters: {'lambda_l1': 5.249043648064021e-06, 'lambda_l2': 0.43061360974471585, 'num_leaves': 27, 'feature_fraction': 0.5447100445855201, 'bagging_fraction': 0.7117531471753789, 'bagging_freq': 2, 'min_child_samples': 11, 'max_depth': 7, 'learning_rate': 0.048168515919241726, 'subsample': 0.6529742376040675, 'n_estimators': 762}. Best is trial 31 with value: 0.8082922707787293.\n", "[I 2025-08-21 13:09:37,383] Trial 35 finished with value: 0.7992474872235665 and parameters: {'lambda_l1': 0.9416897594413361, 'lambda_l2': 2.526044302428236, 'num_leaves': 54, 'feature_fraction': 0.6850171846329881, 'bagging_fraction': 0.6605156708043238, 'bagging_freq': 5, 'min_child_samples': 21, 'max_depth': 3, 'learning_rate': 0.014939993502592884, 'subsample': 0.5617681468969287, 'n_estimators': 678}. Best is trial 31 with value: 0.8082922707787293.\n", "[I 2025-08-21 13:09:37,896] Trial 36 finished with value: 0.7863772089762331 and parameters: {'lambda_l1': 6.469653122558942e-07, 'lambda_l2': 0.07323933835017521, 'num_leaves': 79, 'feature_fraction': 0.7345067337223952, 'bagging_fraction': 0.5893876512775251, 'bagging_freq': 4, 'min_child_samples': 30, 'max_depth': 9, 'learning_rate': 0.0073798206401438846, 'subsample': 0.7434643550636256, 'n_estimators': 465}. Best is trial 31 with value: 0.8082922707787293.\n", "[I 2025-08-21 13:09:38,301] Trial 37 finished with value: 0.764364874985674 and parameters: {'lambda_l1': 0.0003372179332097603, 'lambda_l2': 0.23978791592822885, 'num_leaves': 117, 'feature_fraction': 0.6048936472291234, 'bagging_fraction': 0.6553083853538875, 'bagging_freq': 4, 'min_child_samples': 97, 'max_depth': 10, 'learning_rate': 0.053819324996041217, 'subsample': 0.6888318358321914, 'n_estimators': 676}. Best is trial 31 with value: 0.8082922707787293.\n", "[I 2025-08-21 13:09:38,906] Trial 38 finished with value: 0.7852236042224899 and parameters: {'lambda_l1': 3.348776272400117e-06, 'lambda_l2': 0.024520899700226646, 'num_leaves': 39, 'feature_fraction': 0.7917329927471758, 'bagging_fraction': 0.8893307317907342, 'bagging_freq': 4, 'min_child_samples': 62, 'max_depth': 11, 'learning_rate': 0.025009054527307, 'subsample': 0.7765805526333202, 'n_estimators': 577}. Best is trial 31 with value: 0.8082922707787293.\n", "[I 2025-08-21 13:09:39,709] Trial 39 finished with value: 0.7731973447336176 and parameters: {'lambda_l1': 4.0421431613883936e-08, 'lambda_l2': 2.715766009002989, 'num_leaves': 20, 'feature_fraction': 0.6623726532384702, 'bagging_fraction': 0.6869106104611895, 'bagging_freq': 2, 'min_child_samples': 39, 'max_depth': 12, 'learning_rate': 0.03400599254434802, 'subsample': 0.8507908832578951, 'n_estimators': 801}. Best is trial 31 with value: 0.8082922707787293.\n", "[I 2025-08-21 13:09:40,452] Trial 40 finished with value: 0.7903114014688798 and parameters: {'lambda_l1': 0.005943342317057979, 'lambda_l2': 0.0022438540768031936, 'num_leaves': 58, 'feature_fraction': 0.4971093834976156, 'bagging_fraction': 0.7869778018607932, 'bagging_freq': 5, 'min_child_samples': 12, 'max_depth': 7, 'learning_rate': 0.012900635765396865, 'subsample': 0.6267005065959574, 'n_estimators': 365}. Best is trial 31 with value: 0.8082922707787293.\n", "[I 2025-08-21 13:09:41,117] Trial 41 finished with value: 0.8066130495608566 and parameters: {'lambda_l1': 6.716671198893957e-06, 'lambda_l2': 1.7643514948377463, 'num_leaves': 8, 'feature_fraction': 0.609520105335922, 'bagging_fraction': 0.7165589340127441, 'bagging_freq': 3, 'min_child_samples': 15, 'max_depth': 9, 'learning_rate': 0.009736812083047205, 'subsample': 0.728848860087429, 'n_estimators': 647}. Best is trial 31 with value: 0.8082922707787293.\n", "[I 2025-08-21 13:09:41,621] Trial 42 finished with value: 0.8082020035928327 and parameters: {'lambda_l1': 2.375069625541729e-05, 'lambda_l2': 2.359387775372228, 'num_leaves': 8, 'feature_fraction': 0.6925810678102431, 'bagging_fraction': 0.7369089288555701, 'bagging_freq': 3, 'min_child_samples': 18, 'max_depth': 8, 'learning_rate': 0.021315868213121723, 'subsample': 0.8056039074044526, 'n_estimators': 473}. Best is trial 31 with value: 0.8082922707787293.\n", "[I 2025-08-21 13:09:42,329] Trial 43 finished with value: 0.7987951070038599 and parameters: {'lambda_l1': 2.7257146316453054e-05, 'lambda_l2': 0.43106352122080926, 'num_leaves': 40, 'feature_fraction': 0.7000641483156845, 'bagging_fraction': 0.6151906986717494, 'bagging_freq': 3, 'min_child_samples': 19, 'max_depth': 8, 'learning_rate': 0.02035851459604051, 'subsample': 0.8115062002925257, 'n_estimators': 483}. Best is trial 31 with value: 0.8082922707787293.\n", "[I 2025-08-21 13:09:42,816] Trial 44 finished with value: 0.7477139269066596 and parameters: {'lambda_l1': 0.00020869507497940973, 'lambda_l2': 0.00678584604238902, 'num_leaves': 20, 'feature_fraction': 0.5671188411768545, 'bagging_fraction': 0.744807745379926, 'bagging_freq': 4, 'min_child_samples': 23, 'max_depth': 5, 'learning_rate': 0.005202913971063157, 'subsample': 0.7970210567889454, 'n_estimators': 346}. Best is trial 31 with value: 0.8082922707787293.\n", "[I 2025-08-21 13:09:44,568] Trial 45 finished with value: 0.7916247451757801 and parameters: {'lambda_l1': 9.156658218000553e-08, 'lambda_l2': 1.364399685561116e-07, 'num_leaves': 187, 'feature_fraction': 0.7650131152770625, 'bagging_fraction': 0.8390801683293196, 'bagging_freq': 1, 'min_child_samples': 9, 'max_depth': 10, 'learning_rate': 0.022384627437816586, 'subsample': 0.7668628143836806, 'n_estimators': 571}. Best is trial 31 with value: 0.8082922707787293.\n", "[I 2025-08-21 13:09:44,941] Trial 46 finished with value: 0.7864325022573623 and parameters: {'lambda_l1': 8.161812200400115e-07, 'lambda_l2': 0.12205771766716238, 'num_leaves': 227, 'feature_fraction': 0.6335361778284154, 'bagging_fraction': 0.6771837033058103, 'bagging_freq': 3, 'min_child_samples': 34, 'max_depth': 6, 'learning_rate': 0.01481773038812065, 'subsample': 0.8871192922382474, 'n_estimators': 288}. Best is trial 31 with value: 0.8082922707787293.\n", "[I 2025-08-21 13:09:45,418] Trial 47 finished with value: 0.7727944151569817 and parameters: {'lambda_l1': 6.730175637794645e-05, 'lambda_l2': 3.122738260661117, 'num_leaves': 21, 'feature_fraction': 0.6933040427587474, 'bagging_fraction': 0.5771393224366698, 'bagging_freq': 4, 'min_child_samples': 79, 'max_depth': 8, 'learning_rate': 0.027098799160851263, 'subsample': 0.8385490607223178, 'n_estimators': 854}. Best is trial 31 with value: 0.8082922707787293.\n", "[I 2025-08-21 13:09:46,051] Trial 48 finished with value: 0.7749514528159362 and parameters: {'lambda_l1': 0.00048166715248519826, 'lambda_l2': 0.4890231341493577, 'num_leaves': 35, 'feature_fraction': 0.5757176179542863, 'bagging_fraction': 0.639826481618497, 'bagging_freq': 3, 'min_child_samples': 24, 'max_depth': 11, 'learning_rate': 0.06256277898624694, 'subsample': 0.8230527418670974, 'n_estimators': 462}. Best is trial 31 with value: 0.8082922707787293.\n", "[I 2025-08-21 13:09:46,491] Trial 49 finished with value: 0.8063431225280556 and parameters: {'lambda_l1': 1.583957176561482e-05, 'lambda_l2': 4.761034834135234e-05, 'num_leaves': 65, 'feature_fraction': 0.7863906964650684, 'bagging_fraction': 0.7819413739437926, 'bagging_freq': 2, 'min_child_samples': 11, 'max_depth': 3, 'learning_rate': 0.03619881904415903, 'subsample': 0.8660424671338907, 'n_estimators': 521}. Best is trial 31 with value: 0.8082922707787293.\n", "[I 2025-08-21 13:09:46,620] A new study created in memory with name: no-name-f9d3e460-27c4-41fd-9603-f3744cb68faa\n", "[I 2025-08-21 13:09:49,365] Trial 0 finished with value: 0.7931331474577547 and parameters: {'lambda': 0.0034478767514035426, 'alpha': 2.737641361597271e-05, 'max_depth': 9, 'learning_rate': 0.022026896599999447, 'n_estimators': 571, 'subsample': 0.66873348898952, 'colsample_bytree': 0.9904944149872652, 'min_child_weight': 18}. Best is trial 0 with value: 0.7931331474577547.\n", "[I 2025-08-21 13:09:50,486] Trial 1 finished with value: 0.7780639439907064 and parameters: {'lambda': 2.886576094281132e-07, 'alpha': 0.1697195789092284, 'max_depth': 10, 'learning_rate': 0.10744856594290933, 'n_estimators': 192, 'subsample': 0.9334190633308166, 'colsample_bytree': 0.5563368834645877, 'min_child_weight': 12}. Best is trial 0 with value: 0.7931331474577547.\n", "[I 2025-08-21 13:09:57,370] Trial 2 finished with value: 0.7869964610301086 and parameters: {'lambda': 0.021578179223640703, 'alpha': 0.02175012466023401, 'max_depth': 12, 'learning_rate': 0.016217300635976692, 'n_estimators': 797, 'subsample': 0.6985522828670793, 'colsample_bytree': 0.729614640660627, 'min_child_weight': 4}. Best is trial 0 with value: 0.7931331474577547.\n", "[I 2025-08-21 13:09:57,615] Trial 3 finished with value: 0.7904199294203569 and parameters: {'lambda': 0.006263181147870989, 'alpha': 2.436978649448901, 'max_depth': 3, 'learning_rate': 0.10019252024717853, 'n_estimators': 110, 'subsample': 0.7739149164830419, 'colsample_bytree': 0.919968397227793, 'min_child_weight': 3}. Best is trial 0 with value: 0.7931331474577547.\n", "[I 2025-08-21 13:09:58,918] Trial 4 finished with value: 0.7754087005475097 and parameters: {'lambda': 6.0633563684504076e-05, 'alpha': 8.347234787248662e-06, 'max_depth': 3, 'learning_rate': 0.13374457776548926, 'n_estimators': 634, 'subsample': 0.5729595537292336, 'colsample_bytree': 0.7636405316174724, 'min_child_weight': 4}. Best is trial 0 with value: 0.7931331474577547.\n", "[I 2025-08-21 13:10:02,237] Trial 5 finished with value: 0.7856588715962621 and parameters: {'lambda': 0.006670852229095431, 'alpha': 0.0038468536968875567, 'max_depth': 8, 'learning_rate': 0.047962810699014764, 'n_estimators': 612, 'subsample': 0.8555318124933906, 'colsample_bytree': 0.8396785909904019, 'min_child_weight': 5}. Best is trial 0 with value: 0.7931331474577547.\n", "[I 2025-08-21 13:10:06,577] Trial 6 finished with value: 0.7789048346450059 and parameters: {'lambda': 0.054417242428701884, 'alpha': 0.005208181186833018, 'max_depth': 11, 'learning_rate': 0.08559321158042081, 'n_estimators': 981, 'subsample': 0.9243815630685637, 'colsample_bytree': 0.8431999181212504, 'min_child_weight': 19}. Best is trial 0 with value: 0.7931331474577547.\n", "[I 2025-08-21 13:10:07,532] Trial 7 finished with value: 0.7833836997040826 and parameters: {'lambda': 0.0016342066592598592, 'alpha': 0.0004784102949419035, 'max_depth': 12, 'learning_rate': 0.08115642614824586, 'n_estimators': 190, 'subsample': 0.973977900926726, 'colsample_bytree': 0.8065665227683381, 'min_child_weight': 19}. Best is trial 0 with value: 0.7931331474577547.\n", "[I 2025-08-21 13:10:11,602] Trial 8 finished with value: 0.7844419266719675 and parameters: {'lambda': 4.7533795827837407e-07, 'alpha': 0.00020763215980156775, 'max_depth': 9, 'learning_rate': 0.012156490545578678, 'n_estimators': 353, 'subsample': 0.6892254282744549, 'colsample_bytree': 0.7498368812673295, 'min_child_weight': 1}. Best is trial 0 with value: 0.7931331474577547.\n", "[I 2025-08-21 13:10:15,176] Trial 9 finished with value: 0.7933998678192313 and parameters: {'lambda': 8.346601006116883e-08, 'alpha': 0.00035180776796363446, 'max_depth': 9, 'learning_rate': 0.02527764994631089, 'n_estimators': 549, 'subsample': 0.5397983661250669, 'colsample_bytree': 0.9674554818734797, 'min_child_weight': 3}. Best is trial 9 with value: 0.7933998678192313.\n", "[I 2025-08-21 13:10:16,509] Trial 10 finished with value: 0.7661784896298002 and parameters: {'lambda': 1.0524262320212456e-08, 'alpha': 6.034874149874216e-08, 'max_depth': 6, 'learning_rate': 0.19904000981593553, 'n_estimators': 431, 'subsample': 0.5085510182362099, 'colsample_bytree': 0.6099959705069113, 'min_child_weight': 8}. Best is trial 9 with value: 0.7933998678192313.\n", "[I 2025-08-21 13:10:18,208] Trial 11 finished with value: 0.7876292721429425 and parameters: {'lambda': 1.8066230959763083, 'alpha': 3.1761609158785552e-06, 'max_depth': 6, 'learning_rate': 0.045013255767340686, 'n_estimators': 467, 'subsample': 0.6087570337196875, 'colsample_bytree': 0.9746070601333882, 'min_child_weight': 14}. Best is trial 9 with value: 0.7933998678192313.\n", "[I 2025-08-21 13:10:21,128] Trial 12 finished with value: 0.7827920181904677 and parameters: {'lambda': 3.8446912261471284e-05, 'alpha': 1.1287276656839025e-05, 'max_depth': 7, 'learning_rate': 0.049933573267307296, 'n_estimators': 740, 'subsample': 0.6132041583560182, 'colsample_bytree': 0.9979953422916426, 'min_child_weight': 15}. Best is trial 9 with value: 0.7933998678192313.\n", "[I 2025-08-21 13:10:23,668] Trial 13 finished with value: 0.7925145411311997 and parameters: {'lambda': 4.3900104710147704e-06, 'alpha': 7.219464290779044e-08, 'max_depth': 9, 'learning_rate': 0.030767539834019975, 'n_estimators': 539, 'subsample': 0.5002453060408851, 'colsample_bytree': 0.9269534529834702, 'min_child_weight': 9}. Best is trial 9 with value: 0.7933998678192313.\n", "[I 2025-08-21 13:10:27,179] Trial 14 finished with value: 0.794279609743507 and parameters: {'lambda': 0.4848477787007462, 'alpha': 0.0001797981802038185, 'max_depth': 10, 'learning_rate': 0.005142106647284209, 'n_estimators': 759, 'subsample': 0.6794361679279831, 'colsample_bytree': 0.904211758142466, 'min_child_weight': 16}. Best is trial 14 with value: 0.794279609743507.\n", "[I 2025-08-21 13:10:30,915] Trial 15 finished with value: 0.7792775466029206 and parameters: {'lambda': 8.44231247570146, 'alpha': 0.00039714351837404484, 'max_depth': 10, 'learning_rate': 0.14891806287637144, 'n_estimators': 880, 'subsample': 0.7855566600591917, 'colsample_bytree': 0.906189420046951, 'min_child_weight': 16}. Best is trial 14 with value: 0.794279609743507.\n", "[I 2025-08-21 13:10:33,592] Trial 16 finished with value: 0.7984985967526504 and parameters: {'lambda': 0.4364079287136194, 'alpha': 0.1508751579851825, 'max_depth': 7, 'learning_rate': 0.007696659536666928, 'n_estimators': 740, 'subsample': 0.5657827448387689, 'colsample_bytree': 0.6494972310603331, 'min_child_weight': 12}. Best is trial 16 with value: 0.7984985967526504.\n", "[I 2025-08-21 13:10:34,595] Trial 17 finished with value: 0.7593422766578926 and parameters: {'lambda': 0.664300898381854, 'alpha': 6.805827641831139, 'max_depth': 5, 'learning_rate': 0.07183649913703188, 'n_estimators': 775, 'subsample': 0.632658182000887, 'colsample_bytree': 0.6641346057032848, 'min_child_weight': 12}. Best is trial 16 with value: 0.7984985967526504.\n", "[I 2025-08-21 13:10:38,358] Trial 18 finished with value: 0.7984027504147335 and parameters: {'lambda': 0.15719176475478977, 'alpha': 0.5718160756452226, 'max_depth': 7, 'learning_rate': 0.006359346459097692, 'n_estimators': 997, 'subsample': 0.7301193591291973, 'colsample_bytree': 0.6664579032238455, 'min_child_weight': 11}. Best is trial 16 with value: 0.7984985967526504.\n", "[I 2025-08-21 13:10:40,935] Trial 19 finished with value: 0.789335421933313 and parameters: {'lambda': 0.09187102758168675, 'alpha': 0.4523029855916133, 'max_depth': 5, 'learning_rate': 0.058178485107875946, 'n_estimators': 991, 'subsample': 0.8501201584273965, 'colsample_bytree': 0.5133112608876302, 'min_child_weight': 9}. Best is trial 16 with value: 0.7984985967526504.\n", "[I 2025-08-21 13:10:43,683] Trial 20 finished with value: 0.778737974424391 and parameters: {'lambda': 0.00034269403253625624, 'alpha': 0.1070848432875935, 'max_depth': 7, 'learning_rate': 0.14306846984979033, 'n_estimators': 892, 'subsample': 0.724960380671867, 'colsample_bytree': 0.6703116819541373, 'min_child_weight': 7}. Best is trial 16 with value: 0.7984985967526504.\n", "[I 2025-08-21 13:10:46,312] Trial 21 finished with value: 0.7953990783148597 and parameters: {'lambda': 0.416603793569738, 'alpha': 0.7170593534861438, 'max_depth': 8, 'learning_rate': 0.007973220541708074, 'n_estimators': 688, 'subsample': 0.7449664708501748, 'colsample_bytree': 0.6685487786995538, 'min_child_weight': 12}. Best is trial 16 with value: 0.7984985967526504.\n", "[I 2025-08-21 13:10:48,663] Trial 22 finished with value: 0.798481477640391 and parameters: {'lambda': 0.18821286643395152, 'alpha': 1.4378588289851626, 'max_depth': 8, 'learning_rate': 0.03688906029565821, 'n_estimators': 701, 'subsample': 0.810216974934908, 'colsample_bytree': 0.6660765654455014, 'min_child_weight': 12}. Best is trial 16 with value: 0.7984985967526504.\n", "[I 2025-08-21 13:10:49,904] Trial 23 finished with value: 0.7375928816534512 and parameters: {'lambda': 6.49260382019626, 'alpha': 9.781662700834149, 'max_depth': 6, 'learning_rate': 0.036544361076356655, 'n_estimators': 844, 'subsample': 0.8288818029093794, 'colsample_bytree': 0.6080266229833028, 'min_child_weight': 10}. Best is trial 16 with value: 0.7984985967526504.\n", "[I 2025-08-21 13:10:52,634] Trial 24 finished with value: 0.7879375835480065 and parameters: {'lambda': 0.15077209456451313, 'alpha': 0.03209373986865848, 'max_depth': 7, 'learning_rate': 0.03521329437093422, 'n_estimators': 688, 'subsample': 0.8087373642718456, 'colsample_bytree': 0.6115234315761336, 'min_child_weight': 14}. Best is trial 16 with value: 0.7984985967526504.\n", "[I 2025-08-21 13:10:54,928] Trial 25 finished with value: 0.795906037155585 and parameters: {'lambda': 2.2020180643834513, 'alpha': 1.2698630891923575, 'max_depth': 5, 'learning_rate': 0.05768676713550118, 'n_estimators': 930, 'subsample': 0.9083020641345287, 'colsample_bytree': 0.6848869364485908, 'min_child_weight': 11}. Best is trial 16 with value: 0.7984985967526504.\n", "[I 2025-08-21 13:10:59,456] Trial 26 finished with value: 0.7870601461228453 and parameters: {'lambda': 0.03510340844024597, 'alpha': 0.0787775413872148, 'max_depth': 8, 'learning_rate': 0.024129730923154662, 'n_estimators': 827, 'subsample': 0.8763110049160336, 'colsample_bytree': 0.7120829983380325, 'min_child_weight': 7}. Best is trial 16 with value: 0.7984985967526504.\n", "[I 2025-08-21 13:11:01,250] Trial 27 finished with value: 0.7885965274741558 and parameters: {'lambda': 0.00037290757422455166, 'alpha': 0.015652219628404653, 'max_depth': 4, 'learning_rate': 0.06628189531969075, 'n_estimators': 711, 'subsample': 0.7388376419089059, 'colsample_bytree': 0.5729117942166476, 'min_child_weight': 14}. Best is trial 16 with value: 0.7984985967526504.\n", "[I 2025-08-21 13:11:04,710] Trial 28 finished with value: 0.7958364149436791 and parameters: {'lambda': 0.23004770914057937, 'alpha': 0.38825663440670555, 'max_depth': 7, 'learning_rate': 0.005156154792151479, 'n_estimators': 928, 'subsample': 0.5757463826367446, 'colsample_bytree': 0.6241468061792748, 'min_child_weight': 11}. Best is trial 16 with value: 0.7984985967526504.\n", "[I 2025-08-21 13:11:06,320] Trial 29 finished with value: 0.7904313000303738 and parameters: {'lambda': 0.0010571011418896022, 'alpha': 2.5476604535264227, 'max_depth': 6, 'learning_rate': 0.022742273497285914, 'n_estimators': 622, 'subsample': 0.6495636714108253, 'colsample_bytree': 0.6444556422919534, 'min_child_weight': 17}. Best is trial 16 with value: 0.7984985967526504.\n", "[I 2025-08-21 13:11:08,250] Trial 30 finished with value: 0.7764948009714946 and parameters: {'lambda': 0.015073139383616744, 'alpha': 0.0023590882589196475, 'max_depth': 7, 'learning_rate': 0.17322004351264747, 'n_estimators': 513, 'subsample': 0.781544694449207, 'colsample_bytree': 0.566515281726209, 'min_child_weight': 13}. Best is trial 16 with value: 0.7984985967526504.\n", "[I 2025-08-21 13:11:10,489] Trial 31 finished with value: 0.798841494882526 and parameters: {'lambda': 1.8337527526629256, 'alpha': 1.3583767218402807, 'max_depth': 5, 'learning_rate': 0.03863848066323042, 'n_estimators': 932, 'subsample': 0.8972337043473068, 'colsample_bytree': 0.699820094597179, 'min_child_weight': 10}. Best is trial 31 with value: 0.798841494882526.\n", "[I 2025-08-21 13:11:12,879] Trial 32 finished with value: 0.7968708631910341 and parameters: {'lambda': 1.373378718555686, 'alpha': 0.2777955246232725, 'max_depth': 4, 'learning_rate': 0.02016569017626333, 'n_estimators': 940, 'subsample': 0.9742919462804864, 'colsample_bytree': 0.705933582186928, 'min_child_weight': 10}. Best is trial 31 with value: 0.798841494882526.\n", "[I 2025-08-21 13:11:15,004] Trial 33 finished with value: 0.8005655223261325 and parameters: {'lambda': 3.478068668841368, 'alpha': 2.089047802750229, 'max_depth': 8, 'learning_rate': 0.036351997452542126, 'n_estimators': 841, 'subsample': 0.8955337643700384, 'colsample_bytree': 0.7763564472333007, 'min_child_weight': 13}. Best is trial 33 with value: 0.8005655223261325.\n", "[I 2025-08-21 13:11:16,345] Trial 34 finished with value: 0.7970879479142267 and parameters: {'lambda': 5.060510980196285, 'alpha': 2.922208666532131, 'max_depth': 8, 'learning_rate': 0.10361684850700188, 'n_estimators': 811, 'subsample': 0.8988867033809229, 'colsample_bytree': 0.7847854590802145, 'min_child_weight': 13}. Best is trial 33 with value: 0.8005655223261325.\n", "[I 2025-08-21 13:11:18,501] Trial 35 finished with value: 0.7897366186719237 and parameters: {'lambda': 1.575614522687267, 'alpha': 0.058828813847086145, 'max_depth': 4, 'learning_rate': 0.039775771802595734, 'n_estimators': 860, 'subsample': 0.9980653542271576, 'colsample_bytree': 0.728065615157306, 'min_child_weight': 13}. Best is trial 33 with value: 0.8005655223261325.\n", "[I 2025-08-21 13:11:19,801] Trial 36 finished with value: 0.7949665456645195 and parameters: {'lambda': 0.012023721424292432, 'alpha': 2.2059367735147037, 'max_depth': 8, 'learning_rate': 0.08390407722196519, 'n_estimators': 673, 'subsample': 0.9464801012292767, 'colsample_bytree': 0.7549837719425763, 'min_child_weight': 9}. Best is trial 33 with value: 0.8005655223261325.\n", "[I 2025-08-21 13:11:24,651] Trial 37 finished with value: 0.790805293622405 and parameters: {'lambda': 9.190392428185616, 'alpha': 0.013655232888726743, 'max_depth': 9, 'learning_rate': 0.05355551034237323, 'n_estimators': 774, 'subsample': 0.8754865624143775, 'colsample_bytree': 0.836131609077337, 'min_child_weight': 6}. Best is trial 33 with value: 0.8005655223261325.\n", "[I 2025-08-21 13:11:27,543] Trial 38 finished with value: 0.7791746576543742 and parameters: {'lambda': 0.04381477361850226, 'alpha': 0.19013989163842984, 'max_depth': 10, 'learning_rate': 0.12198656162216004, 'n_estimators': 725, 'subsample': 0.8130413319737555, 'colsample_bytree': 0.697009398119944, 'min_child_weight': 12}. Best is trial 33 with value: 0.8005655223261325.\n", "[I 2025-08-21 13:11:28,472] Trial 39 finished with value: 0.7774258359360928 and parameters: {'lambda': 0.003242612984946614, 'alpha': 4.963693502553369, 'max_depth': 3, 'learning_rate': 0.06954363191697029, 'n_estimators': 600, 'subsample': 0.8458807888446366, 'colsample_bytree': 0.7930112906281274, 'min_child_weight': 15}. Best is trial 33 with value: 0.8005655223261325.\n", "[I 2025-08-21 13:11:30,526] Trial 40 finished with value: 0.7952617998632581 and parameters: {'lambda': 1.0208504741612734, 'alpha': 1.1576501199537828, 'max_depth': 11, 'learning_rate': 0.09498397760178443, 'n_estimators': 802, 'subsample': 0.937263682061362, 'colsample_bytree': 0.7288376980976173, 'min_child_weight': 20}. Best is trial 33 with value: 0.8005655223261325.\n", "[I 2025-08-21 13:11:34,399] Trial 41 finished with value: 0.797893074902461 and parameters: {'lambda': 0.1898862627127817, 'alpha': 0.7336342450528239, 'max_depth': 8, 'learning_rate': 0.019364850968569595, 'n_estimators': 997, 'subsample': 0.715552689771544, 'colsample_bytree': 0.6426834444639219, 'min_child_weight': 11}. Best is trial 33 with value: 0.8005655223261325.\n", "[I 2025-08-21 13:11:37,638] Trial 42 finished with value: 0.791262791324631 and parameters: {'lambda': 0.2671267477917808, 'alpha': 0.3618111043943763, 'max_depth': 6, 'learning_rate': 0.03042380251887909, 'n_estimators': 931, 'subsample': 0.7677192721035098, 'colsample_bytree': 0.6413486833177997, 'min_child_weight': 10}. Best is trial 33 with value: 0.8005655223261325.\n", "[I 2025-08-21 13:11:40,992] Trial 43 finished with value: 0.7892033909184746 and parameters: {'lambda': 3.532952566983344, 'alpha': 0.13228293240368208, 'max_depth': 7, 'learning_rate': 0.041571137257004745, 'n_estimators': 878, 'subsample': 0.88941836249369, 'colsample_bytree': 0.5876493259029714, 'min_child_weight': 12}. Best is trial 33 with value: 0.8005655223261325.\n", "[I 2025-08-21 13:11:42,434] Trial 44 finished with value: 0.7900625818946146 and parameters: {'lambda': 0.07521032965915475, 'alpha': 1.4607033347377971, 'max_depth': 9, 'learning_rate': 0.014755044960413057, 'n_estimators': 322, 'subsample': 0.9185456330391801, 'colsample_bytree': 0.7669670675580147, 'min_child_weight': 8}. Best is trial 33 with value: 0.8005655223261325.\n", "[I 2025-08-21 13:11:44,971] Trial 45 finished with value: 0.800454931822262 and parameters: {'lambda': 0.8009442262875487, 'alpha': 0.00161421599450327, 'max_depth': 5, 'learning_rate': 0.014029512667379916, 'n_estimators': 952, 'subsample': 0.5686613973885365, 'colsample_bytree': 0.5189751787131525, 'min_child_weight': 13}. Best is trial 33 with value: 0.8005655223261325.\n", "[I 2025-08-21 13:11:47,110] Trial 46 finished with value: 0.7849407299733533 and parameters: {'lambda': 0.7461540590572946, 'alpha': 0.0009018298846994249, 'max_depth': 5, 'learning_rate': 0.046161020863950075, 'n_estimators': 651, 'subsample': 0.5503611528223579, 'colsample_bytree': 0.8279820329846312, 'min_child_weight': 15}. Best is trial 33 with value: 0.8005655223261325.\n", "[I 2025-08-21 13:11:49,272] Trial 47 finished with value: 0.7977772279057144 and parameters: {'lambda': 2.668702735226072, 'alpha': 3.21854192999117e-05, 'max_depth': 4, 'learning_rate': 0.02803099281650747, 'n_estimators': 948, 'subsample': 0.5309525270776877, 'colsample_bytree': 0.5395011323032629, 'min_child_weight': 13}. Best is trial 33 with value: 0.8005655223261325.\n", "[I 2025-08-21 13:11:50,880] Trial 48 finished with value: 0.8084298832353095 and parameters: {'lambda': 7.646780137148901e-05, 'alpha': 0.005638471142719872, 'max_depth': 3, 'learning_rate': 0.015841755405201448, 'n_estimators': 899, 'subsample': 0.6093711392223484, 'colsample_bytree': 0.5362903454506992, 'min_child_weight': 17}. Best is trial 48 with value: 0.8084298832353095.\n", "[I 2025-08-21 13:11:53,224] Trial 49 finished with value: 0.7997976547309819 and parameters: {'lambda': 2.1829119457576677e-05, 'alpha': 0.005651693157602365, 'max_depth': 5, 'learning_rate': 0.01607865210327436, 'n_estimators': 894, 'subsample': 0.5863437847514055, 'colsample_bytree': 0.5020830810158213, 'min_child_weight': 17}. Best is trial 48 with value: 0.8084298832353095.\n", "[I 2025-08-21 13:11:53,485] A new study created in memory with name: no-name-17590d28-9d31-4626-84b9-854c3a2fdf84\n", "[I 2025-08-21 13:12:21,170] Trial 0 finished with value: 0.8038014608898998 and parameters: {'iterations': 961, 'depth': 9, 'learning_rate': 0.12477125055325596, 'l2_leaf_reg': 7.810780051269786, 'bagging_temperature': 0.39383862667988456, 'border_count': 149, 'random_strength': 4.003834308231592}. Best is trial 0 with value: 0.8038014608898998.\n", "[I 2025-08-21 13:12:30,190] Trial 1 finished with value: 0.813871930860189 and parameters: {'iterations': 742, 'depth': 7, 'learning_rate': 0.06251811522370616, 'l2_leaf_reg': 7.481610411459552, 'bagging_temperature': 0.9695179372932329, 'border_count': 249, 'random_strength': 4.443813428199062}. Best is trial 1 with value: 0.813871930860189.\n", "[I 2025-08-21 13:12:32,827] Trial 2 finished with value: 0.8123590214668763 and parameters: {'iterations': 269, 'depth': 7, 'learning_rate': 0.08812923638268609, 'l2_leaf_reg': 8.673462366529941, 'bagging_temperature': 0.048527142137642976, 'border_count': 168, 'random_strength': 1.7248438935624535}. Best is trial 1 with value: 0.813871930860189.\n", "[I 2025-08-21 13:12:45,533] Trial 3 finished with value: 0.7974880151449617 and parameters: {'iterations': 452, 'depth': 10, 'learning_rate': 0.18719408687492647, 'l2_leaf_reg': 1.1577448391665661, 'bagging_temperature': 0.6578690596875266, 'border_count': 44, 'random_strength': 4.116771770447266}. Best is trial 1 with value: 0.813871930860189.\n", "[I 2025-08-21 13:14:48,337] Trial 4 finished with value: 0.8024426390493582 and parameters: {'iterations': 886, 'depth': 10, 'learning_rate': 0.10348166461266253, 'l2_leaf_reg': 4.044924274834189, 'bagging_temperature': 0.9969036064642812, 'border_count': 224, 'random_strength': 2.2743098106090693}. Best is trial 1 with value: 0.813871930860189.\n", "[I 2025-08-21 13:15:02,631] Trial 5 finished with value: 0.8133730144354129 and parameters: {'iterations': 716, 'depth': 5, 'learning_rate': 0.15457700436860447, 'l2_leaf_reg': 3.2423187151108737, 'bagging_temperature': 0.850111428377811, 'border_count': 145, 'random_strength': 1.6070559538269402}. Best is trial 1 with value: 0.813871930860189.\n", "[I 2025-08-21 13:15:16,601] Trial 6 finished with value: 0.8137087123421656 and parameters: {'iterations': 959, 'depth': 7, 'learning_rate': 0.0774217765084346, 'l2_leaf_reg': 1.5574040910620706, 'bagging_temperature': 0.20436841289319607, 'border_count': 130, 'random_strength': 1.7392776165763804}. Best is trial 1 with value: 0.813871930860189.\n", "[I 2025-08-21 13:15:19,338] Trial 7 finished with value: 0.8104203070164621 and parameters: {'iterations': 510, 'depth': 5, 'learning_rate': 0.14577574656442352, 'l2_leaf_reg': 3.1210441584463844, 'bagging_temperature': 0.9549807438867781, 'border_count': 232, 'random_strength': 3.671072373958863}. Best is trial 1 with value: 0.813871930860189.\n", "[I 2025-08-21 13:15:32,022] Trial 8 finished with value: 0.8070706843560197 and parameters: {'iterations': 322, 'depth': 9, 'learning_rate': 0.04913959973159415, 'l2_leaf_reg': 1.016209374492146, 'bagging_temperature': 0.5010941290299532, 'border_count': 189, 'random_strength': 2.9218794739730245}. Best is trial 1 with value: 0.813871930860189.\n", "[I 2025-08-21 13:15:45,983] Trial 9 finished with value: 0.8078835631496257 and parameters: {'iterations': 563, 'depth': 8, 'learning_rate': 0.13358114681828825, 'l2_leaf_reg': 3.4846896106699856, 'bagging_temperature': 0.8447546628845808, 'border_count': 227, 'random_strength': 2.189306516236975}. Best is trial 1 with value: 0.813871930860189.\n", "[I 2025-08-21 13:15:48,893] Trial 10 finished with value: 0.7952235662942219 and parameters: {'iterations': 742, 'depth': 4, 'learning_rate': 0.0119230798577043, 'l2_leaf_reg': 5.864177021565409, 'bagging_temperature': 0.6678190271838382, 'border_count': 92, 'random_strength': 4.942934914146626}. Best is trial 1 with value: 0.813871930860189.\n", "[I 2025-08-21 13:15:56,358] Trial 11 finished with value: 0.8096821397807148 and parameters: {'iterations': 815, 'depth': 7, 'learning_rate': 0.06384725836361106, 'l2_leaf_reg': 1.8221078450791055, 'bagging_temperature': 0.14288650991216267, 'border_count': 100, 'random_strength': 0.7608834319544834}. Best is trial 1 with value: 0.813871930860189.\n", "[I 2025-08-21 13:16:02,692] Trial 12 finished with value: 0.8146936112714389 and parameters: {'iterations': 992, 'depth': 6, 'learning_rate': 0.03600670724707207, 'l2_leaf_reg': 1.726274376732641, 'bagging_temperature': 0.2667338145585675, 'border_count': 107, 'random_strength': 0.5643041250570786}. Best is trial 12 with value: 0.8146936112714389.\n", "[I 2025-08-21 13:16:06,194] Trial 13 finished with value: 0.8153580879933594 and parameters: {'iterations': 659, 'depth': 6, 'learning_rate': 0.024022663871204556, 'l2_leaf_reg': 1.9891564436547524, 'bagging_temperature': 0.32012036436096514, 'border_count': 49, 'random_strength': 0.6262708126833169}. Best is trial 13 with value: 0.8153580879933594.\n", "[I 2025-08-21 13:16:09,138] Trial 14 finished with value: 0.7936703560583804 and parameters: {'iterations': 626, 'depth': 6, 'learning_rate': 0.006597018817754787, 'l2_leaf_reg': 2.1075120971837715, 'bagging_temperature': 0.32535759628358696, 'border_count': 33, 'random_strength': 0.7280955835637135}. Best is trial 13 with value: 0.8153580879933594.\n", "[I 2025-08-21 13:16:11,040] Trial 15 finished with value: 0.8137488502233147 and parameters: {'iterations': 404, 'depth': 5, 'learning_rate': 0.027092599856483393, 'l2_leaf_reg': 2.373530483066576, 'bagging_temperature': 0.2764319739888257, 'border_count': 74, 'random_strength': 0.5188259180678283}. Best is trial 13 with value: 0.8153580879933594.\n", "[I 2025-08-21 13:16:14,499] Trial 16 finished with value: 0.813890545091019 and parameters: {'iterations': 647, 'depth': 6, 'learning_rate': 0.03758707931077852, 'l2_leaf_reg': 1.4469793661099235, 'bagging_temperature': 0.47370430943410563, 'border_count': 63, 'random_strength': 1.2141328294090443}. Best is trial 13 with value: 0.8153580879933594.\n", "[I 2025-08-21 13:16:17,678] Trial 17 finished with value: 0.8171272995648307 and parameters: {'iterations': 837, 'depth': 4, 'learning_rate': 0.03235106514474324, 'l2_leaf_reg': 2.5345363378705716, 'bagging_temperature': 0.047417860052291594, 'border_count': 118, 'random_strength': 1.1082626770045185}. Best is trial 17 with value: 0.8171272995648307.\n", "[I 2025-08-21 13:16:20,580] Trial 18 finished with value: 0.8067934182811175 and parameters: {'iterations': 804, 'depth': 4, 'learning_rate': 0.10602522652938842, 'l2_leaf_reg': 4.711720637496496, 'bagging_temperature': 0.04752742599331875, 'border_count': 117, 'random_strength': 3.0692514334312597}. Best is trial 17 with value: 0.8171272995648307.\n", "[I 2025-08-21 13:16:23,439] Trial 19 finished with value: 0.8166998562078225 and parameters: {'iterations': 850, 'depth': 4, 'learning_rate': 0.021377555352663304, 'l2_leaf_reg': 2.5932811398875826, 'bagging_temperature': 0.007236003734532474, 'border_count': 68, 'random_strength': 1.2383339525212922}. Best is trial 17 with value: 0.8171272995648307.\n", "[I 2025-08-21 13:16:26,530] Trial 20 finished with value: 0.8150352583652953 and parameters: {'iterations': 870, 'depth': 4, 'learning_rate': 0.05387287569574559, 'l2_leaf_reg': 2.612375941315178, 'bagging_temperature': 0.002560967558217081, 'border_count': 80, 'random_strength': 1.2997666153799456}. Best is trial 17 with value: 0.8171272995648307.\n", "[I 2025-08-21 13:16:30,179] Trial 21 finished with value: 0.8157705313790606 and parameters: {'iterations': 871, 'depth': 5, 'learning_rate': 0.020956335716333766, 'l2_leaf_reg': 2.6074014740196736, 'bagging_temperature': 0.09795280225188141, 'border_count': 55, 'random_strength': 1.0500833306018302}. Best is trial 17 with value: 0.8171272995648307.\n", "[I 2025-08-21 13:16:33,298] Trial 22 finished with value: 0.8135829970473865 and parameters: {'iterations': 887, 'depth': 4, 'learning_rate': 0.016745009285687187, 'l2_leaf_reg': 2.7601909720739264, 'bagging_temperature': 0.13323629441474094, 'border_count': 58, 'random_strength': 1.1252981415857852}. Best is trial 17 with value: 0.8171272995648307.\n", "[I 2025-08-21 13:16:36,910] Trial 23 finished with value: 0.81860172974046 and parameters: {'iterations': 825, 'depth': 5, 'learning_rate': 0.041331774949073634, 'l2_leaf_reg': 4.372991302903244, 'bagging_temperature': 0.13122680144989896, 'border_count': 80, 'random_strength': 2.358827578904561}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:16:39,355] Trial 24 finished with value: 0.8141103314261798 and parameters: {'iterations': 805, 'depth': 4, 'learning_rate': 0.04219750089728613, 'l2_leaf_reg': 4.754503524350552, 'bagging_temperature': 0.18243393494264773, 'border_count': 80, 'random_strength': 2.309269614927553}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:16:42,235] Trial 25 finished with value: 0.8143431135679459 and parameters: {'iterations': 719, 'depth': 5, 'learning_rate': 0.08034857330541759, 'l2_leaf_reg': 5.915711905741432, 'bagging_temperature': 0.00331882131642966, 'border_count': 121, 'random_strength': 3.286144581601095}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:16:44,677] Trial 26 finished with value: 0.8156213713310254 and parameters: {'iterations': 818, 'depth': 4, 'learning_rate': 0.06529793746045812, 'l2_leaf_reg': 3.9677330310702996, 'bagging_temperature': 0.09217537296200035, 'border_count': 94, 'random_strength': 2.5515488976714624}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:16:48,678] Trial 27 finished with value: 0.801359391613448 and parameters: {'iterations': 932, 'depth': 5, 'learning_rate': 0.007626655462366801, 'l2_leaf_reg': 2.235113991293263, 'bagging_temperature': 0.21811350692961662, 'border_count': 163, 'random_strength': 1.908432135328765}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:16:50,443] Trial 28 finished with value: 0.815707653604866 and parameters: {'iterations': 578, 'depth': 4, 'learning_rate': 0.032568673905873056, 'l2_leaf_reg': 5.355177464566514, 'bagging_temperature': 0.4225762549067964, 'border_count': 73, 'random_strength': 2.643463506116976}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:16:54,226] Trial 29 finished with value: 0.8140764194269599 and parameters: {'iterations': 943, 'depth': 5, 'learning_rate': 0.052724929197135756, 'l2_leaf_reg': 3.711592536509823, 'bagging_temperature': 0.5551894655764392, 'border_count': 133, 'random_strength': 2.0012881408022913}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:16:57,940] Trial 30 finished with value: 0.8151628699586217 and parameters: {'iterations': 694, 'depth': 6, 'learning_rate': 0.11888094253432269, 'l2_leaf_reg': 7.008553080945625, 'bagging_temperature': 0.08438606059299736, 'border_count': 110, 'random_strength': 1.5216622340207873}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:17:01,391] Trial 31 finished with value: 0.815557966685559 and parameters: {'iterations': 859, 'depth': 5, 'learning_rate': 0.029127183280344172, 'l2_leaf_reg': 2.8315650553867875, 'bagging_temperature': 0.10734141118518672, 'border_count': 60, 'random_strength': 0.9976754579544189}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:17:03,929] Trial 32 finished with value: 0.8168025848830501 and parameters: {'iterations': 789, 'depth': 4, 'learning_rate': 0.02167427417746765, 'l2_leaf_reg': 2.4773105320090045, 'bagging_temperature': 0.1515786373694921, 'border_count': 36, 'random_strength': 1.3418837043770875}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:17:06,612] Trial 33 finished with value: 0.8177641147985408 and parameters: {'iterations': 766, 'depth': 4, 'learning_rate': 0.04467999365508048, 'l2_leaf_reg': 3.1542105916661205, 'bagging_temperature': 0.17649701310432786, 'border_count': 34, 'random_strength': 1.3936749720960888}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:17:09,085] Trial 34 finished with value: 0.8115722989372705 and parameters: {'iterations': 785, 'depth': 4, 'learning_rate': 0.07231260426291733, 'l2_leaf_reg': 3.134387243503532, 'bagging_temperature': 0.17809320779046955, 'border_count': 35, 'random_strength': 1.391137171273173}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:17:11,571] Trial 35 finished with value: 0.8129537695949682 and parameters: {'iterations': 768, 'depth': 4, 'learning_rate': 0.091185197331549, 'l2_leaf_reg': 4.486431664352805, 'bagging_temperature': 0.2593952940003046, 'border_count': 41, 'random_strength': 1.8003775065191168}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:17:18,051] Trial 36 finished with value: 0.8090352007846968 and parameters: {'iterations': 685, 'depth': 8, 'learning_rate': 0.046125376698116075, 'l2_leaf_reg': 1.3331505239015895, 'bagging_temperature': 0.3220978219998833, 'border_count': 49, 'random_strength': 0.8459729097785941}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:17:22,279] Trial 37 finished with value: 0.8150215838061998 and parameters: {'iterations': 754, 'depth': 5, 'learning_rate': 0.05927815262472606, 'l2_leaf_reg': 4.240766120382393, 'bagging_temperature': 0.1812725265290191, 'border_count': 162, 'random_strength': 1.5222515743451106}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:17:26,477] Trial 38 finished with value: 0.8084220241683953 and parameters: {'iterations': 933, 'depth': 4, 'learning_rate': 0.18434240924949563, 'l2_leaf_reg': 3.3741653089278816, 'bagging_temperature': 0.3640374502566148, 'border_count': 188, 'random_strength': 2.026846467050921}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:17:29,218] Trial 39 finished with value: 0.8164093569726936 and parameters: {'iterations': 530, 'depth': 5, 'learning_rate': 0.09431078355538286, 'l2_leaf_reg': 2.943340908429398, 'bagging_temperature': 0.06722270000493151, 'border_count': 88, 'random_strength': 3.6746332386173957}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:17:33,097] Trial 40 finished with value: 0.8140600842588526 and parameters: {'iterations': 910, 'depth': 4, 'learning_rate': 0.04348624494034088, 'l2_leaf_reg': 8.23543480389723, 'bagging_temperature': 0.2446793911463369, 'border_count': 136, 'random_strength': 2.4955735641575574}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:17:36,037] Trial 41 finished with value: 0.8154323180152984 and parameters: {'iterations': 835, 'depth': 4, 'learning_rate': 0.0177753890392089, 'l2_leaf_reg': 1.9241481358017805, 'bagging_temperature': 0.04273685018646804, 'border_count': 68, 'random_strength': 1.429542188167548}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:17:38,573] Trial 42 finished with value: 0.8179565889858195 and parameters: {'iterations': 848, 'depth': 4, 'learning_rate': 0.029152746273013452, 'l2_leaf_reg': 2.3754454313829694, 'bagging_temperature': 0.14737764749095847, 'border_count': 32, 'random_strength': 1.689232666263298}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:17:41,327] Trial 43 finished with value: 0.8179489183453598 and parameters: {'iterations': 986, 'depth': 4, 'learning_rate': 0.03422954834321244, 'l2_leaf_reg': 2.31018316087645, 'bagging_temperature': 0.1300318069243656, 'border_count': 44, 'random_strength': 1.7170398011538892}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:17:44,348] Trial 44 finished with value: 0.8173294518770884 and parameters: {'iterations': 906, 'depth': 5, 'learning_rate': 0.0353228976190285, 'l2_leaf_reg': 1.717355007377317, 'bagging_temperature': 0.21693035834064955, 'border_count': 46, 'random_strength': 1.7577381991392167}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:17:47,593] Trial 45 finished with value: 0.8156030208325318 and parameters: {'iterations': 989, 'depth': 5, 'learning_rate': 0.0671262463220717, 'l2_leaf_reg': 1.6635141317479059, 'bagging_temperature': 0.213145204138664, 'border_count': 48, 'random_strength': 1.7143595145072466}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:17:51,915] Trial 46 finished with value: 0.8141152313208057 and parameters: {'iterations': 993, 'depth': 6, 'learning_rate': 0.0565592999030259, 'l2_leaf_reg': 2.1964772757464495, 'bagging_temperature': 0.13292269559441314, 'border_count': 45, 'random_strength': 2.2160148628995016}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:17:58,559] Trial 47 finished with value: 0.8061397371300719 and parameters: {'iterations': 911, 'depth': 8, 'learning_rate': 0.08053653744514808, 'l2_leaf_reg': 1.2848273197586202, 'bagging_temperature': 0.6170070018605348, 'border_count': 32, 'random_strength': 2.3365709867635354}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:17:59,569] Trial 48 finished with value: 0.8125015648762262 and parameters: {'iterations': 239, 'depth': 5, 'learning_rate': 0.038427012321052736, 'l2_leaf_reg': 1.5132761071825307, 'bagging_temperature': 0.38618874426108424, 'border_count': 54, 'random_strength': 2.0586171460734772}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:18:10,317] Trial 49 finished with value: 0.8103248078301345 and parameters: {'iterations': 900, 'depth': 9, 'learning_rate': 0.0487736158995503, 'l2_leaf_reg': 3.7419462590641763, 'bagging_temperature': 0.7580386542996286, 'border_count': 42, 'random_strength': 2.848629606067792}. Best is trial 23 with value: 0.81860172974046.\n", "[I 2025-08-21 13:18:10,904] A new study created in memory with name: no-name-6db9a400-ff9e-4a6f-89ed-087a1f999cd1\n", "[I 2025-08-21 13:18:13,978] Trial 0 finished with value: 0.7451148525010719 and parameters: {'n_estimators': 441, 'max_depth': 7, 'min_samples_split': 2, 'min_samples_leaf': 8, 'max_features': 'sqrt'}. Best is trial 0 with value: 0.7451148525010719.\n", "[I 2025-08-21 13:18:15,710] Trial 1 finished with value: 0.7334605874017067 and parameters: {'n_estimators': 305, 'max_depth': 5, 'min_samples_split': 16, 'min_samples_leaf': 6, 'max_features': 'log2'}. Best is trial 0 with value: 0.7451148525010719.\n", "[I 2025-08-21 13:18:20,861] Trial 2 finished with value: 0.7514353346121807 and parameters: {'n_estimators': 690, 'max_depth': 7, 'min_samples_split': 2, 'min_samples_leaf': 4, 'max_features': 'sqrt'}. Best is trial 2 with value: 0.7514353346121807.\n", "[I 2025-08-21 13:18:21,792] Trial 3 finished with value: 0.7393390785072442 and parameters: {'n_estimators': 134, 'max_depth': 19, 'min_samples_split': 3, 'min_samples_leaf': 8, 'max_features': 'log2'}. Best is trial 2 with value: 0.7514353346121807.\n", "[I 2025-08-21 13:18:27,347] Trial 4 finished with value: 0.7488802674415436 and parameters: {'n_estimators': 668, 'max_depth': 19, 'min_samples_split': 14, 'min_samples_leaf': 4, 'max_features': 'log2'}. Best is trial 2 with value: 0.7514353346121807.\n", "[I 2025-08-21 13:18:47,920] Trial 5 finished with value: 0.7831117010009329 and parameters: {'n_estimators': 452, 'max_depth': 10, 'min_samples_split': 8, 'min_samples_leaf': 1, 'max_features': None}. Best is trial 5 with value: 0.7831117010009329.\n", "[I 2025-08-21 13:18:49,969] Trial 6 finished with value: 0.7444049261832022 and parameters: {'n_estimators': 260, 'max_depth': 10, 'min_samples_split': 19, 'min_samples_leaf': 5, 'max_features': 'log2'}. Best is trial 5 with value: 0.7831117010009329.\n", "[I 2025-08-21 13:18:55,848] Trial 7 finished with value: 0.741273201909338 and parameters: {'n_estimators': 753, 'max_depth': 8, 'min_samples_split': 10, 'min_samples_leaf': 10, 'max_features': 'sqrt'}. Best is trial 5 with value: 0.7831117010009329.\n", "[I 2025-08-21 13:19:57,637] Trial 8 finished with value: 0.7768111494850427 and parameters: {'n_estimators': 702, 'max_depth': 10, 'min_samples_split': 9, 'min_samples_leaf': 8, 'max_features': None}. Best is trial 5 with value: 0.7831117010009329.\n", "[I 2025-08-21 13:20:00,243] Trial 9 finished with value: 0.699924974181244 and parameters: {'n_estimators': 121, 'max_depth': 3, 'min_samples_split': 9, 'min_samples_leaf': 9, 'max_features': 'log2'}. Best is trial 5 with value: 0.7831117010009329.\n", "[I 2025-08-21 13:20:58,187] Trial 10 finished with value: 0.7837435774945696 and parameters: {'n_estimators': 525, 'max_depth': 15, 'min_samples_split': 6, 'min_samples_leaf': 1, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:21:20,632] Trial 11 finished with value: 0.7836761108198875 and parameters: {'n_estimators': 508, 'max_depth': 15, 'min_samples_split': 6, 'min_samples_leaf': 1, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:21:48,189] Trial 12 finished with value: 0.7837028045796609 and parameters: {'n_estimators': 564, 'max_depth': 15, 'min_samples_split': 5, 'min_samples_leaf': 1, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:22:12,918] Trial 13 finished with value: 0.7831912702133415 and parameters: {'n_estimators': 566, 'max_depth': 15, 'min_samples_split': 6, 'min_samples_leaf': 2, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:22:35,867] Trial 14 finished with value: 0.782468657238889 and parameters: {'n_estimators': 568, 'max_depth': 15, 'min_samples_split': 5, 'min_samples_leaf': 3, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:22:50,120] Trial 15 finished with value: 0.781999107052883 and parameters: {'n_estimators': 355, 'max_depth': 13, 'min_samples_split': 13, 'min_samples_leaf': 2, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:23:20,079] Trial 16 finished with value: 0.7831492854804019 and parameters: {'n_estimators': 594, 'max_depth': 17, 'min_samples_split': 5, 'min_samples_leaf': 1, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:23:34,294] Trial 17 finished with value: 0.779386333015432 and parameters: {'n_estimators': 419, 'max_depth': 13, 'min_samples_split': 12, 'min_samples_leaf': 6, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:23:55,366] Trial 18 finished with value: 0.7825773447355077 and parameters: {'n_estimators': 513, 'max_depth': 17, 'min_samples_split': 7, 'min_samples_leaf': 3, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:24:30,550] Trial 19 finished with value: 0.7825810648698639 and parameters: {'n_estimators': 781, 'max_depth': 13, 'min_samples_split': 4, 'min_samples_leaf': 2, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:24:36,262] Trial 20 finished with value: 0.756001869789199 and parameters: {'n_estimators': 642, 'max_depth': 17, 'min_samples_split': 11, 'min_samples_leaf': 3, 'max_features': 'sqrt'}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:24:58,680] Trial 21 finished with value: 0.7832180855103209 and parameters: {'n_estimators': 504, 'max_depth': 15, 'min_samples_split': 7, 'min_samples_leaf': 1, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:25:23,252] Trial 22 finished with value: 0.7836277972398682 and parameters: {'n_estimators': 517, 'max_depth': 16, 'min_samples_split': 5, 'min_samples_leaf': 1, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:25:50,886] Trial 23 finished with value: 0.7826345600980733 and parameters: {'n_estimators': 613, 'max_depth': 20, 'min_samples_split': 4, 'min_samples_leaf': 2, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:26:03,817] Trial 24 finished with value: 0.7813733043401537 and parameters: {'n_estimators': 361, 'max_depth': 12, 'min_samples_split': 7, 'min_samples_leaf': 4, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:26:24,880] Trial 25 finished with value: 0.78350392813291 and parameters: {'n_estimators': 490, 'max_depth': 14, 'min_samples_split': 9, 'min_samples_leaf': 1, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:26:42,111] Trial 26 finished with value: 0.7819968556432286 and parameters: {'n_estimators': 384, 'max_depth': 18, 'min_samples_split': 3, 'min_samples_leaf': 2, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:27:03,360] Trial 27 finished with value: 0.7827428880888779 and parameters: {'n_estimators': 541, 'max_depth': 11, 'min_samples_split': 6, 'min_samples_leaf': 3, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:27:05,483] Trial 28 finished with value: 0.7597074912459372 and parameters: {'n_estimators': 208, 'max_depth': 16, 'min_samples_split': 8, 'min_samples_leaf': 1, 'max_features': 'sqrt'}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:27:25,221] Trial 29 finished with value: 0.7823656140958531 and parameters: {'n_estimators': 446, 'max_depth': 14, 'min_samples_split': 2, 'min_samples_leaf': 2, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:27:30,074] Trial 30 finished with value: 0.7501140576480896 and parameters: {'n_estimators': 616, 'max_depth': 12, 'min_samples_split': 16, 'min_samples_leaf': 5, 'max_features': 'sqrt'}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:27:52,320] Trial 31 finished with value: 0.7832170314986535 and parameters: {'n_estimators': 467, 'max_depth': 16, 'min_samples_split': 5, 'min_samples_leaf': 1, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:28:18,596] Trial 32 finished with value: 0.7834198106000183 and parameters: {'n_estimators': 530, 'max_depth': 16, 'min_samples_split': 4, 'min_samples_leaf': 1, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:28:31,251] Trial 33 finished with value: 0.7779017321818706 and parameters: {'n_estimators': 424, 'max_depth': 14, 'min_samples_split': 6, 'min_samples_leaf': 7, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:28:56,728] Trial 34 finished with value: 0.7826021919061563 and parameters: {'n_estimators': 565, 'max_depth': 19, 'min_samples_split': 2, 'min_samples_leaf': 2, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:29:02,686] Trial 35 finished with value: 0.7537800706281463 and parameters: {'n_estimators': 702, 'max_depth': 18, 'min_samples_split': 3, 'min_samples_leaf': 3, 'max_features': 'log2'}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:29:20,094] Trial 36 finished with value: 0.7816258254650167 and parameters: {'n_estimators': 484, 'max_depth': 18, 'min_samples_split': 8, 'min_samples_leaf': 4, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:29:25,519] Trial 37 finished with value: 0.7541976439842047 and parameters: {'n_estimators': 658, 'max_depth': 8, 'min_samples_split': 5, 'min_samples_leaf': 1, 'max_features': 'log2'}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:29:28,178] Trial 38 finished with value: 0.7537325380609763 and parameters: {'n_estimators': 312, 'max_depth': 15, 'min_samples_split': 20, 'min_samples_leaf': 2, 'max_features': 'sqrt'}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:29:42,443] Trial 39 finished with value: 0.7809457937948784 and parameters: {'n_estimators': 400, 'max_depth': 20, 'min_samples_split': 10, 'min_samples_leaf': 4, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:29:47,510] Trial 40 finished with value: 0.757298867370354 and parameters: {'n_estimators': 537, 'max_depth': 11, 'min_samples_split': 6, 'min_samples_leaf': 1, 'max_features': 'log2'}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:30:08,494] Trial 41 finished with value: 0.7834417863552876 and parameters: {'n_estimators': 489, 'max_depth': 14, 'min_samples_split': 9, 'min_samples_leaf': 1, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:30:29,555] Trial 42 finished with value: 0.7833120916803503 and parameters: {'n_estimators': 470, 'max_depth': 16, 'min_samples_split': 7, 'min_samples_leaf': 1, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:30:53,278] Trial 43 finished with value: 0.7827218709753514 and parameters: {'n_estimators': 581, 'max_depth': 14, 'min_samples_split': 8, 'min_samples_leaf': 2, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:31:15,965] Trial 44 finished with value: 0.7833702090184669 and parameters: {'n_estimators': 543, 'max_depth': 13, 'min_samples_split': 10, 'min_samples_leaf': 1, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:31:34,603] Trial 45 finished with value: 0.7819971685764747 and parameters: {'n_estimators': 504, 'max_depth': 15, 'min_samples_split': 15, 'min_samples_leaf': 2, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:31:51,580] Trial 46 finished with value: 0.7755286340119399 and parameters: {'n_estimators': 621, 'max_depth': 17, 'min_samples_split': 4, 'min_samples_leaf': 9, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:32:08,036] Trial 47 finished with value: 0.7822866357544687 and parameters: {'n_estimators': 435, 'max_depth': 14, 'min_samples_split': 9, 'min_samples_leaf': 3, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:32:33,813] Trial 48 finished with value: 0.7832977801704184 and parameters: {'n_estimators': 568, 'max_depth': 12, 'min_samples_split': 5, 'min_samples_leaf': 1, 'max_features': None}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:32:38,431] Trial 49 finished with value: 0.7338240849240497 and parameters: {'n_estimators': 737, 'max_depth': 5, 'min_samples_split': 11, 'min_samples_leaf': 7, 'max_features': 'log2'}. Best is trial 10 with value: 0.7837435774945696.\n", "[I 2025-08-21 13:32:44,467] A new study created in memory with name: no-name-5aded8ad-0105-494f-96d3-c117669151e6\n", "[I 2025-08-21 13:32:48,277] Trial 0 finished with value: 0.7696369588859046 and parameters: {'n_estimators': 546, 'max_depth': 7, 'min_samples_split': 4, 'min_samples_leaf': 6, 'max_features': None}. Best is trial 0 with value: 0.7696369588859046.\n", "[I 2025-08-21 13:32:49,645] Trial 1 finished with value: 0.7367064533043765 and parameters: {'n_estimators': 425, 'max_depth': 18, 'min_samples_split': 9, 'min_samples_leaf': 6, 'max_features': 'log2'}. Best is trial 0 with value: 0.7696369588859046.\n", "[I 2025-08-21 13:32:51,945] Trial 2 finished with value: 0.7382260449863642 and parameters: {'n_estimators': 711, 'max_depth': 13, 'min_samples_split': 18, 'min_samples_leaf': 7, 'max_features': 'sqrt'}. Best is trial 0 with value: 0.7696369588859046.\n", "[I 2025-08-21 13:32:54,840] Trial 3 finished with value: 0.7557661303160774 and parameters: {'n_estimators': 790, 'max_depth': 13, 'min_samples_split': 12, 'min_samples_leaf': 3, 'max_features': 'sqrt'}. Best is trial 0 with value: 0.7696369588859046.\n", "[I 2025-08-21 13:32:56,216] Trial 4 finished with value: 0.7319512897074208 and parameters: {'n_estimators': 418, 'max_depth': 11, 'min_samples_split': 15, 'min_samples_leaf': 8, 'max_features': 'sqrt'}. Best is trial 0 with value: 0.7696369588859046.\n", "[I 2025-08-21 13:32:59,540] Trial 5 finished with value: 0.7605035616319464 and parameters: {'n_estimators': 502, 'max_depth': 6, 'min_samples_split': 6, 'min_samples_leaf': 1, 'max_features': None}. Best is trial 0 with value: 0.7696369588859046.\n", "[I 2025-08-21 13:33:02,246] Trial 6 finished with value: 0.7434415117384695 and parameters: {'n_estimators': 783, 'max_depth': 15, 'min_samples_split': 2, 'min_samples_leaf': 5, 'max_features': 'log2'}. Best is trial 0 with value: 0.7696369588859046.\n", "[I 2025-08-21 13:33:04,974] Trial 7 finished with value: 0.7287199666793203 and parameters: {'n_estimators': 602, 'max_depth': 4, 'min_samples_split': 6, 'min_samples_leaf': 10, 'max_features': None}. Best is trial 0 with value: 0.7696369588859046.\n", "[I 2025-08-21 13:33:08,156] Trial 8 finished with value: 0.754932862920602 and parameters: {'n_estimators': 779, 'max_depth': 17, 'min_samples_split': 6, 'min_samples_leaf': 3, 'max_features': 'log2'}. Best is trial 0 with value: 0.7696369588859046.\n", "[I 2025-08-21 13:33:14,274] Trial 9 finished with value: 0.7780868029154524 and parameters: {'n_estimators': 669, 'max_depth': 9, 'min_samples_split': 11, 'min_samples_leaf': 1, 'max_features': None}. Best is trial 9 with value: 0.7780868029154524.\n", "[I 2025-08-21 13:33:16,236] Trial 10 finished with value: 0.776680384714741 and parameters: {'n_estimators': 240, 'max_depth': 9, 'min_samples_split': 20, 'min_samples_leaf': 1, 'max_features': None}. Best is trial 9 with value: 0.7780868029154524.\n", "[I 2025-08-21 13:33:17,857] Trial 11 finished with value: 0.7759616614616487 and parameters: {'n_estimators': 197, 'max_depth': 9, 'min_samples_split': 20, 'min_samples_leaf': 1, 'max_features': None}. Best is trial 9 with value: 0.7780868029154524.\n", "[I 2025-08-21 13:33:19,104] Trial 12 finished with value: 0.7787905534811502 and parameters: {'n_estimators': 143, 'max_depth': 9, 'min_samples_split': 12, 'min_samples_leaf': 3, 'max_features': None}. Best is trial 12 with value: 0.7787905534811502.\n", "[I 2025-08-21 13:33:20,154] Trial 13 finished with value: 0.7793967518544213 and parameters: {'n_estimators': 103, 'max_depth': 10, 'min_samples_split': 12, 'min_samples_leaf': 3, 'max_features': None}. Best is trial 13 with value: 0.7793967518544213.\n", "[I 2025-08-21 13:33:21,060] Trial 14 finished with value: 0.7795096828609113 and parameters: {'n_estimators': 102, 'max_depth': 20, 'min_samples_split': 14, 'min_samples_leaf': 4, 'max_features': None}. Best is trial 14 with value: 0.7795096828609113.\n", "[I 2025-08-21 13:33:23,753] Trial 15 finished with value: 0.7802395393817235 and parameters: {'n_estimators': 303, 'max_depth': 20, 'min_samples_split': 15, 'min_samples_leaf': 4, 'max_features': None}. Best is trial 15 with value: 0.7802395393817235.\n", "[I 2025-08-21 13:33:26,402] Trial 16 finished with value: 0.7779369545634951 and parameters: {'n_estimators': 315, 'max_depth': 20, 'min_samples_split': 16, 'min_samples_leaf': 4, 'max_features': None}. Best is trial 15 with value: 0.7802395393817235.\n", "[I 2025-08-21 13:33:29,123] Trial 17 finished with value: 0.7771318346838616 and parameters: {'n_estimators': 319, 'max_depth': 20, 'min_samples_split': 15, 'min_samples_leaf': 5, 'max_features': None}. Best is trial 15 with value: 0.7802395393817235.\n", "[I 2025-08-21 13:33:30,104] Trial 18 finished with value: 0.7332072030131359 and parameters: {'n_estimators': 307, 'max_depth': 17, 'min_samples_split': 17, 'min_samples_leaf': 8, 'max_features': 'sqrt'}. Best is trial 15 with value: 0.7802395393817235.\n", "[I 2025-08-21 13:33:30,797] Trial 19 finished with value: 0.7464950392026969 and parameters: {'n_estimators': 184, 'max_depth': 15, 'min_samples_split': 14, 'min_samples_leaf': 4, 'max_features': 'log2'}. Best is trial 15 with value: 0.7802395393817235.\n", "[I 2025-08-21 13:33:34,625] Trial 20 finished with value: 0.7801219309580951 and parameters: {'n_estimators': 365, 'max_depth': 19, 'min_samples_split': 9, 'min_samples_leaf': 4, 'max_features': None}. Best is trial 15 with value: 0.7802395393817235.\n", "[I 2025-08-21 13:33:38,488] Trial 21 finished with value: 0.7800726559982505 and parameters: {'n_estimators': 367, 'max_depth': 19, 'min_samples_split': 9, 'min_samples_leaf': 4, 'max_features': None}. Best is trial 15 with value: 0.7802395393817235.\n", "[I 2025-08-21 13:33:42,949] Trial 22 finished with value: 0.7823342452222054 and parameters: {'n_estimators': 371, 'max_depth': 18, 'min_samples_split': 9, 'min_samples_leaf': 2, 'max_features': None}. Best is trial 22 with value: 0.7823342452222054.\n", "[I 2025-08-21 13:33:46,206] Trial 23 finished with value: 0.7813938213953289 and parameters: {'n_estimators': 262, 'max_depth': 15, 'min_samples_split': 9, 'min_samples_leaf': 2, 'max_features': None}. Best is trial 22 with value: 0.7823342452222054.\n", "[I 2025-08-21 13:33:49,412] Trial 24 finished with value: 0.7810980386306553 and parameters: {'n_estimators': 256, 'max_depth': 15, 'min_samples_split': 8, 'min_samples_leaf': 2, 'max_features': None}. Best is trial 22 with value: 0.7823342452222054.\n", "[I 2025-08-21 13:33:52,344] Trial 25 finished with value: 0.7810021548665571 and parameters: {'n_estimators': 241, 'max_depth': 15, 'min_samples_split': 8, 'min_samples_leaf': 2, 'max_features': None}. Best is trial 22 with value: 0.7823342452222054.\n", "[I 2025-08-21 13:33:55,372] Trial 26 finished with value: 0.7796361980652748 and parameters: {'n_estimators': 243, 'max_depth': 13, 'min_samples_split': 7, 'min_samples_leaf': 2, 'max_features': None}. Best is trial 22 with value: 0.7823342452222054.\n", "[I 2025-08-21 13:34:00,626] Trial 27 finished with value: 0.7822652986860064 and parameters: {'n_estimators': 465, 'max_depth': 16, 'min_samples_split': 10, 'min_samples_leaf': 2, 'max_features': None}. Best is trial 22 with value: 0.7823342452222054.\n", "[I 2025-08-21 13:34:02,704] Trial 28 finished with value: 0.7617054617886712 and parameters: {'n_estimators': 469, 'max_depth': 17, 'min_samples_split': 10, 'min_samples_leaf': 2, 'max_features': 'sqrt'}. Best is trial 22 with value: 0.7823342452222054.\n", "[I 2025-08-21 13:34:05,328] Trial 29 finished with value: 0.7618173211936107 and parameters: {'n_estimators': 557, 'max_depth': 16, 'min_samples_split': 4, 'min_samples_leaf': 2, 'max_features': 'log2'}. Best is trial 22 with value: 0.7823342452222054.\n", "[I 2025-08-21 13:34:08,706] Trial 30 finished with value: 0.7774081675345534 and parameters: {'n_estimators': 374, 'max_depth': 14, 'min_samples_split': 4, 'min_samples_leaf': 6, 'max_features': None}. Best is trial 22 with value: 0.7823342452222054.\n", "[I 2025-08-21 13:34:14,090] Trial 31 finished with value: 0.7822105183256934 and parameters: {'n_estimators': 470, 'max_depth': 16, 'min_samples_split': 10, 'min_samples_leaf': 2, 'max_features': None}. Best is trial 22 with value: 0.7823342452222054.\n", "[I 2025-08-21 13:34:19,869] Trial 32 finished with value: 0.7814482613731062 and parameters: {'n_estimators': 504, 'max_depth': 17, 'min_samples_split': 10, 'min_samples_leaf': 2, 'max_features': None}. Best is trial 22 with value: 0.7823342452222054.\n", "[I 2025-08-21 13:34:24,859] Trial 33 finished with value: 0.7810764357883366 and parameters: {'n_estimators': 482, 'max_depth': 18, 'min_samples_split': 11, 'min_samples_leaf': 3, 'max_features': None}. Best is trial 22 with value: 0.7823342452222054.\n", "[I 2025-08-21 13:34:31,361] Trial 34 finished with value: 0.7809362099290178 and parameters: {'n_estimators': 532, 'max_depth': 18, 'min_samples_split': 11, 'min_samples_leaf': 1, 'max_features': None}. Best is trial 22 with value: 0.7823342452222054.\n", "[I 2025-08-21 13:34:33,061] Trial 35 finished with value: 0.7566589068784876 and parameters: {'n_estimators': 435, 'max_depth': 16, 'min_samples_split': 13, 'min_samples_leaf': 3, 'max_features': 'sqrt'}. Best is trial 22 with value: 0.7823342452222054.\n", "[I 2025-08-21 13:34:39,319] Trial 36 finished with value: 0.7818793904605446 and parameters: {'n_estimators': 590, 'max_depth': 12, 'min_samples_split': 10, 'min_samples_leaf': 2, 'max_features': None}. Best is trial 22 with value: 0.7823342452222054.\n", "[I 2025-08-21 13:34:41,375] Trial 37 finished with value: 0.729319340398202 and parameters: {'n_estimators': 612, 'max_depth': 12, 'min_samples_split': 7, 'min_samples_leaf': 7, 'max_features': 'log2'}. Best is trial 22 with value: 0.7823342452222054.\n", "[I 2025-08-21 13:34:42,858] Trial 38 finished with value: 0.7460897767920585 and parameters: {'n_estimators': 397, 'max_depth': 12, 'min_samples_split': 10, 'min_samples_leaf': 5, 'max_features': 'sqrt'}. Best is trial 22 with value: 0.7823342452222054.\n", "[I 2025-08-21 13:34:49,120] Trial 39 finished with value: 0.780651912453412 and parameters: {'n_estimators': 597, 'max_depth': 13, 'min_samples_split': 13, 'min_samples_leaf': 1, 'max_features': None}. Best is trial 22 with value: 0.7823342452222054.\n", "[I 2025-08-21 13:34:52,454] Trial 40 finished with value: 0.7708886677581037 and parameters: {'n_estimators': 452, 'max_depth': 7, 'min_samples_split': 8, 'min_samples_leaf': 3, 'max_features': None}. Best is trial 22 with value: 0.7823342452222054.\n", "[I 2025-08-21 13:34:58,300] Trial 41 finished with value: 0.7814406308957921 and parameters: {'n_estimators': 516, 'max_depth': 17, 'min_samples_split': 10, 'min_samples_leaf': 2, 'max_features': None}. Best is trial 22 with value: 0.7823342452222054.\n", "[I 2025-08-21 13:35:05,364] Trial 42 finished with value: 0.7811390205571258 and parameters: {'n_estimators': 558, 'max_depth': 16, 'min_samples_split': 10, 'min_samples_leaf': 1, 'max_features': None}. Best is trial 22 with value: 0.7823342452222054.\n", "[I 2025-08-21 13:35:13,594] Trial 43 finished with value: 0.782585469551193 and parameters: {'n_estimators': 657, 'max_depth': 18, 'min_samples_split': 7, 'min_samples_leaf': 2, 'max_features': None}. Best is trial 43 with value: 0.782585469551193.\n", "[I 2025-08-21 13:35:24,794] Trial 44 finished with value: 0.7816645704451615 and parameters: {'n_estimators': 682, 'max_depth': 19, 'min_samples_split': 5, 'min_samples_leaf': 1, 'max_features': None}. Best is trial 43 with value: 0.782585469551193.\n", "[I 2025-08-21 13:35:33,614] Trial 45 finished with value: 0.7808225805529105 and parameters: {'n_estimators': 744, 'max_depth': 14, 'min_samples_split': 7, 'min_samples_leaf': 3, 'max_features': None}. Best is trial 43 with value: 0.782585469551193.\n", "[I 2025-08-21 13:35:38,228] Trial 46 finished with value: 0.7670451073944659 and parameters: {'n_estimators': 643, 'max_depth': 18, 'min_samples_split': 6, 'min_samples_leaf': 10, 'max_features': None}. Best is trial 43 with value: 0.782585469551193.\n", "[I 2025-08-21 13:35:40,745] Trial 47 finished with value: 0.7505036383627608 and parameters: {'n_estimators': 716, 'max_depth': 11, 'min_samples_split': 12, 'min_samples_leaf': 2, 'max_features': 'log2'}. Best is trial 43 with value: 0.782585469551193.\n", "[I 2025-08-21 13:35:42,875] Trial 48 finished with value: 0.7014635837219119 and parameters: {'n_estimators': 577, 'max_depth': 3, 'min_samples_split': 9, 'min_samples_leaf': 9, 'max_features': None}. Best is trial 43 with value: 0.782585469551193.\n", "[I 2025-08-21 13:35:50,777] Trial 49 finished with value: 0.7814545823228694 and parameters: {'n_estimators': 649, 'max_depth': 14, 'min_samples_split': 2, 'min_samples_leaf': 3, 'max_features': None}. Best is trial 43 with value: 0.782585469551193.\n"]}], "source": ["model_dict = train_all_models(X_train, y_train, n_trials=50)"]}, {"cell_type": "code", "execution_count": 33, "id": "772721d7", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'lgbm': (LGBMRegressor(bagging_fraction=0.7131684625939216, bagging_freq=3,\n", "                feature_fraction=0.6095353470016823,\n", "                lambda_l1=1.209461302982968e-05, lambda_l2=2.2410631828177343,\n", "                learning_rate=0.013792945999559478, max_depth=9,\n", "                min_child_samples=15, n_estimators=628, num_leaves=8,\n", "                random_state=42, subsample=0.7251106102217528, verbose=-1),\n", "  0.8082922707787293,\n", "  {'lambda_l1': 1.209461302982968e-05,\n", "   'lambda_l2': 2.2410631828177343,\n", "   'num_leaves': 8,\n", "   'feature_fraction': 0.6095353470016823,\n", "   'bagging_fraction': 0.7131684625939216,\n", "   'bagging_freq': 3,\n", "   'min_child_samples': 15,\n", "   'max_depth': 9,\n", "   'learning_rate': 0.013792945999559478,\n", "   'subsample': 0.7251106102217528,\n", "   'n_estimators': 628}),\n", " 'xgb': (XGBRegressor(alpha=0.005638471142719872, base_score=None, booster=None,\n", "               callbacks=None, colsample_bylevel=None, colsample_bynode=None,\n", "               colsample_bytree=0.5362903454506992, device=None,\n", "               early_stopping_rounds=None, enable_categorical=False,\n", "               eval_metric=None, feature_types=None, feature_weights=None,\n", "               gamma=None, grow_policy=None, importance_type=None,\n", "               interaction_constraints=None, lambda=7.646780137148901e-05,\n", "               learning_rate=0.015841755405201448, max_bin=None,\n", "               max_cat_threshold=None, max_cat_to_onehot=None,\n", "               max_delta_step=None, max_depth=3, max_leaves=None,\n", "               min_child_weight=17, missing=nan, monotone_constraints=None,\n", "               multi_strategy=None, n_estimators=899, ...),\n", "  0.8084298832353095,\n", "  {'lambda': 7.646780137148901e-05,\n", "   'alpha': 0.005638471142719872,\n", "   'max_depth': 3,\n", "   'learning_rate': 0.015841755405201448,\n", "   'n_estimators': 899,\n", "   'subsample': 0.6093711392223484,\n", "   'colsample_bytree': 0.5362903454506992,\n", "   'min_child_weight': 17}),\n", " 'catboost': (<catboost.core.CatBoostRegressor at 0x28cf09cd490>,\n", "  0.81860172974046,\n", "  {'iterations': 825,\n", "   'depth': 5,\n", "   'learning_rate': 0.041331774949073634,\n", "   'l2_leaf_reg': 4.372991302903244,\n", "   'bagging_temperature': 0.13122680144989896,\n", "   'border_count': 80,\n", "   'random_strength': 2.358827578904561}),\n", " 'rf': (RandomForestRegressor(max_depth=15, max_features=None, min_samples_split=6,\n", "                        n_estimators=525, random_state=42),\n", "  0.7837435774945696,\n", "  {'n_estimators': 525,\n", "   'max_depth': 15,\n", "   'min_samples_split': 6,\n", "   'min_samples_leaf': 1,\n", "   'max_features': None}),\n", " 'et': (ExtraTreesRegressor(max_depth=18, max_features=None, min_samples_leaf=2,\n", "                      min_samples_split=7, n_estimators=657, random_state=42),\n", "  0.782585469551193,\n", "  {'n_estimators': 657,\n", "   'max_depth': 18,\n", "   'min_samples_split': 7,\n", "   'min_samples_leaf': 2,\n", "   'max_features': None}),\n", " 'ensemble': <src.map_exploration_service.ensemble_model.EnsembleRegressor at 0x28cb82f8890>,\n", " 'stacking': <src.map_exploration_service.ensemble_model.StackingRegressor at 0x28cb82f8710>}"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["model_dict"]}, {"cell_type": "code", "execution_count": 34, "id": "ecf92c8f", "metadata": {}, "outputs": [], "source": ["from sklearn.metrics import mean_squared_error, r2_score"]}, {"cell_type": "code", "execution_count": 35, "id": "f8d458b9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["R2: 0.8908, MSE: 0.0093\n"]}], "source": ["r2 = r2_score(y_test, model_dict['stacking'].predict(X_test))\n", "mse = mean_squared_error(y_test, model_dict['stacking'].predict(X_test))\n", "print(f\"R2: {r2:.4f}, MSE: {mse:.4f}\")"]}, {"cell_type": "code", "execution_count": 36, "id": "c22a52df", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["R2: 0.8870, MSE: 0.0096\n"]}], "source": ["r2 = r2_score(y_test, model_dict['ensemble'].predict(X_test))\n", "mse = mean_squared_error(y_test, model_dict['ensemble'].predict(X_test))\n", "print(f\"R2: {r2:.4f}, MSE: {mse:.4f}\")"]}, {"cell_type": "code", "execution_count": 37, "id": "4d2ccea7", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'lgbm': (LGBMRegressor(bagging_fraction=0.7131684625939216, bagging_freq=3,\n", "                feature_fraction=0.6095353470016823,\n", "                lambda_l1=1.209461302982968e-05, lambda_l2=2.2410631828177343,\n", "                learning_rate=0.013792945999559478, max_depth=9,\n", "                min_child_samples=15, n_estimators=628, num_leaves=8,\n", "                random_state=42, subsample=0.7251106102217528, verbose=-1),\n", "  0.8082922707787293,\n", "  {'lambda_l1': 1.209461302982968e-05,\n", "   'lambda_l2': 2.2410631828177343,\n", "   'num_leaves': 8,\n", "   'feature_fraction': 0.6095353470016823,\n", "   'bagging_fraction': 0.7131684625939216,\n", "   'bagging_freq': 3,\n", "   'min_child_samples': 15,\n", "   'max_depth': 9,\n", "   'learning_rate': 0.013792945999559478,\n", "   'subsample': 0.7251106102217528,\n", "   'n_estimators': 628}),\n", " 'xgb': (XGBRegressor(alpha=0.005638471142719872, base_score=None, booster=None,\n", "               callbacks=None, colsample_bylevel=None, colsample_bynode=None,\n", "               colsample_bytree=0.5362903454506992, device=None,\n", "               early_stopping_rounds=None, enable_categorical=False,\n", "               eval_metric=None, feature_types=None, feature_weights=None,\n", "               gamma=None, grow_policy=None, importance_type=None,\n", "               interaction_constraints=None, lambda=7.646780137148901e-05,\n", "               learning_rate=0.015841755405201448, max_bin=None,\n", "               max_cat_threshold=None, max_cat_to_onehot=None,\n", "               max_delta_step=None, max_depth=3, max_leaves=None,\n", "               min_child_weight=17, missing=nan, monotone_constraints=None,\n", "               multi_strategy=None, n_estimators=899, ...),\n", "  0.8084298832353095,\n", "  {'lambda': 7.646780137148901e-05,\n", "   'alpha': 0.005638471142719872,\n", "   'max_depth': 3,\n", "   'learning_rate': 0.015841755405201448,\n", "   'n_estimators': 899,\n", "   'subsample': 0.6093711392223484,\n", "   'colsample_bytree': 0.5362903454506992,\n", "   'min_child_weight': 17}),\n", " 'catboost': (<catboost.core.CatBoostRegressor at 0x28cf09cd490>,\n", "  0.81860172974046,\n", "  {'iterations': 825,\n", "   'depth': 5,\n", "   'learning_rate': 0.041331774949073634,\n", "   'l2_leaf_reg': 4.372991302903244,\n", "   'bagging_temperature': 0.13122680144989896,\n", "   'border_count': 80,\n", "   'random_strength': 2.358827578904561}),\n", " 'rf': (RandomForestRegressor(max_depth=15, max_features=None, min_samples_split=6,\n", "                        n_estimators=525, random_state=42),\n", "  0.7837435774945696,\n", "  {'n_estimators': 525,\n", "   'max_depth': 15,\n", "   'min_samples_split': 6,\n", "   'min_samples_leaf': 1,\n", "   'max_features': None}),\n", " 'et': (ExtraTreesRegressor(max_depth=18, max_features=None, min_samples_leaf=2,\n", "                      min_samples_split=7, n_estimators=657, random_state=42),\n", "  0.782585469551193,\n", "  {'n_estimators': 657,\n", "   'max_depth': 18,\n", "   'min_samples_split': 7,\n", "   'min_samples_leaf': 2,\n", "   'max_features': None}),\n", " 'ensemble': <src.map_exploration_service.ensemble_model.EnsembleRegressor at 0x28cb82f8890>,\n", " 'stacking': <src.map_exploration_service.ensemble_model.StackingRegressor at 0x28cb82f8710>}"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["model_dict"]}, {"cell_type": "code", "execution_count": null, "id": "2f7956db", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "autogluon", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}