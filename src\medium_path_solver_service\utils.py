from ..game.game_state_medium_path import GameState
from ..medium_path_solver_service.config import MediumPathSolverConfig, ShortestPathSolverConfig
import copy
from tqdm import tqdm
from datetime import datetime
import os
import json


class MediumPathSolverUtils:
    def __init__(self, config: MediumPathSolverConfig):
        self.config = config
        os.makedirs(self.config.save_log_path, exist_ok=True)

    @staticmethod
    def _save_solution_images(
        init_state: GameState, path, config: MediumPathSolverConfig
    ) -> None:
        """Save solution as sequence of images."""
        path = clean_path(path)
        try:
            current_state = copy.deepcopy(init_state)
            iter_count = 0

            # Save initial state
            current_state.visualize_state(
                saved_image=True,
                index_image=iter_count,
                save_path=config.save_image_path + "/" + str(init_state.level),
            )
            iter_count += 1

            block_id = -1
            # Execute each step and save image
            for step in tqdm(path, desc="Generating solution images"):
                if step[0] == "eat":
                    block = current_state.find_block(step[1])
                    if block:
                        current_state.eat_block(block)
                elif len(step) == 3:  # Move operation (block_id, dx, dy)
                    block_id, dx, dy = step
                    block = current_state.find_block(block_id)
                    if block:
                        current_state.move_block(block, dx, dy)

                current_state.visualize_state(
                    saved_image=True,
                    index_image=iter_count,
                    save_path=config.save_image_path + "/" + str(init_state.level),
                    block_id=block_id,
                )
                iter_count += 1

            print(
                f"📸 Saved {iter_count} solution images to {current_state.level_path}"
            )

        except Exception as e:
            print(f"Error saving solution images: {e}")

    @staticmethod
    def save_logs(file, path, stats, found, config: MediumPathSolverConfig) -> None:
        os.makedirs(config.save_log_path, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        start_str = stats["start_time"].strftime("%d%m_%H%M%S")
        end_str = stats["end_time"].strftime("%d%m_%H%M%S")

        log_filename = os.path.join(
            config.save_log_path,
            f"{os.path.basename(file)}_{start_str}_{end_str}_result.json",
        )

        log_data = {
            "file": file,
            "timestamp": timestamp,
            "status": "FOUND" if found else "NOT FOUND",
            "steps": len(path) if path else 0,
            "runtime_sec": str(stats["end_time"] - stats["start_time"]),
            "iterations": stats["iteration"],
            "cache_size": stats["cache_size"],
            "shuffle_events": stats.get("shuffle_events", []),
            "path": path if found else [],
        }

        with open(log_filename, "w", encoding="utf-8") as f:
            json.dump(log_data, f, indent=2, ensure_ascii=False)

        print(f"📜 Path log saved: {log_filename}")

def clean_path(path_sequence):
    cleaned_path = []
    last_block_id = None
    total_dx, total_dy = 0, 0

    for step in path_sequence:
        if len(step) > 0 and step[0] == "eat":
            if last_block_id is not None and (total_dx != 0 or total_dy != 0):
                cleaned_path.append((last_block_id, total_dx, total_dy))

            # Reset state và thêm eat command
            last_block_id = None
            total_dx, total_dy = 0, 0
            cleaned_path.append(step)

        elif len(step) >= 3:
            block_id, dx, dy = step[0], step[1], step[2]

            if last_block_id is None:
                last_block_id = block_id
                total_dx, total_dy = dx, dy
            elif block_id == last_block_id:
                total_dx += dx
                total_dy += dy
            else:
                if total_dx != 0 or total_dy != 0:
                    cleaned_path.append((last_block_id, total_dx, total_dy))
                last_block_id = block_id
                total_dx, total_dy = dx, dy

    if last_block_id is not None and (total_dx != 0 or total_dy != 0):
        cleaned_path.append((last_block_id, total_dx, total_dy))

    return cleaned_path



def save_logs(file, path, start_time, end_time, iteration, found, config: ShortestPathSolverConfig) -> None:
        os.makedirs(config.save_log_path, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        start_str = start_time.strftime("%d%m_%H%M%S")
        end_str = end_time.strftime("%d%m_%H%M%S")

        log_filename = os.path.join(
            config.save_log_path,
            f"{os.path.basename(file)}_{start_str}_{end_str}_result.json",
        )

        log_data = {
            "file": file,
            "timestamp": timestamp,
            "status": "FOUND" if found else "NOT FOUND",
            "steps": len(path) if path else 0,
            "runtime_sec": str(end_time - start_time),
            "iterations": iteration,
            "path": path if found else [],
        }

        with open(log_filename, "w", encoding="utf-8") as f:
            json.dump(log_data, f, indent=2, ensure_ascii=False)

        print(f"📜 Path log saved: {log_filename}")

def save_images(game: GameState, path, config:ShortestPathSolverConfig):
    os.makedirs(config.save_image_path, exist_ok=True)
    """Tối ưu với early return và safety checks"""
    if path is None:
        print("Too much iteration bro. I'm out!")
        return
        
    if not path:
        print("Empty path provided!")
        return

    saved_game = game.copy()
    iter_count = 0
    
    # Save initial state
    saved_game.visualize_state(
        saved_image=True, 
        index_image=iter_count, 
        save_path=f"{config.save_image_path}/{game.level}.png"
    )
    iter_count += 1
    
    # Filter valid actions using list comprehension
    valid_actions = [action for action in path if len(action) >= 2]
    invalid_actions = [action for action in path if len(action) < 2]
    
    if invalid_actions:
        print(f"⚠️ Skipping {len(invalid_actions)} invalid actions: {invalid_actions[:3]}...")
    
    for action in tqdm(valid_actions, desc="Processing moves"):
        try:
            if action[0] == 'eat':
                block_to_eat = saved_game.find_block(action[1])
                if not block_to_eat:
                    print(f"⚠️ Block {action[1]} not found for eating")
                    continue
                saved_game.eat_block(block_to_eat)
                
            # Handle move action
            else:
                if len(action) < 3:
                    print(f"⚠️ Invalid move action: {action}")
                    continue
                    
                block_to_move = saved_game.find_block(action[0])
                if not block_to_move:
                    print(f"⚠️ Block {action[0]} not found for moving")
                    continue
                    
                saved_game.move_block(block_to_move, action[1], action[2])
                        
            saved_game.visualize_state(
                saved_image=True, 
                index_image=iter_count, 
                block_id=action[0], 
                save_path=f"{config.save_image_path}/{game.level}.png"
            )
            iter_count += 1
            
        except Exception as e:
            print(f"❌ Error processing action {action}: {e}")
            continue
    
    print(f"✅ Saved {iter_count} states for level {game.level}")