services:
  backend:
    build:
      context: .
      dockerfile: deploy/Dockerfile.backend
    container_name: "slidejam-backend-${DEPLOY_MODE}"
    env_file:
      - .env
    ports:
      - "${BACKEND_PORT}:${BACKEND_PORT}"
    restart: unless-stopped

  frontend:
    build:
      context: .
      dockerfile: deploy/Dockerfile.frontend
    container_name: "slidejam-frontend-${DEPLOY_MODE}"
    env_file:
      - .env
    ports:
      - "${STREAMLIT_PORT}:${STREAMLIT_PORT}"
    depends_on:
      - backend
    restart: unless-stopped
